repos:
  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.11.3
    hooks:
      - id: ruff
        args: [--fix]
        stages: [pre-commit]

      - id: ruff-format
        stages: [pre-commit]

  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.6.0
    hooks:
      - id: trailing-whitespace
        stages: [pre-commit]

      - id: end-of-file-fixer
        stages: [pre-commit]

  - repo: https://gitlab.com/bmares/check-json5
    rev: v1.0.0
    hooks:
      - id: check-json5
        name: vscode settings files
        stages: [pre-commit]

  - repo: local
    hooks:
      - id: tests
        name: unit tests
        entry: poetry run pytest -s tests/unit
        language: system
        pass_filenames: false
        always_run: true
