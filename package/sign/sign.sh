#!/bin/bash
# Sign a file using jsign and a preinstalled key store (e.g. usb device).
# Intended to be run from CI/CD on a specially prepared machine.

TARGET_FILE=$1

set -e
PROVIDER_FILE_PATH="/tmp/provider-$CI_COMMIT_SHA.cfg"
cp $(dirname "$0")/provider.cfg $PROVIDER_FILE_PATH

echo "Starting jsign for $TARGET_FILE"

HASH_BEFORE=($(sha256sum $TARGET_FILE))

jsign --keystore "$PROVIDER_FILE_PATH" \
      --alias "$EV_CERT_ALIAS" \
      --storetype PKCS11 \
      --storepass $EV_CERT_PIN \
      --tsaurl "http://time.certum.pl" \
      --alg SHA-256 \
      --tsmode RFC3161 \
      "$TARGET_FILE"

echo "jsign finished with code $?"
rm $PROVIDER_FILE_PATH

HASH_AFTER=($(sha256sum $TARGET_FILE))
# jsign does not return a non-zero exit code on failures.
# assume jsign succeeded only if it actually changed the file.
if [ "$HASH_BEFORE" == "$HASH_AFTER" ]; then
      echo "jsign didn't not change the file, failing job"
      exit 1
fi
