#!/bin/bash

set -x

PLATFORM=$1

if [ "$PLATFORM" == "win" ]; then
    SETUP_VERSION="setup_version_win"
    BIN_PATH="./dist/scrape.exe"
elif [ "$PLATFORM" == "linux" ] || [ "$PLATFORM" == "macos" ]; then
    SETUP_VERSION="setup_version"
    BIN_PATH="./dist/scrape"
else
    echo "Error: Unsupported platform. Please specify 'win', 'linux', or 'macos'."
    exit 1
fi

poetry install --no-root --only main
poetry run poe $SETUP_VERSION
poetry run poe build_bin

$BIN_PATH --version
