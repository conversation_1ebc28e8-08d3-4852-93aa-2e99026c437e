import json
import os
import sys

sys.path.append(os.path.join(os.path.dirname(__file__), ".."))
from app.core.source import Source  # pylint: disable=wrong-import-position
from app.main import main  # pylint: disable=wrong-import-position


def run(cmd: str, source: str):
    env_vars = load_env_vars()
    args = get_args(cmd, source, env_vars)

    return main(args, standalone=False)


def load_env_vars() -> dict[str, str]:
    required_vars = ["DATA_DIR", "SCRAPE_FROM_DATE", "SCRAPE_TO_DATE"]
    env_vars: dict[str, str] = {}
    for var in required_vars:
        value = os.getenv(var)
        if not value:
            raise OSError(f"{var} is not set in .env file.")
        env_vars[var] = value
    optional_vars = ["TELEMETRY_PARAMS", "SCRAPER_API_URL", "INDIEBI_JWT"]
    for var in optional_vars:
        env_vars[var] = os.getenv(var, "")
    return env_vars


def get_args(cmd: str, source: str, env_vars) -> list[str]:
    source_uppercase = source.upper()
    credentials_var_name = f"{source_uppercase}_CREDENTIALS"
    credentials = os.getenv(credentials_var_name)
    if not credentials:
        raise OSError(
            f"Credentials for {source} are not defined in .env file. "
            + f"Please set {credentials_var_name} variable."
        )

    data_dir_path = os.path.join(os.getcwd(), env_vars["DATA_DIR"])
    session_file = os.path.join(data_dir_path, "sessions", f"{source}.json")
    report_path = os.path.join(data_dir_path, "reports", source)
    dump_path = os.path.join(data_dir_path, "dump_dir", source)

    os.makedirs(os.path.join(data_dir_path, "sessions"), exist_ok=True)
    os.makedirs(report_path, exist_ok=True)
    os.makedirs(dump_path, exist_ok=True)

    command_specific_args = {
        "scrape": {
            "scrape": "",
            "--from": env_vars["SCRAPE_FROM_DATE"],
            "--to": env_vars["SCRAPE_TO_DATE"],
            "--reportPath": report_path,
        },
        "login": {"login": ""},
        "check-session": {"check-session": ""},
    }

    args_dict = {
        **command_specific_args[cmd],
        "--source": source,
        "--output": "verbose-colored",
        "--sessionFile": session_file,
        "--headless": "false",
        "--credentials": credentials,
        "--featureFlags": json.dumps(["full-history-dump"]),
        "--dumpDir": dump_path,
        "--apiUrl": env_vars["SCRAPER_API_URL"],
        "--apiToken": env_vars["INDIEBI_JWT"],
    }

    args = [item for pair in args_dict.items() for item in pair if item]

    log_args = [
        f"{key} {'*' * 16 if key == '--credentials' else value}"
        for key, value in args_dict.items()
    ]
    log_string = "python -m app.main " + " ".join(log_args)

    print(f"Running:\n\n{log_string}\n")
    return args


def generate_session_file_for_nintendo_discounts():
    os.system("bash ./scripts/login_to_nintendo_via_js_repo.sh")


SUPPORTED_COMMANDS = ["scrape", "login", "check-session"]

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print(f"Usage: python run.py <{'|'.join(SUPPORTED_COMMANDS)}> <source>")
        sys.exit(1)

    cmd = sys.argv[1]
    source = sys.argv[2]

    try:
        if source not in Source.values():
            raise ValueError(
                f"Unsupported source: '{source}'. Must be one of: {','.join(Source.values())}"
            )

        if cmd not in SUPPORTED_COMMANDS:
            raise ValueError(
                f"Unsupported command: '{cmd}'. Must be one of: {', '.join(SUPPORTED_COMMANDS)}"
            )

        try:
            sys.exit(run(cmd, source))

        except NotImplementedError as e:
            if cmd == "login" and source == Source.NINTENDO_DISCOUNTS:
                sys.exit(generate_session_file_for_nintendo_discounts())
            raise e

    except Exception as e:  # pylint: disable=broad-except
        print(e)
        pass
