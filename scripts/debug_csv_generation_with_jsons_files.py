"""
In case Nintendo Discount CSV file generation fails, json files are still added to the report.
Those files can be used to debug the issue, and to try to generate the CSV file locally.
This script can help with that process.
"""

import os
import sys

sys.path.append(os.path.join(os.path.dirname(__file__), ".."))

import json
import logging
from pathlib import Path

from app.core.types import JSON, DictJSON
from app.nintendo.converters import to_csv
from app.nintendo.types import NsUid, SubmissionId

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def load_json_file(file_path: Path):
    """Load and parse JSON file."""
    with open(file_path, encoding="utf-8") as f:
        return json.load(f)


def reconstruct_discount_groups(data: list) -> dict:
    """Reconstruct discount_groups dictionary from flat list."""
    # Assuming each sublist in data corresponds to a submission_id
    return {f"submission_{idx}": group for idx, group in enumerate(data)}


def get_nsuid_from_price_data(price_data) -> str:
    """Extract correct nsUid from price data based on LIVE dataSource."""
    online_prices = price_data.get("onlinePrices", [])
    for price in online_prices:
        if price.get("dataSource") == "LIVE":
            return str(price.get("nsUid"))
    # Fallback to first nsUid if no LIVE dataSource found
    return str(online_prices[0].get("nsUid")) if online_prices else "0"


def debug_csv_conversion(data_dir: Path) -> None:
    """
    Load JSON files and attempt CSV conversion with debug capabilities.

    Args:
        data_dir: Directory containing the JSON files
    """
    try:
        # Load all JSON files
        prices_data = load_json_file(data_dir / "prices.json")
        content_lists_data = load_json_file(data_dir / "content_lists.json")
        discount_groups_data = load_json_file(data_dir / "discount_groups.json")
        country_currency_map = load_json_file(
            data_dir / "country_currency_mapping.json"
        )

        # Reconstruct the original data structures, maintaining order as per function signature
        discount_groups: dict[SubmissionId, list[JSON]] = {
            SubmissionId(idx): group for idx, group in enumerate(discount_groups_data)
        }

        prices: dict[NsUid, JSON] = {
            NsUid(get_nsuid_from_price_data(price_data)): price_data
            for price_data in prices_data
        }

        sale_names: dict[SubmissionId, str] = {
            SubmissionId(idx): f"Sale {idx}" for idx in range(len(content_lists_data))
        }

        content_lists: dict[SubmissionId, DictJSON] = {
            SubmissionId(idx): content for idx, content in enumerate(content_lists_data)
        }

        logger.info("Data loaded successfully. Attempting CSV conversion...")

        # Here you can set a breakpoint before the conversion
        result = to_csv(
            discount_groups,  # raw_discount_group_responses_per_submission_id
            prices,  # raw_online_price_responses_per_nsuid
            sale_names,  # sale_names_per_submission_id
            content_lists,  # content_list_per_submission_id
            country_currency_map,  # country_currency_mapping
        )

        # Save the result
        output_path = data_dir / "debug_output.csv"
        with open(output_path, "w", encoding="utf-8") as f:
            f.write(str(result))

        logger.info(f"CSV conversion successful! Output saved to {output_path}")

    except Exception as e:
        logger.error(f"Error during debug process: {str(e)}", exc_info=True)
        # Here you can set another breakpoint to inspect the error state
        raise


if __name__ == "__main__":
    # Specify the directory containing your JSON files
    data_directory = Path("/Users/<USER>/Desktop/untold")
    debug_csv_conversion(data_directory)
