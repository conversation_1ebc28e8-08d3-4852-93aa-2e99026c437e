import os

import click
from cryptography.fernet import Fernet


@click.command
@click.option("--encrypt", "mode", flag_value="encrypt", default=True)
@click.option("--decrypt", "mode", flag_value="decrypt")
def main(mode):
    if mode == "encrypt":
        encrypt()
    else:
        decrypt()


def encrypt():
    with (
        open("tests/credentials.json", "rb") as inf,
        open("tests/credentials_encrypted.json", "wb") as outf,
    ):
        payload = inf.read()
        f = Fernet(os.environ["SCP_CREDENTIALS_ENCRYPTION_KEY"])
        outf.write(f.encrypt(payload))


def decrypt():
    with (
        open("tests/credentials_encrypted.json", "rb") as inf,
        open("tests/credentials.json", "wb") as outf,
    ):
        payload = inf.read()
        f = Fernet(os.environ["SCP_CREDENTIALS_ENCRYPTION_KEY"])
        outf.write(f.decrypt(payload))


if __name__ == "__main__":
    main()  # pylint: disable=no-value-for-parameter
