<!--- docs
# Metadata used by our doc generator
title: <PERSON><PERSON><PERSON> (Py)
group: scrapers
-->

# <PERSON><PERSON><PERSON> (Python)

Collection of Python-based scrapers.

## Dev Quickstart

### First-time setup

1. Install Python 3.11

   1.1. Ubuntu

      ```bash
      sudo apt update
      sudo apt install software-properties-common
      sudo add-apt-repository ppa:deadsnakes/ppa
      sudo apt update
      sudo apt install python3.11
      ```
   1.2. MacOS

      ```bash
      brew install conda
      conda create -n py311 python=3.11
      conda activate py311
      ```


1. Install Poetry

   2.1. Ubuntu

      ```bash
      curl -sSL https://install.python-poetry.org | python3 -
      ```

   2.2. <PERSON><PERSON>
      ```bash
      conda install poetry
      poetry env use `which python`
      ```

3. Configure GitLab token for VCR

   ```
   poetry config http-basic.vcr <username> <token>
   ```

   where `<username>` is your GitLab username and `<token>` is your GitLab token. You can find your token [here](https://gitlab.com/-/profile/personal_access_tokens). You need to select `api` scope and set an expiration date.


1. In repo directory, run `poetry install`

   ```
   poetry install
   ```

1. Install pre-commit hooks:
   ```bash
   poetry run pre-commit install
   ```

1. Add `SCP_CREDENTIALS_ENCRYPTION_KEY` to your `.env` file. You can find it in zoho vault as `ScraperLib Encryption key`. You might need to export it as an environment variable or source it from a file (execute `source .env` command).

### Running the app

```bash
poetry shell
python ./scripts/run.py login epic_sales
# OR
python -m app.main login --source=epic_sales --sessionFile=session.json ...
# OR
poe cli login --source=epic_sales --sessionFile=session.json ...
```

or without creating a separate shell:

```bash
$ poetry run python -m app.main login --source=epic_sales --sessionFile=session.json ...
```

Whole interface should be the same as described [here](https://gitlab.com/bluebrick/indiebi/scrapers/-/blob/master/docs/design/launchInterface.md). If you noticed some differences it's probably a bug ;)

### Running scripts

We are using `poethepoet` to manage common scripts in the project. To run script defined in `[tool.poe.tasks]` section in `pyproject.toml` you should use `poe` command in poetry shell

For example:

```bash
$ poetry shell
$ poe build_bin
```


### Linting

We use `ruff` to keep consistent code style. To run linter, use:

```bash
poe lint
```

If you want to make `poe` available from your main shell, you can add alias: `alias poe="poetry run poe"` in `.bashrc`/`.zshrc`

## Before running tests

1. Go to the https://gitlab.com/-/profile/personal_access_tokens
1. Generate a new token (select only `api`, and set an expiration date)
1. Set up a generated token in your `.env` file or in your shell; e.g. as a value of the `GITLAB_PAT` variable
1. Install the required VCR library using the command: `poetry run pip install scrapers-vcr --index-url https://__token__:${GITLAB_PersonalAccessToken}@gitlab.com/api/v4/projects/45807331/packages/pypi/simple`

### Running tests

```bash
$ poetry shell
$ pytest -s # or `poe test` to run everything at once (not recommended)
$ pytest -s test/dir_name # to run tests from specific directory, f.e. `unit`
```

Also possible to run specific test type:

```bash
$ poe test_unit
$ poe test_integration
$ poe test_vcr
```

### Run single test

```
$ pytest -s -k test_name
```

or

```
$ pytest -s test/dir_name
```

or

```
$ pytest -s test/test_specific_file.py
```

### Adding telemetry
We use AppInsights as a Telemetry backend. Standard system logger is configured to send traces to Azure with a known set of custom dimensions. We also manualy set some of Azure standard dimensions to specific values.

By default, a log statement (info, warn, error) will be sent to traces. If you wish to send to events instead you can use the helper function:
```python
from logging import event_only
...
log.info("Sample statement", extra=event_only())
```

If you wish to add custom dimensions to your log statement, you can use:
```python
log.warn("Something is not quite right", extra=as_custom_dimensions({"second_run_today": "True"}))
```

Helper functions can be combined, i.e. you can do:
```python
log.error("Something went wrong!!!", extra=event_only(as_custom_dimensions({"api_version_used": "4.5.6"})))
```

Exceptions are logged to exceptions and customEvents by default.
If you wish to log a stack trace, remember to set the exc_info flag in the statement. This only works inside the except block.
```python
try:
   # something goes wrong
   raise Exception()
except:
   log.exception("Uh-oh", exc_info=True)
```

## Short _how to_ write tests in Python

### Create new test file(s)

1. Create a new file in the `test` directory (or in any `test` subdirectory).
1. The file name has to look like `test_*.py` - the asterisk is your invention.
1. Remember to start your test function name from `test_`. :exclamation:

### Simple test

```python
from one.of.my.files import function_which_i_want_to_test

def test_ANYTHING_WHAT_IS_CLEAR():
   assert function_which_i_want_to_test(msg='example_input') == 'example_result'
```

### Using one variable in more than one test

```python
import pytest
from one.of.my.files import function_which_i_want_to_test, second_function

@pytest.fixture
def name_of_variable():
   return "This text will be use as a value."

def test_ANYTHING_WHAT_IS_CLEAR(name_of_variable):
   assert function_which_i_want_to_test(msg=name_of_variable) == 'example_result'

def test_ANYTHING_WHAT_IS_CLEAR_2(name_of_variable):
   example_result_2 = 666
   assert second_function(msg=name_of_variable) == example_result_2
```

### Using multiple cases in one test

```python
import pytest
from one.of.my.files import function_which_i_want_to_test

@pytest.mark.parametrize(
   'input_var, output_var',
   [
      ('text-1', 'expected-result-1'),
      ('Any_other-Text 2', 'expected-result-2')
   ]
)
def test_ANYTHING_WHAT_IS_CLEAR(input_var, output_var):
   assert function_which_i_want_to_test(msg=input_var) == output_var
```

### Cleaning tests

1. We can use the `conftest.py` file to move fixtures there.
1. Access to the `conftest.py` file, have files in the same directory and files in subdirectories.

## Why Python?

** Python allows for faster development of better code. **

Why?

- saner language and std lib
- less dependency hell
- runtime access to type hints
- opt-in async (scrapers are mostly single-threaded anyway)
- awesome libraries (requests, pydantic, poetry)

Bonus points:

- potentially smaller binary sizes
- potentially faster than node when executed as binary (?)
- better skill coherence with backend stuff

## Interesting stuff to talk about

- Running
- Poetry + pyproject.toml
- Dependency Injection for Scrapers
- VCR Tests
- CLI arguments
- Pydantic
- Pytest

## Developers hints

To change the python version on Ubuntu, you can install it and then use `update-alternatives`:

```bash
sudo update-alternatives --config python3
```

And manually select proper version (3.11).

## Info about MacOS runner

SSH config you need add to your `~/.ssh/config` file:

```
Host bastion-indiebi
  HostName *************
  Port 60022
  User indiebi_admin

Host to-macmini
  HostName **********
  Port 22
  User lokal
  ProxyJump bastion-indiebi
```

If you need to do anything with MacOS runner (or pipeline stuck on the MacOS job) do following steps:


1. Connect to the VPN
1. Log in via SSH to the MacOS server using `ssh to-macmini`
1. Input password which you can find in Zoho: **Apple Mac mini PY GL runner**
1. That should be fine to unblock the pipeline

### Pycharm

when running Pycharm do not put additional quotiations in embeded jsons in the configuration edit modal

` '{"this": "is invalid"}'` is invalid

` {"this": "is valid"}` is valid


### Debugging

For VS Code launch configurations has been prepared for you.

![](./docs/images/debug_vscode.png)

To use them, you would first need to set a few variables in `.env` file.

```bash
DATA_DIR='.private'
SCRAPE_FROM_DATE=2023-01-14
SCRAPE_TO_DATE=2023-02-23

APP_STORE_SALES_CREDENTIALS='{"user":"<user>","password":"<password>"}'
EPIC_SALES_CREDENTIALS='{"user":"<user>","password":"<password>"}'
GOOGLE_SALES_CREDENTIALS='{"user":"<user>","password":"<password>"}'
MICROSOFT_SALES_CREDENTIALS='{"user":"<user>","password":"<password>"}'
NINTENDO_DISCOUNTS_CREDENTIALS='{"user":"<user>","password":"<password>"}'
```

Copy `.env.example` to `.env` and fill it with proper values.
