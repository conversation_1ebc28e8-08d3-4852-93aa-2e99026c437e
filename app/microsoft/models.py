from datetime import date, datetime, timedelta

from pydantic import BaseModel, ConfigDict, RootModel, alias_generators

from app.core.sessions import Session
from app.util.camel_case_model import CamelCaseModel

DAYS_TO_EXPIRE_APPS_CACHE = (
    7  # change back to 3 days after MS will solve its issue with reports
)


class APISetupData(CamelCaseModel):
    company_name: str
    client_id: str
    client_secret: str
    tenant_id: str


class OptionalAPISetupData(CamelCaseModel):
    should_download: bool = (
        True  # backward compatibility with previous login/password login
    )
    company_name: str
    client_id: str | None = (
        None  # backward compatibility with previous login/password login
    )
    client_secret: str | None = None  # as above
    tenant_id: str | None = None  # as above

    def to_required(self) -> APISetupData:
        if not self.should_download:
            raise Exception(
                "API %s is not marked as `should download` and can't be used",
                self.should_download,
            )
        if self.client_id is None:
            raise Exception(
                "Client Id is required when `should download` is set",
                self.should_download,
            )
        elif self.client_secret is None:
            raise Exception(
                "Client Secret is required when `should download` is set",
                self.should_download,
            )
        elif self.tenant_id is None:
            raise Exception(
                "Tenant Id is required when `should download` is set",
                self.should_download,
            )
        else:
            return APISetupData(
                company_name=self.company_name,
                client_id=self.client_id,
                client_secret=self.client_secret,
                tenant_id=self.tenant_id,
            )


class MicrosoftCredentials(CamelCaseModel):
    api_setup_data: list[OptionalAPISetupData] = []

    def api_to_scrape(self) -> list[APISetupData]:
        return [api.to_required() for api in self.api_setup_data if api.should_download]


class MicrosoftFileMetaData(CamelCaseModel):
    human_name: str
    sku_id: str
    parent_sku_id: str


class MSApplication(BaseModel):
    product_id: str
    title_name: str
    parent_product_id: str
    parent_product_name: str
    in_app: bool

    def to_metadata(self) -> MicrosoftFileMetaData:
        return MicrosoftFileMetaData(
            human_name=self.title_name,
            sku_id=self.product_id,
            parent_sku_id=self.parent_product_id,
        )


class MicrosoftSession(Session, CamelCaseModel):
    api_setup_data: list[
        APISetupData
    ] = []  # TODO: remove default value after separating code that requires session from one that requires login only

    @property
    def is_expired(self) -> bool:
        return len(self.api_setup_data) == 0


class Report(BaseModel):
    model_config = ConfigDict(
        alias_generator=alias_generators.to_camel,
        populate_by_name=True,
    )

    report_id: str
    report_name: str
    query_id: str
    created_time: datetime
    start_time: datetime
    end_time: datetime
    report_status: str

    @property
    def is_expired(self) -> bool:
        return (
            self.start_time + timedelta(days=DAYS_TO_EXPIRE_APPS_CACHE)
        ).date() <= date.today()

    @property
    def if_from_today(self) -> bool:
        return self.start_time.date() == date.today()


class Reports(RootModel):
    root: list[Report]

    @property
    def not_expired(self) -> "Reports":
        return Reports(root=[report for report in self.root if not report.is_expired])

    @property
    def has_report_from_today(self) -> bool:
        return any(report.if_from_today for report in self.root)
