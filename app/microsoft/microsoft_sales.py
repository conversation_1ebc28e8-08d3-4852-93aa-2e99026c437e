import csv
import json
import logging
from collections.abc import Sequence
from datetime import date
from operator import itemgetter
from time import sleep
from typing import Any

import httpx
from pydantic import BaseModel

from app.core.exceptions import (
    InvalidCredentialsException,
    MissingPermissionsException,
    SessionExpiredException,
    TemporaryApiIssueException,
)
from app.core.scraper import (
    CSSSelector,
    ManualLoginDetails,
    Organization,
    ReportZip,
    ScrapeInfo,
    Scraper,
    SessionIdentifier,
)
from app.core.source import Source
from app.core.types import FeatureFlags
from app.microsoft.api import MicrosoftAPIClient, get_microsoft_auth_token_provider
from app.microsoft.models import (
    MicrosoftCredentials,
    MicrosoftFileMetaData,
    MicrosoftSession,
    MSApplication,
    Report,
    Reports,
)
from app.util.string_functions import generate_report_file_name

FieldsInOrder = list[str | tuple[str, Any]]

SCOPE_URL = "https://manage.devcenter.microsoft.com/.default"
ANALYTICS_BASE_URL = "https://manage.devcenter.microsoft.com/v1.0/my/"
REPORT_BASE_URL = "https://manage.devcenter.microsoft.com/consumer/insights/v1.1/"

APP_ACQUISITIONS_GROUP_BY: str = "market,applicationName,acquisitionType,osVersion,age,deviceType,gender,paymentInstrumentType,sandboxId,storeClient,xboxTitleId,localCurrencyCode,xboxProductId,availabilityId,skuId,skuDisplayName,xboxParentProductId,parentProductName"
ADDONS_ACQUISITIONS_GROUP_BY: str = f"{APP_ACQUISITIONS_GROUP_BY},inAppProductName"


class Query(BaseModel):
    name: str
    query: str


DEFAULT_QUERY = Query(
    name="IndieBIAcquisitionV2",
    query="SELECT DateStamp, ProductId, TitleName, ParentProductId, ParentProductName FROM Acquisitions WHERE ProductId IN ('all') ORDER BY DateStamp TIMESPAN LAST_4_YEARS AGGREGATED Monthly",
)
QUERY_2Y = Query(
    name="IndieBIAcquisitionV22Y",
    query="SELECT DateStamp, ProductId, TitleName, ParentProductId, ParentProductName FROM Acquisitions WHERE ProductId IN ('all') ORDER BY DateStamp TIMESPAN LAST_2_YEARS AGGREGATED Monthly",
)

REQUIRED_FIELDS_IN_ORDER: FieldsInOrder = [
    "market",
    "applicationName",
    "acquisitionType",
    "osVersion",
    "age",
    "deviceType",
    "gender",
    ("paymentInstrumentType", ""),  # Sometimes it is missing (ie. Annapurna)
    "sandboxId",
    "storeClient",
    ("xboxTitleId", ""),  # Sometimes it is missing
    "localCurrencyCode",
    ("xboxProductId", ""),  # Sometimes it is missing (ie. SUPERHOT VR DEMO)
    ("availabilityId", ""),
    ("skuId", ""),  # Sometimes it's missing, appear to be physical activations
    "skuDisplayName",
    ("xboxParentProductId", ""),  # Sometimes it is missing
    ("parentProductName", ""),  # Sometimes it is missing (i.e Raw Fury)
    "applicationId",
    "date",
    "acquisitionQuantity",
    "purchasePriceUSDAmount",
    "purchasePriceLocalAmount",
    "purchaseTaxUSDAmount",
    "purchaseTaxLocalAmount",
    ("inAppProductName", ""),
]

log = logging.getLogger(__name__)


class MicrosoftSalesScraper(Scraper):
    manifest_version: int = 5
    """
    Manifest version that will be saved in manifest.json file.
    In Core Silver converter we support 4 different reports generations.
    - 1 and 2 are old and not used anymore
    - 3 is the current version (but without versioning in manifest.json)
    - 4 wast a never version that was never used
    - 5 is the same as 3 but with versioning in manifest.json (and will not brake converter)
    """

    manual_login_details = ManualLoginDetails(
        url="https://partner.microsoft.com/en-us/dashboard/home",
        success_selector=CSSSelector(value="#pageMainContent"),
    )

    def __init__(
        self,
        credentials: MicrosoftCredentials,
        session: MicrosoftSession,
        feature_flags: FeatureFlags,
    ) -> None:
        self._credentials: MicrosoftCredentials = credentials
        self._session: MicrosoftSession = session
        self._query: Query = (
            QUERY_2Y
            if "microsoft-sales-sku-report-range-2y" in feature_flags
            else DEFAULT_QUERY
        )

    def get_organizations(self) -> Sequence[Organization]:
        return [
            Organization(id=api.client_id, name=api.company_name)
            for api in self._session.api_setup_data
        ]

    def check_session(self) -> SessionIdentifier:
        if len(self._session.api_setup_data) == 0:
            raise InvalidCredentialsException

        session_id = "-".join(
            sorted(api.client_id for api in self._session.api_setup_data)
        )

        for api_setup_data in self._session.api_setup_data:
            log.info(
                "Checking API configuration for organization %s",
                api_setup_data.company_name,
            )
            auth_token_provider = get_microsoft_auth_token_provider(
                api_setup_data=api_setup_data, scope=SCOPE_URL
            )
            auth_token_provider()

        return SessionIdentifier(
            id=session_id,
            has_scrape_blocking_issues=False,
        )

    def login(self):
        log.info("Login")
        # Save in session only those API's that are configured for scraping
        self._session.api_setup_data = self._credentials.api_to_scrape()

    def scrape(
        self,
        from_: date,
        to: date,
        report_zip: ReportZip[MicrosoftFileMetaData],
        excluded_skus: list[str],
    ) -> ScrapeInfo:
        no_data = True
        if self._session.is_expired:
            raise SessionExpiredException

        for api_setup_data in self._session.api_setup_data:
            log.info(
                "Starting download process for organization %s",
                api_setup_data.company_name,
            )
            auth_token_provider = get_microsoft_auth_token_provider(
                api_setup_data=api_setup_data, scope=SCOPE_URL
            )

            with MicrosoftAPIClient(
                auth_token_provider=auth_token_provider,
                base_url=REPORT_BASE_URL,
            ) as client:
                applications: list[MSApplication] = SKUsFetcher(
                    client=client, query=self._query
                ).get_skus()

            log.info(
                "Found %d applications for organization %s",
                len(applications),
                api_setup_data.company_name,
            )

            with MicrosoftAPIClient(
                auth_token_provider=auth_token_provider,
                base_url=ANALYTICS_BASE_URL,
            ) as client:
                no_data_for_this_api: bool = scrape_acquisitions(
                    client=client,
                    report_zip=report_zip,
                    company_name=api_setup_data.company_name,
                    applications=applications,
                    date_from=from_,
                    date_to=to,
                    excluded_skus=excluded_skus,
                )
                no_data = no_data_for_this_api and no_data

        return ScrapeInfo(no_data=no_data)


def get_report_status(client: MicrosoftAPIClient, report_id):
    possible_statues = [
        # order from the most possible
        "Completed",
        "Failed",
        "Running",
        "Pending",
        "Paused",
    ]
    for status in possible_statues:
        status_count = client.get(
            f"ScheduledReport/execution/{report_id}",
            params={"executionStatus": status},
        ).json()["totalCount"]
        if status_count > 0:
            return status
    else:
        return "Unknown"


class SKUsFetcher:
    def __init__(self, client: MicrosoftAPIClient, query: Query) -> None:
        self._client: MicrosoftAPIClient = client
        self.query = query

    def get_skus(self) -> list[MSApplication]:
        log.info("Fetching list of SKUs. This may take a long time")
        log.info("Using query: %s", self.query.name)
        query = self._get_or_create_query()

        reports = self._all_reports(query_id=query["queryId"])

        report = self._find_valid_report(reports=reports)
        if report is not None:
            log.info("Found existing instant report for query %s", query["queryId"])
        else:
            log.info("Scheduling new report for query %s", query["queryId"])
            report_request = self._client.post(
                "ScheduledReport",
                json={
                    "QueryId": query["queryId"],
                    "ReportName": f"{self.query.name}Execution",
                    "executeNow": True,
                },
            )
            report = Report.model_validate(report_request.json()["value"][0])

        report_status_result = self._wait_for_report_to_finish(report=report)

        raw_products = {}
        report_url = report_status_result["value"][0]["reportAccessSecureLink"]

        with httpx.stream("GET", report_url, timeout=30) as r:
            if r.encoding is None:
                r.encoding = "utf-8"
            reader = csv.DictReader(r.iter_lines(), delimiter=",", quotechar='"')
            for row in reader:
                if row["ProductId"] not in raw_products:
                    raw_products[row["ProductId"]] = row

        if reports.has_report_from_today:
            log.info(
                "There was already a report from today, skipping schedule for the next one"
            )
        else:
            log.info("Scheduling next report for query %s", query["queryId"])
            self._client.post(
                "ScheduledReport",
                json={
                    "QueryId": query["queryId"],
                    "ReportName": f"{self.query.name}Execution",
                    "executeNow": True,
                },
            )

        return [
            MSApplication(
                product_id=product["ProductId"],
                title_name=product["TitleName"],
                parent_product_id=product["ParentProductId"],
                parent_product_name=product["ParentProductName"],
                in_app=product["ProductId"] != product["ParentProductId"],
            )
            for product in sorted(raw_products.values(), key=itemgetter("TitleName"))
        ]

    def _get_or_create_query(self) -> dict:
        existing_queries = self._client.get("ScheduledQueries")
        query = next(
            filter(
                lambda i: i["name"] == self.query.name,
                existing_queries.json()["value"],
            ),
            None,
        )
        if query is None:
            log.info("Creating CSV report query")
            query = self._create_query()
        return query

    def _create_query(self) -> dict:
        body = {
            "Query": self.query.query,
            "Name": self.query.name,
            "Description": "Acquisition query creation.",
        }
        response_create = self._client.post("ScheduledQueries", json=body)
        return response_create.json()["value"][0]

    def _all_reports(self, query_id) -> Reports:
        existing_raw_reports = self._client.get(
            "ScheduledReport", params={"queryId": query_id}
        ).json()["value"]
        return Reports(root=existing_raw_reports)

    def _find_valid_report(self, reports: Reports) -> Report | None:
        for report in reports.not_expired.root:
            status = get_report_status(self._client, report_id=report.report_id)
            log.debug(
                "Found report %s (start_time: %s) with status: %s",
                report.report_id,
                report.start_time,
                status,
            )
            if status in ["Completed", "Pending", "Running"]:
                log.debug(
                    "Using report %s (start_time: %s) with status: %s",
                    report.report_id,
                    report.start_time,
                    status,
                )
                return report

        return None

    def _wait_for_report_to_finish(self, report: Report):
        report_status_result = self._client.get(
            f"ScheduledReport/execution/{report.report_id}"
        ).json()

        log.info("Waiting for scheduled report to finish (id: %s)", report.report_id)
        completed_reports_count = report_status_result["totalCount"]
        while completed_reports_count == 0:
            sleep(30)
            response = self._client.get(
                f"ScheduledReport/execution/{report.report_id}?executionStatus=Failed"
            )

            try:
                failed_reports_json = response.json()
            except (json.decoder.JSONDecodeError, httpx.HTTPError) as e:
                log.debug("Full response: %s", response.text)
                log.error(
                    "Failed to decode JSON response for report %s; Exception: %s",
                    report.report_id,
                    e,
                )
                raise TemporaryApiIssueException
            except Exception as e:
                log.debug("Full response: %s", response.text)
                log.error(
                    "Failed to fetch report status for report %s; Exception: %s",
                    report.report_id,
                    e,
                    exc_info=e,
                )
                raise TemporaryApiIssueException

            if failed_reports_json["totalCount"] > 0:
                raise MissingPermissionsException("Failed to generate SKU report")

            report_status_result = self._client.get(
                f"ScheduledReport/execution/{report.report_id}"
            ).json()
            completed_reports_count = report_status_result["totalCount"]

        log.info("Scheduled report finished (id: %s)", report.report_id)
        return report_status_result


def _reorder_dict_keys(rows: list[dict], required_order: FieldsInOrder) -> list[dict]:
    reordered_data = []

    for row in rows:
        ordered_dict = {}
        for field in required_order:
            if isinstance(field, tuple):
                key, default_value = field
                ordered_dict[key] = row.get(
                    key, default_value
                )  # Use default if missing
            else:
                if field not in row:
                    raise KeyError(f"Required field '{field}' is missing.")
                ordered_dict[field] = row[field]  # Raise exception if missing
        reordered_data.append(ordered_dict)

    return reordered_data


def _fix_application_id_if_necessary(
    input_rows: list[dict], app: MSApplication
) -> list[dict]:
    result = [
        {
            **row,
            "applicationId": (
                row.get("applicationId")
                if row.get("applicationId") in {app.parent_product_id, app.product_id}
                else app.parent_product_id
                if row.get("acquisitionType") == "Iap"
                else app.product_id
            ),
        }
        for row in input_rows
    ]

    if input_rows != result:
        log.warning("Invalid application Id for rows related to %s", app.to_metadata())

    return result


def check_get_json_response_content(response_json: dict, app: MSApplication) -> None:
    if "Value" in response_json:
        return
    else:
        log.error(
            "Unknown response from API %s; Full response: %s",
            app.to_metadata(),
            response_json,
        )
        raise TemporaryApiIssueException


def scrape_acquisitions(
    client: MicrosoftAPIClient,
    report_zip: ReportZip,
    company_name: str,
    applications: list[MSApplication],
    date_from: date,
    date_to: date,
    excluded_skus: list,
) -> bool:
    no_data = True
    num_of_apps = len(applications)
    for i, app in enumerate(applications):
        if app.product_id in excluded_skus:
            log.info(
                "[%d/%d] Skipping excluded sku: %s",
                i + 1,
                num_of_apps,
                app.to_metadata(),
            )
            continue

        log.info(
            "[%d/%d] Fetching sales for SKU %s", i + 1, num_of_apps, app.to_metadata()
        )
        try:
            response: httpx.Response = client.get(
                "analytics/acquisitions",
                params={
                    "applicationId": app.product_id,
                    "startDate": str(date_from),
                    "endDate": str(date_to),
                    "groupby": ADDONS_ACQUISITIONS_GROUP_BY
                    if app.in_app
                    else APP_ACQUISITIONS_GROUP_BY,
                    "orderby": "date",
                },
            )
            response_json = response.json()
        except json.decoder.JSONDecodeError:
            log.error(
                "Failed to decode JSON response for SKU %s; Status code: %s; Response text: %s",
                app.to_metadata(),
                response.status_code,
                response.text,
            )
            raise TemporaryApiIssueException
        except Exception as e:
            log.error(
                "Failed to fetch sales for SKU %s; Exception: %s",
                app.to_metadata(),
                e,
                exc_info=e,
            )
            raise TemporaryApiIssueException

        check_get_json_response_content(response_json, app)

        report = _fix_application_id_if_necessary(response_json["Value"], app)
        report = _reorder_dict_keys(report, REQUIRED_FIELDS_IN_ORDER)
        if len(report) > 0:
            no_data = False

        report_suffix = f"{app.product_id}-{company_name}".upper()
        with report_zip.create_report(
            file_name=generate_report_file_name(
                source=Source.MICROSOFT_SALES,
                start_date=date_from,
                end_date=date_to,
                extension="json",
                suffix=report_suffix,
            ),
            meta_data=app.to_metadata(),
        ) as singe_report:
            singe_report.write(json.dumps(report, indent=4).encode())

    return no_data
