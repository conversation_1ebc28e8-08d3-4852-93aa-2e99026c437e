import json
import logging
from time import sleep
from typing import Callable

import httpx
import msal

from app.core.exceptions import InvalidCredentialsException
from app.microsoft.models import APISetupData

log = logging.getLogger(__name__)

MAX_RETRIES = 5


class MicrosoftAPIClient:
    def __init__(
        self,
        auth_token_provider: Callable[[], str],
        base_url: str | None = None,
    ) -> None:
        self._auth_token_provider: Callable[[], str] = auth_token_provider
        self._client: httpx.Client | None = None
        self._base_url: str | None = base_url

    def __enter__(self):
        self._client = httpx.Client(base_url=self._base_url)
        return self

    def __exit__(self, exc_type, exc_value, traceback) -> None:
        assert self._client is not None  # noqa: S101
        self._client.close()

    def get(self, url: str, **kwargs) -> httpx.Response:
        return self.request("get", url=url, **kwargs)

    def post(self, url: str, **kwargs) -> httpx.Response:
        return self.request("post", url=url, **kwargs)

    def get_paginated(self, url: str, bath_size=5, **kwargs) -> list[dict]:
        values = []
        response = self.get(url=url, params={"top": bath_size})

        values.extend(response.json()["value"])
        while "@nextLink" in response.json():
            response = self.get(response.json()["@nextLink"])
            values.extend(response.json()["value"])
        return values

    @staticmethod
    def _is_retry_applicable(response: httpx.Response) -> bool:
        retryable_status_codes = [
            httpx.codes.TOO_MANY_REQUESTS,
            httpx.codes.GATEWAY_TIMEOUT,
        ]
        is_retryable = response.status_code in retryable_status_codes
        try:
            is_retryable = is_retryable or (
                isinstance(response.json(), dict)
                and response.json().get("status_code")
                in [code.value for code in retryable_status_codes]
            )
        except json.decoder.JSONDecodeError:
            log.debug(
                "Failed to obtain JSON response, status code: %s; response content: %s; text: %s",
                response.status_code,
                response.content if response.content else "No content",
                response.text if response.text else "No text",
            )
        except Exception as e:
            log.debug("Unexpected exception: %s", e, exc_info=e)

        if is_retryable:
            log.debug("Retrying...")

        is_unauthorized = response.status_code == httpx.codes.UNAUTHORIZED
        if is_unauthorized:
            log.debug("Unauthorized")

        return is_retryable or is_unauthorized

    def request(self, method: str, url: str, **kwargs) -> httpx.Response:
        assert self._client is not None, "Use only in context manager!"  # noqa: S101

        log.debug("Running %s, %s", method, url)

        headers: dict[str, str] = {
            "Authorization": self._auth_token_provider(),
            "Content-Type": "application/json",
        }

        if "headers" in kwargs:
            headers.update(kwargs["headers"])
            del kwargs["headers"]

        response: httpx.Response = self._client.request(
            method=method, url=url, headers=headers, timeout=600, **kwargs
        )

        retry_count = 0
        while self._is_retry_applicable(response=response):
            if retry_count > MAX_RETRIES:
                break
            retry_count += 1
            headers["Authorization"] = self._auth_token_provider()
            retry_after_seconds = (
                int(response.headers.get("retry-after", 5 * retry_count)) + 1
            )
            log.debug("Try again in %s seconds", retry_after_seconds)
            sleep(retry_after_seconds)
            response = self._client.request(
                method=method, url=url, headers=headers, timeout=600, **kwargs
            )

        return response


def _handle_msal_error(token_response):
    error_description = token_response.get(
        "error_description", "No error description found"
    )
    error_codes = token_response.get("error_codes", [])
    if not len(error_codes):
        raise InvalidCredentialsException(error_description)

    match error_codes[0]:
        case ClientSecretExpiredException.msal_client_error_code:
            raise ClientSecretExpiredException(error_description)
        case InvalidClientIdException.msal_client_error_code:
            raise InvalidClientIdException(error_description)
        case InvalidTenantIdCodeException.msal_client_error_code:
            raise InvalidTenantIdCodeException(
                error_description
            )  # Captured at a higher level
        case InvalidClientSecretException.msal_client_error_code:
            raise InvalidClientSecretException(
                error_description
            )  # Captured at a higher level
        case (
            NullClientSecretException.msal_client_error_code
        ):  # Should never happen in current configuration
            raise NullClientSecretException(error_description)
        case _:
            raise InvalidCredentialsException(error_description)


def get_microsoft_auth_token_provider(api_setup_data: APISetupData, scope: str):
    try:
        msal_app = msal.ConfidentialClientApplication(
            client_id=api_setup_data.client_id,
            authority=f"https://login.microsoftonline.com/{api_setup_data.tenant_id}",
            client_credential=api_setup_data.client_secret,
        )
    except ValueError:
        raise InvalidCredentialsException(
            "Invalid API configuration. Check your tenant id"
        )

    def _token_provider() -> str:
        token_response: dict[str, str] | None = msal_app.acquire_token_for_client(
            scopes=[scope]
        )
        if token_response is None:
            raise InvalidCredentialsException("No response from MSAL API")

        if "error" in token_response:
            _handle_msal_error(token_response)
        return f"Bearer {token_response['access_token']}"

    return _token_provider


class MSALClientException(InvalidCredentialsException):
    msal_client_error_code: int


class ClientSecretExpiredException(MSALClientException):
    """Client secret key expired. (these keys are valid for a year)"""

    error_type = "SECRET_EXPIRED"
    msal_client_error_code = 7000222


class InvalidClientIdException(MSALClientException):
    """Invalid client id."""

    msal_client_error_code = 700016


class InvalidTenantIdCodeException(MSALClientException):
    """Invalid tenant id."""

    msal_client_error_code = 900023


class InvalidClientSecretException(MSALClientException):
    """Invalid client secret."""

    msal_client_error_code = 7000215


class NullClientSecretException(MSALClientException):
    """
    Null client secret.
    This error can only be obtained by intentionally setting client secret to None.
    It should not be possible to achieve in the current setup
    """

    msal_client_error_code = 7000216
