import logging
import urllib.parse as urlparse

import httpx
from bs4 import BeautifulSoup

from app.core.exceptions import (
    InvalidCredentialsException,
    MFAInvalid,
    TemporaryApiIssueException,
)
from app.core.mfa import MFA
from app.util.camel_case_model import CamelCaseModel

log = logging.getLogger(__name__)

BASE_URL = "https://ncms3.mng.nintendo.net"
BASE_LOGIN_URL = "https://ndid.mng.nintendo.net/ndid/oauth/Authorized"
HTML_PARSER_NAME = "html.parser"
LOGIN_FORM_CSS_SELECTOR = (
    'form[action*="/ndid/oauth/Authorized/issue"] input[name="loginid"]'
)


class NintendoCredentials(CamelCaseModel):
    user: str | None = None
    password: str | None = None
    totp_secret: str | None = None


def login(credentials: NintendoCredentials, http: httpx.Client, mfa: MFA) -> None:
    log.info("Logging into Nintendo")
    if credentials.user is None or credentials.password is None:
        raise InvalidCredentialsException("No credentials provided")

    http.cookies.clear()  # prevent from using partially logged in session

    unauthorized_dashboard_response = http.get(f"{BASE_URL}/ncms3/home/<USER>")
    query_params = urlparse.parse_qs(
        urlparse.urlparse(str(unauthorized_dashboard_response.url)).query
    )
    login_page_html = BeautifulSoup(
        unauthorized_dashboard_response.text, HTML_PARSER_NAME
    )
    is_login_form_present = login_page_html.select_one(LOGIN_FORM_CSS_SELECTOR)
    if not is_login_form_present:
        raise TemporaryApiIssueException("Din't find login form in the response")

    csrf_token = get_ticket_value(login_page_html)
    ticket = get_csrf_token_value(login_page_html)

    response = http.post(
        f"{BASE_LOGIN_URL}/issue",
        headers={"Content-Type": "application/x-www-form-urlencoded"},
        params=query_params,
        data={
            "_csrf": csrf_token,
            "loginid": credentials.user,
            "password": credentials.password,
            "recaptcha": "false",
            "ticket": ticket,
        },
    )
    if response.status_code >= 400:
        log.debug(
            "Error while trying to authenticate. Response status code: %s, message %s",
            response.status_code,
            response.text,
        )
        raise InvalidCredentialsException

    mfa_code = mfa.request()

    response = http.post(
        f"{BASE_LOGIN_URL}/mfa",
        headers={"Content-Type": "application/x-www-form-urlencoded"},
        params=query_params,
        data={
            "_csrf": csrf_token,
            "mfa_code": mfa_code,
        },
    )
    if response.status_code >= 400:
        raise MFAInvalid

    soup = BeautifulSoup(response.text, HTML_PARSER_NAME)
    is_login_form_present = soup.select_one(LOGIN_FORM_CSS_SELECTOR)
    if is_login_form_present:
        log.debug("Found login form in the response")
        raise InvalidCredentialsException(
            "Provided credentials or MFA code are invalid"
        )

    log.info("Logged in to Nintendo successfully")


def get_ticket_value(page_html: BeautifulSoup) -> str:
    ticket_input = page_html.find("input", {"name": "ticket"})
    if not ticket_input:
        raise TemporaryApiIssueException("Could not find ticket input")
    return ticket_input["value"]


def get_csrf_token_value(page_html: BeautifulSoup) -> str:
    csrf_input = page_html.find("input", {"name": "_csrf"})
    if not csrf_input:
        raise TemporaryApiIssueException("Could not find csrf token input")
    return csrf_input["value"]
