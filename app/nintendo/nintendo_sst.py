import base64
import csv
import io
import logging
from collections.abc import Sequence
from datetime import date

import requests
from bs4 import BeautifulSoup
from requests import codes

from app.core.exceptions import (
    InvalidSessionException,
    MissingPermissionsException,
    NoSessionException,
    SessionExpiredException,
)
from app.core.mfa import MFA
from app.core.scraper import (
    ManualLoginDetails,
    Organization,
    ReportZip,
    ScrapeInfo,
    Scraper,
    SessionIdentifier,
    XPathSelector,
)
from app.core.sessions import HTTPSession
from app.core.source import Source
from app.nintendo.login import LOGIN_FORM_CSS_SELECTOR, NintendoCredentials, login
from app.util.iterable import batched
from app.util.string_functions import generate_report_file_name

log = logging.getLogger(__name__)


DEFAULT_PRODUCT_IDS_PER_REPORT = 10

HTML_PARSER_NAME = "html.parser"

SST_BASE_URL = "https://sst.mng.nintendo.net/shoptools/"
SST_MAIN_PAGE: str = ""
SST_SALES_URL: str = "switchLicenseeReports/titleReport/search"
SST_WISHLIST_ACTIONS_URL: str = "licenseereports/wishlistReport/search"
SST_PRODUCTS_URL: str = "api/switchLicenseeProductCode"
SST_LOGGED_IN_CSS_SELECTOR = 'a[href="/shoptools/oauth/logout"]'


class SSTClient:
    def __init__(self, http: requests.Session) -> None:
        self._http = http

    def main_page(self) -> BeautifulSoup:
        self._refresh_authorization()
        response = self._get(SST_MAIN_PAGE)
        return BeautifulSoup(response.text, HTML_PARSER_NAME)

    def get_products(self) -> list[str]:
        self._refresh_authorization()
        response = self._get(SST_PRODUCTS_URL)
        try:
            products = response.json()
        except (requests.JSONDecodeError, AssertionError):
            log.debug("Failed to get products from response: %s", response.text)
            raise SessionExpiredException

        if not isinstance(products, list):
            log.debug("Products is not a list: %s", products)
            raise SessionExpiredException

        if len(products) == 0:
            log.debug("No products found in response: %s", response.text)
            raise MissingPermissionsException

        log.info("We found %s product IDs.", len(products))
        return products

    def get_valid_report(
        self,
        url: str,
        params: dict | None = None,
        *,
        expected_columns: list | None = None,
    ) -> bytes:
        self._refresh_authorization()
        response = self._get(url, params)
        data = base64.b64decode(response.json()["data"])

        self._validate_report(data, expected_columns)

        return data

    def _refresh_authorization(self) -> None:
        response = self._http.get(url=f"{SST_BASE_URL}oauth2/authorization/ndid")
        soup = BeautifulSoup(response.text, HTML_PARSER_NAME)
        if soup.select_one(LOGIN_FORM_CSS_SELECTOR):
            log.debug(
                "Refresh authorization returned login form. Invalidating session."
            )
            raise InvalidSessionException

    def _validate_report(self, data: bytes, expected_columns):
        log.debug("Validating report")
        try:
            csv_file = io.StringIO(data.decode("utf-8").lstrip("\ufeff"))
            reader = csv.DictReader(csv_file)
            if (
                expected_columns is not None
                and list(reader.fieldnames)[: len(expected_columns)] != expected_columns
            ):
                raise Exception(
                    f"Report contain unexpected fieldnames : {reader.fieldnames}"
                )

            list(reader)  # read the whole file to validate it
            log.debug("Report is valid")
        except Exception as e:
            log.error("Error while trying to parse CSV report: %s", e)
            raise SessionExpiredException

    def _get(self, url: str, params: dict | None = None) -> requests.Response:
        response = self._http.get(url=f"{SST_BASE_URL}{url}", params=params)

        if response.status_code == codes.found:
            log.debug("Got 302 Found status. Redirecting to NDID authorization.")
            self._refresh_authorization()
            response = self._http.get(url=f"{SST_BASE_URL}{url}", params=params)

        if response.status_code != codes.ok:
            log.error(
                "Error while trying to get data from %s. Response status code: %s, message %s",
                url,
                response.status_code,
                response.text[:100],
            )
            response.raise_for_status()
        return response


class _NintendoSSTScraper(Scraper):
    max_mfa_attempts: int = 1

    manual_login_details = ManualLoginDetails(
        url="https://sst.mng.nintendo.net/shoptools/switchLicenseeReports/titleReport",
        success_selector=XPathSelector(value='//a[@href="/shoptools/oauth/logout"]'),
    )

    def __init__(
        self,
        session: HTTPSession,
        credentials: NintendoCredentials,
        mfa: MFA,
        http: requests.Session,
    ) -> None:
        self._credentials = credentials
        self._mfa: MFA = mfa
        self._session = session
        self._http = http
        self._sst_client = SSTClient(http)

    def login(self) -> None:
        login(
            credentials=self._credentials,
            http=self._http,
            mfa=self._mfa,
        )

    def check_session(self) -> SessionIdentifier:
        log.info("Checking session")
        if not self._session.has_required_cookies():
            raise NoSessionException

        main_page_html = self._sst_client.main_page()
        if main_page_html.select(SST_LOGGED_IN_CSS_SELECTOR) == []:
            raise SessionExpiredException

        self._validate_if_user_is_not_half_logged_in()

        session_identifier = main_page_html.select(
            ".nav.pull-right>li>span.navbar-text"
        )[0].text.replace("User: ", "")

        return SessionIdentifier(id=session_identifier)

    def _validate_if_user_is_not_half_logged_in(self) -> None:
        try:
            self._sst_client.get_products()
        except InvalidSessionException as e:
            log.warning("User is half logged in. Logging out.")
            raise e

    def get_organizations(self) -> Sequence[Organization]:
        log.info("Getting organizations")
        return []


class NintendoWishlistsScraper(_NintendoSSTScraper):
    def scrape(
        self, from_: date, to: date, report_zip: ReportZip, excluded_skus: list[str]
    ) -> ScrapeInfo:
        log.info("Scraping Nintendo Wishlist Actions from %s to %s", from_, to)

        products: list[str] = self._sst_client.get_products()

        for index, products_batch in enumerate(
            batched(products, DEFAULT_PRODUCT_IDS_PER_REPORT)
        ):
            file_name = generate_report_file_name(
                source=Source.NINTENDO_WISHLISTS,
                start_date=from_,
                end_date=to,
                extension="csv",
                suffix=str(index),
            )
            report_data = self._get_wishlist_report(
                from_=from_,
                to=to,
                product_codes=products_batch,
            )

            report_zip.add_file(
                data=report_data,
                file_name=file_name,
                date_from=from_,
                date_to=to,
            )

        return ScrapeInfo(no_data=len(products) == 0)

    def _get_wishlist_report(self, from_: date, to: date, product_codes: list[str]):
        regions = ["JPN", "USA", "EUR", "AUS", "KOR", "CHN", "TWN", "Other"]
        devices = ["HAC", "BEE"]
        types_ = ["TITLE", "AOC", "BUNDLE"]

        params = {
            "period": "DAILY",
            "beginYear": from_.year,
            "beginMonth": from_.month,
            "endYear": to.year,
            "endMonth": to.month,
            "begin": from_.strftime("%Y/%m/%d"),
            "end": to.strftime("%Y/%m/%d"),
            "registeredOnly": "false",
            "searchUnit": "5",
            "searchTitles": "",
            "searchCodes": "\r\n".join(product_codes),
            "downloadCsv": "downloadCsv",
            "regions": regions,
            "devices": devices,
            "types": types_,
        }

        return self._sst_client.get_valid_report(
            SST_WISHLIST_ACTIONS_URL,
            params,
            expected_columns=[
                "NsUid",
                "Code",
                "Name",
                "Region",
                "Platform",
                "Publisher",
                "Sales Total",
                "Sales Conversion Rate",
                "Total",
                "Period Total",
            ],
        )


class NintendoSalesScraper(_NintendoSSTScraper):
    def scrape(
        self, from_: date, to: date, report_zip: ReportZip, excluded_skus: list[str]
    ) -> ScrapeInfo:
        log.info("Scraping Nintendo Sales from %s to %s", from_, to)

        products: list[str] = self._sst_client.get_products()

        for index, products_batch in enumerate(
            batched(products, DEFAULT_PRODUCT_IDS_PER_REPORT)
        ):
            file_name = generate_report_file_name(
                source=Source.NINTENDO_SALES,
                start_date=from_,
                end_date=to,
                extension="csv",
                suffix=str(index),
            )
            report_data = self._get_sales_report(
                from_=from_,
                to=to,
                product_codes=products_batch,
            )

            report_zip.add_file(
                data=report_data,
                file_name=file_name,
                date_from=from_,
                date_to=to,
            )

        return ScrapeInfo(no_data=len(products) == 0)

    def _get_sales_report(self, from_: date, to: date, product_codes: list[str]):
        regions = ["JPN", "USA", "EUR", "AUS", "KOR", "CHN", "TWN", "Other"]
        types_ = ["TITLE", "TRIAL", "AOC", "SERVICE_TICKET", "BUNDLE"]
        codes = ["P", "M", "V", "Y", "H", "X"]
        devices = ["HAC", "BEE"]
        additionals = ["NSUID"]

        params = {
            "period": "DAILY",
            "beginYear": from_.year,
            "beginMonth": from_.month,
            "endYear": to.year,
            "endMonth": to.month,
            "begin": from_.strftime("%Y/%m/%d"),
            "end": to.strftime("%Y/%m/%d"),
            "paid": "",
            "searchUnit": "0",
            "searchPrice": "true",
            "detail": "none",
            "searchTitle": "",
            "searchCodes": "\r\n".join(product_codes),
            "downloadCsv": "downloadCsv",
            "regions": regions,
            "devices": devices,
            "types": types_,
            "codes": codes,
            "additionals": additionals,
        }

        return self._sst_client.get_valid_report(
            SST_SALES_URL,
            params,
            expected_columns=[
                "TitleCode",
                "TitleName",
                "ItemCode",
                "ItemName",
                "Region",
                "Country",
                "CountryName",
                "Platform",
                "ContentType",
                "Publisher",
                "StartTime",
                "Points/Cost",
                "Currency",
                "First Week",
                "Total Sales",
                "eShop/PIN/POSA",
                "Card Type",
                "NsUid",
                "Period Total",
            ],
        )
