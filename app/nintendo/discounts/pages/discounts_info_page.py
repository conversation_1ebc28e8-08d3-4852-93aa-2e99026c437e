import json
import logging
import re
from contextlib import suppress

import httpx
from bs4 import <PERSON>Soup

from app.core.types import Dict<PERSON><PERSON><PERSON>, ListJSON
from app.nintendo.discounts.pages.http_request_handler import HttpRequestHandler
from app.nintendo.discounts.types import (
    AccessToken,
    DiscountGroupId,
    NsUid,
    SubmissionId,
)
from app.util.parsing import get_text_from_tag, get_value

log = logging.getLogger(__name__)


class DiscountInfoPage(HttpRequestHandler):
    _response: httpx.Response
    _soup: BeautifulSoup

    url = "https://ncms3.mng.nintendo.net/ncms3/temporaryDiscount/discountInfo?id={submission_id}"

    def __init__(self, http: httpx.Client, submission_id: SubmissionId):
        super().__init__(http, self.url.format(submission_id=submission_id))

        # Only needed because of missing sales name debug, consider removing _submission_id later on.
        self._submission_id = submission_id
        self._response = self.do_request()
        self._soup = BeautifulSoup(self._response.text, "html.parser")

    def get_access_token(self) -> AccessToken:
        return AccessToken(get_value(self._soup, "input", {"id": "accessToken"}))

    def get_discount_group_ids(self) -> list[DiscountGroupId]:
        log.info("Getting discount group ids")

        return [
            DiscountGroupId(discount_group_id)
            for discount_group_id in get_value(
                self._soup, "input", {"id": "discountGroupIds"}
            ).split(",")
        ]

    def get_ns_uids(self) -> list[NsUid]:
        pattern = r'"nsUid":\s*"(?P<ns_uid>\d+)"'
        matches = re.finditer(pattern, self._response.text)
        return [NsUid(match.group("ns_uid")) for match in matches]

    def get_sale_name(self) -> str:
        log.info("Getting sale name")
        value = get_text_from_tag(
            self._soup.select_one("textarea#saleName"), allow_empty=True
        )

        if not value:
            value = f"Missing sale name for submission {self._submission_id}"
            log.debug(value)

        return value

    def get_discount_content_list(self) -> DictJSON:
        # We are assuming that getDiscountContentList function extracted from 'script' section of
        # the page always returns static JSON-like object. I can imagine this could change on their
        # website in the future therefore convertion on our side is performed, but we have a
        # fallback in form of sending a content of non-converted content in case of any error.
        log.info("Getting discount content list")
        content = None
        regx = r"function\s+getDiscountContentList\s*\(\s*\)\s*\{\s*return\s+(?P<json>.*?)\s*;\s*\}"
        match = re.search(regx, self._response.text)
        if match:
            content = match.group("json")
            with suppress(Exception):
                return {"valid": True, "content": json.loads(content)}

        return {"valid": False, "content": content}

    def get_country_currency_mapping(self) -> ListJSON:
        log.info("Getting country currency mapping")
        trimmed_text = self._response.text.replace("\\", "")
        pattern = (
            r'"countryCode":"(?P<countryCode>\w+)",'
            r'"countryName":"(?P<countryName>[\w\s]+)",'
            r'"currency":{"currencyCode":"(?P<currencyCode>\w+)",'
        )
        matches = re.finditer(pattern, trimmed_text)
        return [
            {
                "countryCode": match.group("countryCode"),
                "countryName": match.group("countryName"),
                "currencyCode": match.group("currencyCode"),
            }
            for match in matches
        ]

    def get_region_code(self) -> str:
        code = get_value(self._soup, "input", {"id": "regionCode"})
        return code
