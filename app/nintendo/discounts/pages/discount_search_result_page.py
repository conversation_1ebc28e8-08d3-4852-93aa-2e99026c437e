import logging

import requests
from bs4 import BeautifulSoup

# pyright: reportMissingTypeStubs=false
from retry import retry

from app.core.exceptions import SessionExpiredException
from app.nintendo.discounts.pages.discount_search_result_page_form_params import (
    get_form_params,
)
from app.nintendo.discounts.pages.http_request_handler import HttpR<PERSON><PERSON><PERSON>andler
from app.nintendo.discounts.types import CSRFToken, DiscountSearchResult, SubmissionId
from app.util.parsing import get_value

log = logging.getLogger(__name__)


class DiscountSearchResultPage(HttpRequestHandler):
    _soup: BeautifulSoup

    URL = "https://ncms3.mng.nintendo.net/ncms3/discount/search"
    RESULTS_PER_PAGE = 50

    def __init__(
        self,
        http: requests.Session,
        token: CSRFToken,
        page_number: int,
        datetime_format: str,
    ):
        super().__init__(http)
        self.load_page(token, page_number)
        self.datetime_format = datetime_format

    @retry(SessionExpiredException, tries=5, delay=5, logger=log)
    def load_page(self, token: CSRFToken, page_number: int) -> None:
        headers = {"x-csrf-token": token}
        data = get_form_params(token, self.RESULTS_PER_PAGE, page_number)
        response = self.do_post_request(self.URL, data, headers)
        self._soup = BeautifulSoup(response.text, "html.parser")
        self.validate_is_logged_in()

    def get_results_number(self) -> int:
        return int(get_value(self._soup, "input", {"id": "maxCountTERM"}))

    def get_results_per_page(self) -> int:
        return int(get_value(self._soup, "input", {"id": "termDiscountDispCount"}))

    def get_discount_records(self) -> list[DiscountSearchResult]:
        records = []
        for row in self._soup.select("tr.indicator.ac"):
            # Get all td elements in the row
            cells = row.find_all("td")
            if len(cells) < 9:  # Skip rows that don't match expected format
                continue

            submission_id = SubmissionId(cells[1].find("span").text.strip())

            records.append(
                DiscountSearchResult.from_raw_data(
                    submission_id=submission_id,
                    product_code=cells[2].text.strip(),
                    region=cells[3].text.strip(),
                    sale_name=cells[4].text.strip(),
                    start_datetime=cells[5].text.strip(),
                    end_datetime=cells[6].text.strip(),
                    status=cells[7].text.strip(),
                    last_update=cells[8].text.strip(),
                    datetime_format=self.datetime_format,
                )
            )

        return records

    def get_submission_ids(self) -> list[SubmissionId]:
        return [record.submission_id for record in self.get_discount_records()]

    def is_last_page(self) -> bool:
        return self.get_page_number() == self.get_total_pages()

    def get_page_number(self) -> int:
        return int(get_value(self._soup, "input", {"id": "pageTermDiscount"}))

    def get_total_pages(self) -> int:
        return self.get_results_number() // self.get_results_per_page() + 1

    def validate_is_logged_in(self):
        log.info("Validating if user is logged in")
        if self._soup.find("div", class_="loginBox loginForm"):  # login form is present
            raise SessionExpiredException
