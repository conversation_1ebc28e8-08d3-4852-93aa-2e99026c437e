import logging
from http.client import RemoteDisconnected
from typing import Any, Optional
from urllib.parse import parse_qs, urlencode, urlparse, urlunparse

import httpx
from httpx import TimeoutException
from retry import retry

from app.core.exceptions import MissingPermissionsException, SessionExpiredException

log = logging.getLogger(__name__)


class HttpRequestHandler:
    _http: httpx.Client
    _url: Optional[str]

    def __init__(self, http: httpx.Client, url: Optional[str] = None):
        self._http = http
        self._url = url

    @retry(
        exceptions=(SessionExpiredException, TimeoutException, RemoteDisconnected),
        tries=5,
        delay=2,
        logger=log,
    )
    def do_request(
        self,
        url: Optional[str] = None,
        treat_authorization_errors_as_session_expired: bool = True,
    ) -> httpx.Response:
        response = self._http.get(self._get_url(url), timeout=60)
        return self._check_for_errors(
            response, treat_authorization_errors_as_session_expired
        )

    @retry(
        exceptions=(SessionExpiredException, TimeoutException, RemoteDisconnected),
        tries=5,
        delay=2,
        logger=log,
    )
    def do_post_request(
        self,
        url: Optional[str] = None,
        data: Any = None,
        extra_headers: Optional[dict[str, str]] = None,
    ) -> httpx.Response:
        headers = {**self._http.headers, **(extra_headers or {})}
        # using `files` makes requests use `multipart/form-data` content type automatically
        response = self._http.post(
            self._get_url(url), files=data, headers=headers, timeout=60
        )
        return self._check_for_errors(response)

    def _check_for_errors(
        self,
        response: httpx.Response,
        treat_authorization_errors_as_session_expired: bool = True,
    ) -> httpx.Response:
        if response.status_code in [401, 403]:  # unauthorized, forbidden
            log.error(
                "Session expired. Status: %s, url: %s, reason: %s, response: %s",
                response.status_code,
                self.obfuscate_url(str(response.url)),
                response.reason_phrase,
                response.text,
            )
            if treat_authorization_errors_as_session_expired:
                raise SessionExpiredException
            raise MissingPermissionsException

        try:
            response.raise_for_status()
        except httpx.HTTPStatusError as e:  # httpx equivalent of HTTPError
            log.exception(e)
            log.error(
                "Status: %s, url: %s, reason: %s, response: %s",
                response.status_code,
                self.obfuscate_url(str(response.url)),
                response.reason_phrase,
                response.text,
            )
            raise e
        except TimeoutException as e:
            log.exception(e)
            log.error(
                "Timeout: url: %s",
                self.obfuscate_url(str(response.url)),
            )
            raise SessionExpiredException from e

        return response

    def _get_url(self, url: Optional[str]) -> str:
        if not (value := (url or self._url)):
            raise ValueError("url is not set")
        return value

    def obfuscate_url(self, url: str) -> str:
        def replace_sensitive_param_values(params: Any) -> dict[str, str]:
            trigger_words = ["token", "auth", "key", "pass"]
            return {
                key: "HIDDEN"
                if any(word in key.lower() for word in trigger_words)
                else value
                for key, value in params.items()
            }

        parsed_url = urlparse(url)
        query_params = replace_sensitive_param_values(parse_qs(parsed_url.query))
        new_query_string = urlencode(query_params, doseq=True)
        return urlunparse(parsed_url._replace(query=new_query_string))
