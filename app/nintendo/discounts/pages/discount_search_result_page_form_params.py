from app.nintendo.discounts.types import (
    ContentType,
    CSRFToken,
    DiscountStatus,
    DiscountType,
)


def get_form_params(
    csrf_token: CSRFToken,
    results_per_page: int,
    page_number: int,
):
    # Base params
    params = [
        ("submissionId", (None, "")),
        ("discountTypes", (None, DiscountType.TEMPORARY.value)),
        ("deviceTypeId", (None, "")),
        ("subjectContentName", (None, "")),
        ("initialCode", (None, "")),
        ("region", (None, "")),
        ("country", (None, "")),
        ("statuses", (None, DiscountStatus.PROCESS_COMPLETED.value)),
    ]

    # Add UTC timezone for all date fields
    date_fields = [
        "startDateTimeFrom",
        "startDateTimeTo",
        "endDateTimeFrom",
        "endDateTimeTo",
        "rangeDateTimeFrom",
        "rangeDateTimeTo",
    ]
    for field in date_fields:
        params.append((f"{field}.standardTimeZone", (None, "UTC")))

    # Rest of the params
    params.extend([
        ("saleName", (None, "")),
        ("discountTypeId", (None, "0")),
        ("sortKeyTermDiscount", (None, "SAVED_DATETIME")),
        ("sortKeyFlagTermDiscount", (None, "false")),
        ("ownerDiscountDispCount", (None, "0")),
        ("sortKeyOwnerDiscount", (None, "SAVED_DATETIME")),
        ("sortKeyFlagOwnerDiscount", (None, "false")),
        ("_csrf", (None, csrf_token)),
        ("termDiscountDispCount", (None, str(results_per_page))),
        ("pageTermDiscount", (None, str(page_number))),
    ])

    return params + [("contentTypes", (None, value)) for value in ContentType.values()]
