import logging

import requests
from bs4 import <PERSON><PERSON>oup
from retry import retry

from app.core.exceptions import MissingPermissionsException, SessionExpiredException
from app.nintendo.discounts.pages.discount_search_result_page import (
    DiscountSearchResultPage,
)
from app.nintendo.discounts.pages.http_request_handler import HttpRequestHandler
from app.nintendo.discounts.types import CSRFToken, DiscountSearchResult, SubmissionId
from app.util.parsing import get_attribute, get_text_from_tag

log = logging.getLogger(__name__)


class DiscountSearchPage(HttpRequestHandler):
    _response: requests.Response
    _soup: BeautifulSoup
    _csrf_token: CSRFToken

    URL = "https://ncms3.mng.nintendo.net/ncms3/discount/search"
    LOGIN_SUCCESS_SELECTOR = "span.ncms_headerRight_userName_loginId"
    LOGIN_FORM_SELECTOR = "div.loginBox.loginForm"
    MISSING_PERMISSION_SELECTOR = (
        "div[id*='Your account doesn’t have NCMS login rights.']"
    )

    def __init__(self, http: requests.Session):
        super().__init__(http, self.URL)
        self._load_page()

    @retry(SessionExpiredException, tries=5, delay=2, logger=log)
    def _load_page(self):
        log.info("Getting discount search page")
        self._response = self.do_request(
            treat_authorization_errors_as_session_expired=False
        )
        self._soup = BeautifulSoup(self._response.text, "html.parser")
        self.validate_is_on_login_page()
        self.validate_permissions()
        self._csrf_token = self.get_csrf_token()

    def get_session_identifier(self) -> str:
        try:
            tag = self._soup.select_one(self.LOGIN_SUCCESS_SELECTOR)
            return get_text_from_tag(tag, allow_empty=False)
        except ValueError as e:
            log.error(
                "Session expired. Could not find session identifier in page response."
            )
            raise SessionExpiredException from e

    def get_all_discount_records(self) -> list[DiscountSearchResult]:
        return [
            record
            for page in self.get_all_pages()
            for record in page.get_discount_records()
        ]

    def get_all_submission_ids(self) -> list[SubmissionId]:
        return sorted([
            record.submission_id for record in self.get_all_discount_records()
        ])

    def get_all_pages(self) -> list[DiscountSearchResultPage]:
        log.info("Getting discounts search result page: 1")
        active_page = self.get_search_result_page(1)
        total = active_page.get_total_pages()
        result = [active_page]

        while not active_page.is_last_page():
            log.info(
                "Getting discounts search result page: %s/%s",
                active_page.get_page_number() + 1,
                total,
            )
            active_page = self.get_search_result_page(active_page.get_page_number() + 1)
            result.append(active_page)

        return result

    def get_search_result_page(self, page_number: int) -> DiscountSearchResultPage:
        client_datetime_format = self.get_client_datetime_format()
        datetime_format = self.get_datetime_format()
        range_date_time_format = self.get_range_date_time_format()

        log.debug(
            "Detected datetime formats: '%s', '%s', '%s'",
            datetime_format,
            client_datetime_format,
            range_date_time_format,
        )

        return DiscountSearchResultPage(
            self._http, self._csrf_token, page_number, client_datetime_format
        )

    def get_csrf_token(self) -> CSRFToken:
        try:
            return CSRFToken(
                get_attribute(
                    self._soup, "meta", {"name": "_csrf"}, "content", allow_empty=False
                )
            )
        except ValueError as e:
            log.error("Session expired. Could not find CSRF token in page response.")
            raise SessionExpiredException from e

    def validate_permissions(self) -> None:
        log.info("Validating permissions")
        tag = self._soup.select_one(self.MISSING_PERMISSION_SELECTOR)
        if tag:
            raise MissingPermissionsException

    def validate_is_on_login_page(self):
        log.info("Validating if user is logged in")
        if self._soup.select_one(self.LOGIN_FORM_SELECTOR):
            raise SessionExpiredException

    def get_client_datetime_format(self) -> str:
        """Extracts datetime format from '<input type="hidden" class="clientDatetimeFormat" value="MM/DD/YYYY HH:mm:ss" />'"""
        return get_attribute(
            self._soup,
            "input",
            {"class": "clientDatetimeFormat"},
            "value",
            allow_empty=False,
        )

    def get_datetime_format(self) -> str:
        return get_attribute(
            self._soup,
            "input",
            {"class": "datetimeFormat"},
            "value",
            allow_empty=False,
        )

    def get_range_date_time_format(self) -> str:
        """
        Extracts format from tag like:
        <input type="text" format="dd/mm/yy" id="rangeDateTimeTo.dateField" name="rangeDateTimeTo.dateField" value="" />
        """
        return get_attribute(
            self._soup,
            "input",
            {"id": "rangeDateTimeTo.dateField"},
            "format",
            allow_empty=False,
        )
