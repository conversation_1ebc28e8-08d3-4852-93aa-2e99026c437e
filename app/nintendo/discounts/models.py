from enum import Enum
from typing import Optional

from pydantic import BaseModel, ConfigDict, Field, alias_generators

from app.nintendo.discounts.types import NsUid


class DataSource(str, Enum):
    QA = "QA"
    LIVE = "LIVE"
    BOTH = "both"  # yes, this one is lowercase

    def __str__(self) -> str:
        return self.value

    @classmethod
    def values(cls):
        return [member.value for member in cls]


class OutputDiscount(BaseModel):
    model_config = ConfigDict(
        populate_by_name=True,
        alias_generator=alias_generators.to_snake,
    )

    submission_id: str
    sale_name: str
    discount_group_id: str
    product_code: str
    ns_uid: str
    discount_id: str
    country_code: str
    start_datetime: str
    end_datetime: str
    currency: str
    discount_value: str
    regular_price: str
    price_start_datetime: str
    price_end_datetime: str
    platform_name: str | None


class CountryCurrency(BaseModel):
    class Config:
        allow_population_by_alias = True

    country_code: str = Field(alias="countryCode")
    country_name: str = Field(alias="countryName")
    currency_code: str = Field(alias="currencyCode")


class CountryCurrencyMapping(BaseModel):
    mapping: list[CountryCurrency]


class Discount(BaseModel):
    class Config:
        allow_population_by_alias = True

    discount_id: str = Field(alias="discountId")
    price_id: str = Field(alias="priceId")
    country_code: str = Field(alias="countryCode")
    discount_value: str = Field(alias="discountValue")
    start_datetime: str = Field(alias="startDatetime")
    end_datetime: str = Field(alias="endDatetime")


class DiscountGroupPerNsuid(BaseModel):
    class Config:
        allow_population_by_alias = True

    contents_type: str = Field(alias="contentsType")
    ns_uid: NsUid = Field(alias="nsUid")
    discounts: list[Discount] = Field(alias="discounts")


class DiscountGroup(BaseModel):
    class Config:
        allow_population_by_alias = True

    data_source: DataSource = Field(alias="dataSource")
    contents: list[DiscountGroupPerNsuid] = Field(alias="contents")
    discount_group_id: str = Field(alias="discountGroupId")


class DiscountGroupResponse(BaseModel):
    class Config:
        allow_population_by_alias = True

    discount_groups: list[DiscountGroup] = Field(alias="discountGroups")


class Price(BaseModel):
    class Config:
        allow_population_by_alias = True

    price_id: str = Field(alias="priceId")
    start_datetime: str = Field(alias="startDatetime")
    end_datetime: Optional[str] = Field(alias="endDatetime")
    amount: str


class OnlinePrice(BaseModel):
    class Config:
        allow_population_by_alias = True

    data_source: DataSource = Field(alias="dataSource")
    prices: list[Price] = Field(alias="prices")


class OnlinePriceResponse(BaseModel):
    class Config:
        allow_population_by_alias = True

    online_prices: list[OnlinePrice] = Field(alias="onlinePrices")


class TargetTitlesPrice(BaseModel):
    class Config:
        allow_population_by_alias = True

    price_id: str = Field(alias="priceId")
    country_code: str = Field(alias="countryCode")
    start_datetime: str = Field(alias="startDatetime")
    end_datetime: Optional[str] = Field(alias="endDatetime")
    amount: str


class TargetTitlesContent(BaseModel):
    class Config:
        allow_population_by_alias = True

    prices: list[TargetTitlesPrice] = Field(alias="prices")


class TargetTitlesResponse(BaseModel):
    class Config:
        allow_population_by_alias = True

    contents: list[TargetTitlesContent] = Field(alias="contents")


class Content(BaseModel):
    class Config:
        allow_population_by_alias = True

    ns_uid: NsUid = Field(alias="nsUid")
    product_code: str = Field(alias="productCode")
    platform_name: Optional[str] = Field(alias="platFormName")


class ContentListWrapper(BaseModel):
    class Config:
        allow_population_by_alias = True

    valid: bool = Field(alias="valid")
    content: list[Content] | str = Field(alias="content")
