import logging
import re
from dataclasses import dataclass
from datetime import datetime
from enum import Enum
from typing import NewType, Union

from dateutil import tz

log = logging.getLogger(__name__)

AccessToken = NewType("AccessToken", str)
DiscountGroupId = NewType("DiscountGroupId", str)
NsUid = NewType("NsUid", str)
CSRFToken = NewType("CSRFToken", str)


class SubmissionId(int):
    def __new__(cls, value: Union[int, str]):
        if isinstance(value, str):
            return super().__new__(cls, int(value))
        return super().__new__(cls, value)


class DataSource(str, Enum):
    QA = "QA"
    LIVE = "LIVE"
    BOTH = "BOTH"

    def __str__(self) -> str:
        return self.value

    @classmethod
    def values(cls):
        return [member.value for member in cls]


class DiscountStatus(str, Enum):
    DRAFT = "1"
    SUBMITTED_FOR_REVIEW = "2"
    IN_REVIEW = "3"
    WAITING_FOR_UPDATED_SUBMISSION = "7"
    PREPARING_FOR_RELEASE = "5"
    IN_APPROVAL_REQUEST = "10"
    PROCESS_COMPLETED = "11"

    def __str__(self) -> str:
        return self.value


class DiscountType(str, Enum):
    TEMPORARY = "0"
    OWNER = "1"

    def __str__(self) -> str:
        return self.value

    @classmethod
    def values(cls):
        return [member.value for member in cls]


class ContentType(str, Enum):
    TITLE = "1"
    AOC = "7"
    BUNDLE = "9"
    CONSUMABLEITEM = "31"

    def __str__(self) -> str:
        return self.value

    @classmethod
    def values(cls):
        return [member.value for member in cls]


@dataclass
class DiscountSearchResult:
    submission_id: SubmissionId
    product_code: str
    region: str
    sale_name: str
    start_datetime: datetime | None  # Now storing as UTC datetime
    end_datetime: datetime | None  # Now storing as UTC datetime
    status: str
    last_update: datetime | None  # Now storing as UTC datetime

    @classmethod
    def from_raw_data(
        cls,
        submission_id: SubmissionId,
        product_code: str,
        region: str,
        sale_name: str,
        start_datetime: str,
        end_datetime: str,
        status: str,
        last_update: str,
        datetime_format: str,
    ) -> "DiscountSearchResult":
        return cls(
            submission_id=submission_id,
            product_code=product_code,
            region=region,
            sale_name=sale_name,
            start_datetime=cls.parse_datetime(start_datetime, datetime_format),
            end_datetime=cls.parse_datetime(end_datetime, datetime_format),
            status=status,
            last_update=cls.parse_datetime(last_update, datetime_format),
        )

    @staticmethod
    def parse_datetime(date_str_to_parse: str, datetime_format: str) -> datetime:
        """Parse Nintendo's datetime format with timezone.
        Example inputs for different formats:
            "26/10/2023 15:00:00 (UTC)"   # DD/MM/YYYY HH:mm:ss
            "11/24/2023 23:59:59 (UTC)"   # MM/DD/YYYY HH:mm:ss
            "2025/03/24 17:00:00 (UTC)"   # YYYY/MM/DD HH:mm:ss
            "2025/24/03 17:00:00 (UTC)"   # YYYY/DD/MM HH:mm:ss
            "2025-03-24 17:00:00 (UTC)"   # YYYY-MM-DD HH:mm:ss
        """
        if not date_str_to_parse:
            log.info(f"Empty date string. Format: {datetime_format}")
            raise ValueError("Empty date string")

        empty_date_strings = [
            "----/--/-- --:--:-- (----)",
            "--/--/---- --:--:-- (----)",
        ]

        if date_str_to_parse in empty_date_strings:
            log.info(f"Empty date string. Format: {datetime_format}")
            return None

        match = re.match(r"^(.+) \(UTC\)$", date_str_to_parse)
        if not match:
            raise ValueError(
                f"Invalid datetime format. Expected 'DATE TIME (UTC)', got: '{date_str_to_parse}'. "
                f"Format: {datetime_format}"
            )

        date_time_str = match.group(1)

        format_mapping = {
            "MM/DD/YYYY HH:mm:ss": "%m/%d/%Y %H:%M:%S",
            "DD/MM/YYYY HH:mm:ss": "%d/%m/%Y %H:%M:%S",
            "YYYY/MM/DD HH:mm:ss": "%Y/%m/%d %H:%M:%S",
            "YYYY/DD/MM HH:mm:ss": "%Y/%d/%m %H:%M:%S",
            "YYYY-MM-DD HH:mm:ss": "%Y-%m-%d %H:%M:%S",
        }

        if datetime_format not in format_mapping:
            raise ValueError(
                f"Unsupported datetime format: '{datetime_format}'. "
                f"Format: {datetime_format}"
            )

        try:
            dt = datetime.strptime(date_time_str, format_mapping[datetime_format])
            return dt.replace(tzinfo=tz.UTC)
        except ValueError:
            raise ValueError(
                f"Failed to parse '{date_time_str}' using format {datetime_format}. "
                f"Make sure the date matches the expected format."
            )
