import datetime
import logging

import httpx
from retry import retry

from app.core.exceptions import Scraper<PERSON>x<PERSON>, SessionExpiredException
from app.core.types import JSON
from app.nintendo.discounts.pages.http_request_handler import HttpRequestHandler
from app.nintendo.discounts.types import (
    ContentType,
    CSRFToken,
    DataSource,
    DiscountGroupId,
    NsUid,
    SubmissionId,
)

log = logging.getLogger(__name__)


class API(HttpRequestHandler):
    base_url = "https://ncms3.mng.nintendo.net/ncms3/rs"

    def __init__(self, http: httpx.Client):
        super().__init__(http, None)

    @retry(SessionExpiredException, tries=5, delay=5, logger=log)
    def get_group_discounts(
        self, csrf_token: CSRFToken, group_id: DiscountGroupId
    ) -> JSON:
        log.info("Getting group discounts for group %s", group_id)
        url = f"{self.base_url}/discount/term/get"

        data = [
            ("discountGroupId", (None, group_id)),
            ("changesetId", (None, "")),
            ("dataSource", (None, DataSource.BOTH.value)),
            ("targetEnvironment", (None, "RELEASE")),
        ]

        headers = {"x-csrf-token": csrf_token}
        return self.do_post_request(url, data, headers).json()

    def get_prices(
        self, csrf_token: CSRFToken, ns_uid: NsUid, product_content_type: str
    ) -> JSON:
        log.info("Getting prices for nsuid %s (%s)", ns_uid, product_content_type)
        url = self.get_price_url(product_content_type)

        data = [
            ("nsUid", (None, ns_uid)),
            ("changesetId", (None, "")),
            ("dataSource", (None, DataSource.BOTH.value)),
            ("targetEnvironment", (None, "RELEASE")),
        ]

        headers = {"x-csrf-token": csrf_token}

        return self.do_post_request(url, data, headers).json()

    def get_price_url(self, product_content_type: str) -> str:
        price_url_mapping = {
            **{ct.name: ct.name.lower() for ct in ContentType},
            ContentType.CONSUMABLEITEM.name: "consumable",
        }
        try:
            content_type = price_url_mapping[product_content_type]
        except KeyError:
            raise ScraperException(
                f"Invalid product content type: {product_content_type}. Cannot get price URL."
            )

        return f"{self.base_url}/{content_type}/onlineprice/get"

    def get_target_titles(
        self,
        csrf_token: CSRFToken,
        submission_id: SubmissionId,
        ns_uid: NsUid,
        content_type: str,
        start_datetime_to: datetime.datetime,
        region: str,
    ) -> JSON:
        log.info("Getting target titles for nsuid %s", ns_uid)
        url = f"{self.base_url}/discount/term/getTargetTitles"
        max_int32 = 2**31 - 1

        start_datetime_formatted = start_datetime_to.strftime("%Y-%m-%dT%H:%M:%S+0000")

        data = [
            ("id", (None, submission_id)),
            ("startDatetimeTo", (None, start_datetime_formatted)),
            ("contentsType", (None, content_type)),
            ("nsUid", (None, ns_uid)),
            ("region", (None, region)),
            ("perPage", (None, str(max_int32))),
            ("targetEnvironment", (None, "RELEASE")),
        ]

        headers = {"x-csrf-token": csrf_token}
        return self.do_post_request(url, data, headers).json()
