import logging
from typing import TypedDict, cast

from app.core.types import CSV, JSON, Dict<PERSON>SO<PERSON>, ListJSON
from app.nintendo.discounts.models import (
    Content,
    ContentListWrapper,
    CountryCurrencyMapping,
    DataSource,
    DiscountGroup,
    DiscountGroupResponse,
    OnlinePriceResponse,
    OutputDiscount,
    Price,
    TargetTitlesPrice,
    TargetTitlesResponse,
)
from app.nintendo.discounts.types import NsUid, SubmissionId

log = logging.getLogger(__name__)


DATA_SOURCE = DataSource.LIVE


def to_csv(
    raw_discount_group_responses_per_submission_id: dict[SubmissionId, list[JSON]],
    raw_online_price_responses_per_nsuid: dict[NsUid, JSON],
    target_titles_responses_per_nsuid: dict[NsUid, JSON],
    sale_names_per_submission_id: dict[SubmissionId, str],
    content_list_per_submission_id: dict[SubmissionId, DictJSON],
    country_currency_mapping: ListJSON,
) -> CSV:
    log.info("Converting JSON files to CSV file...")

    prices: dict[NsUid, OnlinePriceResponse] = {
        nsuid: OnlinePriceResponse.model_validate(response)
        for nsuid, response in raw_online_price_responses_per_nsuid.items()
    }
    target_titles: dict[NsUid, TargetTitlesResponse] = {
        nsuid: TargetTitlesResponse.model_validate(response)
        for nsuid, response in target_titles_responses_per_nsuid.items()
    }

    entries = raw_discount_group_responses_per_submission_id

    rows = [
        OutputDiscount(
            submission_id=str(submission_id),
            sale_name=sale_names_per_submission_id[submission_id],
            discount_group_id=discount_group.discount_group_id,
            discount_id=discount.discount_id,
            country_code=discount.country_code,
            start_datetime=discount.start_datetime,
            end_datetime=discount.end_datetime,
            currency=get_currency(country_currency_mapping, discount.country_code),
            discount_value=discount.discount_value,
            **_get_price_for_discount(
                prices[discount_group_per_nsuid.ns_uid],
                target_titles[discount_group_per_nsuid.ns_uid],
                discount.price_id,
            ),
            **_get_content_list(
                content_list_per_submission_id[submission_id],
                discount_group_per_nsuid.ns_uid,
            ).model_dump(),
        )
        for submission_id, list_of_raw_discount_group_response in entries.items()
        for raw_discount_group_response in list_of_raw_discount_group_response
        for discount_group in _get_discount_groups(raw_discount_group_response)
        if discount_group.data_source == DATA_SOURCE
        for discount_group_per_nsuid in discount_group.contents
        for discount in discount_group_per_nsuid.discounts
    ]

    return {
        "headers": list(OutputDiscount.model_fields.keys()),
        "rows": [row.model_dump() for row in rows],
    }


def get_currency(country_currency_mapping: ListJSON, country_code: str) -> str:
    m = CountryCurrencyMapping.model_validate({"mapping": country_currency_mapping})
    for row in m.mapping:
        if row.country_code == country_code:
            return row.currency_code

    raise ValueError(f"Could not find currency for country code: {country_code}")


def _get_content_list(content_list: DictJSON, ns_uid: NsUid) -> Content:
    content_list_wrapper = ContentListWrapper.model_validate(content_list)
    if content_list_wrapper.valid:
        for item in cast(list[Content], content_list_wrapper.content):
            if item.ns_uid == ns_uid:
                return item

    raise ValueError("Invalid content list")


def _get_discount_groups(raw_discount_group_response: JSON) -> list[DiscountGroup]:
    return DiscountGroupResponse.model_validate(
        raw_discount_group_response
    ).discount_groups


class _PriceOut(TypedDict):
    price_start_datetime: str
    price_end_datetime: str
    regular_price: str


def _get_price_for_discount(
    online_price_response: OnlinePriceResponse,
    target_titles_response: TargetTitlesResponse,
    price_id: str,
) -> _PriceOut:
    prices_per_nsuid: list[Price | TargetTitlesPrice] = [
        price
        for online_price in online_price_response.online_prices
        if online_price.data_source == DATA_SOURCE
        for price in online_price.prices
        if price.price_id == price_id
    ]

    if target_titles_response.contents:
        prices_per_nsuid.extend([
            price
            for content in target_titles_response.contents
            for price in content.prices
            if price.price_id == price_id
        ])
    else:
        log.info("No contents in target titles response")

    if not prices_per_nsuid:
        raise ValueError(f"Could not find price with id: {price_id}")

    result = max(prices_per_nsuid, key=lambda price: price.start_datetime)

    return {
        "price_start_datetime": result.start_datetime,
        "price_end_datetime": result.end_datetime or "",
        "regular_price": result.amount,
    }
