import logging
import random
from collections.abc import Sequence
from datetime import date

import httpx

from app.core.exceptions import NoSessionException, SessionExpiredException
from app.core.mfa import MFA
from app.core.scraper import (
    CSSSelector,
    ManualLoginDetails,
    Organization,
    ReportZip,
    ScrapeInfo,
    Scraper,
    SessionIdentifier,
)
from app.core.sessions import HTTPSession
from app.core.types import JSON, DictJSON, ListJSON
from app.nintendo.discounts.api import API
from app.nintendo.discounts.converters import to_csv
from app.nintendo.discounts.pages.discount_search_page import DiscountSearchPage
from app.nintendo.discounts.pages.discounts_info_page import DiscountInfoPage
from app.nintendo.discounts.types import NsUid, SubmissionId
from app.nintendo.login import NintendoCredentials, login

log = logging.getLogger(__name__)


def generate_timezone_cookie(session_identifier: str) -> dict[str, str]:
    return {
        "name": f"timeZone_{session_identifier}",
        "value": "UTC",
        "domain": "mng.nintendo.net",
    }


class NintendoDiscountsScraper(Scraper):
    max_mfa_attempts: int = 1

    manual_login_details = ManualLoginDetails(
        url=DiscountSearchPage.URL,
        success_selector=CSSSelector(value=DiscountSearchPage.LOGIN_SUCCESS_SELECTOR),
    )

    def __init__(
        self,
        session: HTTPSession,
        credentials: NintendoCredentials,
        mfa: MFA,
        http: httpx.Client,
    ) -> None:
        self._credentials = credentials
        self._mfa: MFA = mfa
        self._session = session
        self._http = http

    def login(self) -> None:
        login(
            credentials=self._credentials,
            http=self._http,
            mfa=self._mfa,
        )

    def check_session(self) -> SessionIdentifier:
        log.info("Checking session")
        if not self._session.has_required_cookies():
            raise NoSessionException

        search_page = DiscountSearchPage(self._http)
        session_identifier = search_page.get_session_identifier()
        self._http.cookies.set(**generate_timezone_cookie(session_identifier))
        csrf_token = search_page.get_csrf_token()
        submissions: list[SubmissionId] = search_page.get_all_submission_ids()

        if len(submissions) == 0:
            log.info("No discount submissions found")
            return SessionIdentifier(id=session_identifier)

        random_submission_id = random.choice(submissions)  # noqa: S311
        log.info(
            "Validating randomly selected discount submission %s", random_submission_id
        )
        api = API(self._http)
        for group_id in DiscountInfoPage(
            self._http, random_submission_id
        ).get_discount_group_ids():
            api.get_group_discounts(csrf_token, group_id)
        session_identifier = search_page.get_session_identifier()
        return SessionIdentifier(id=session_identifier)

    def scrape(
        self, from_: date, to: date, report_zip: ReportZip, excluded_skus: list[str]
    ) -> ScrapeInfo:
        log.info("Scraping Nintendo Discounts from %s to %s", from_, to)

        main_search_page = DiscountSearchPage(self._http)
        session_identifier = main_search_page.get_session_identifier()
        self._http.cookies.set(**generate_timezone_cookie(session_identifier))

        csrf_token = main_search_page.get_csrf_token()
        api = API(self._http)

        prices: dict[NsUid, JSON] = {}
        target_titles: dict[NsUid, JSON] = {}
        discount_groups: dict[SubmissionId, list[JSON]] = {}
        sale_names: dict[SubmissionId, str] = {}
        content_lists: dict[SubmissionId, DictJSON] = {}
        country_currency_map: ListJSON = []

        discount_records = main_search_page.get_all_discount_records()
        total_records = len(discount_records)

        for index, record in enumerate(discount_records, 1):
            try:
                log.info(
                    "[%d/%d] Processing discount %s (%s) starting at %s for region %s",
                    index,
                    total_records,
                    record.submission_id,
                    record.sale_name,
                    record.start_datetime,
                    record.region,
                )
                log.info("Creating info_page")
                info_page = DiscountInfoPage(self._http, record.submission_id)
                country_currency_map = (
                    country_currency_map or info_page.get_country_currency_mapping()
                )
                sale_names[record.submission_id] = info_page.get_sale_name()
                content_lists[record.submission_id] = (
                    info_page.get_discount_content_list()
                )
                log.info("Getting content types")
                content_types = {
                    content["nsUid"]: content["contentType"]
                    for content in content_lists[record.submission_id]["content"]
                }
                group_ids = info_page.get_discount_group_ids()
                log.info(
                    "Found %s discount groups for submission %s",
                    len(group_ids),
                    record.submission_id,
                )
                log.info("Getting group discounts")
                discount_groups[record.submission_id] = [
                    api.get_group_discounts(csrf_token, group_id)
                    for group_id in group_ids
                ]
                prices.update({
                    ns_uid: api.get_prices(csrf_token, ns_uid, content_types[ns_uid])
                    for ns_uid in info_page.get_ns_uids()
                })

                for ns_uid in info_page.get_ns_uids():
                    if ns_uid not in target_titles and record.start_datetime:
                        target_titles[ns_uid] = api.get_target_titles(
                            csrf_token,
                            record.submission_id,
                            ns_uid,
                            content_types[ns_uid],
                            record.start_datetime,
                            info_page.get_region_code(),
                        )

            except httpx.HTTPStatusError as e:
                if e.response.status_code not in [403, 401]:  # forbidden, unauthorized
                    raise e
                log.exception(
                    "Failed to scrape submission %s. Error: %s", record.submission_id, e
                )
            except SessionExpiredException as e:
                log.exception(
                    "Failed to scrape submission %s. Error: %s", record.submission_id, e
                )

        report_zip.add_json_file(list(prices.values()), "prices.json", from_, to)
        report_zip.add_json_file(
            list(content_lists.values()), "content_lists.json", from_, to
        )
        report_zip.add_json_file(
            list(discount_groups.values()), "discount_groups.json", from_, to
        )
        report_zip.add_json_file(
            country_currency_map, "country_currency_mapping.json", from_, to
        )

        try:
            csv = to_csv(
                discount_groups,
                prices,
                target_titles,
                sale_names,
                content_lists,
                country_currency_map,
            )
            report_zip.add_csv_file(csv, "discounts.csv", from_, to, raw_data=False)
        except Exception as e:  # pylint: disable=broad-except
            log.exception(
                "Failed to convert JSONs to CSV. Only raw JSON files will be uploaded. Error: %s",
                e,
            )
            pass

        return ScrapeInfo(no_data=len(discount_records) == 0)

    def get_organizations(self) -> Sequence[Organization]:
        log.info("Getting organizations")
        return []
