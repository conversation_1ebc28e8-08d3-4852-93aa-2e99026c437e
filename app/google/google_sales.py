# Standard libraries
import logging
import shutil
import zipfile
from collections.abc import Sequence
from datetime import date, datetime
from pathlib import Path

# Third party libraries
from google.api_core.exceptions import Forbidden, NotFound, Unauthorized

# pyright: reportMissingTypeStubs=false
from google.auth.exceptions import RefreshError
from google.cloud import storage
from pydantic import Field

# Current project files
from app.core.exceptions import (
    InvalidCredentialsException,
    MissingPermissionsException,
)
from app.core.scraper import (
    CSSSelector,
    ManualLoginDetails,
    Organization,
    ReportZip,
    ScrapeInfo,
    Scraper,
    SessionIdentifier,
)
from app.core.sessions import Session
from app.util.camel_case_model import CamelCaseModel
from app.util.daterange import current_date, google_date_range, last_day_of_date

log = logging.getLogger(__name__)
logging.basicConfig(level=logging.DEBUG)


class ManifestFileFields(CamelCaseModel):
    vendor_id: str
    date_from: date
    date_to: date


class ConfigJsonFields(CamelCaseModel):
    type: str
    project_id: str
    private_key_id: str
    private_key: str
    client_email: str
    client_id: str
    auth_uri: str
    token_uri: str
    auth_provider_x509_cert_url: str
    client_x509_cert_url: str
    universe_domain: str


class GoogleSalesSession(Session):
    config_json: ConfigJsonFields | None = None
    cloud_storage_bucket: str | None = Field(
        default=None,
        pattern=r"pubsite_(prod)?_(rev_)?\d{1,}",
    )


class GoogleSalesCredentials(CamelCaseModel):
    config_json: ConfigJsonFields | None = None
    cloud_storage_bucket: str | None = Field(
        default=None,
        pattern=r"pubsite_(prod)?_(rev_)?\d{1,}",
    )


class GoogleSalesScraper(Scraper):
    manual_login_details = ManualLoginDetails(
        url="https://no-url-is-needed-here.ndbi.or.not/",
        success_selector=CSSSelector(value="#not-needed"),
    )

    def __init__(
        self,
        credentials: GoogleSalesCredentials,
        session: GoogleSalesSession,
    ) -> None:
        self._credentials = credentials
        self._session = session

    def get_organizations(self) -> Sequence[Organization]:
        assert self._session.config_json  # noqa: S101
        id_and_name = self._session.config_json.project_id
        return [Organization(id=id_and_name, name=id_and_name)]

    def check_session(self) -> SessionIdentifier:
        assert self._session.config_json  # noqa: S101
        return SessionIdentifier(id=self._session.config_json.client_id)

    def _rewrite_credentials_to_session(self):
        self._session.config_json = self._credentials.config_json
        self._session.cloud_storage_bucket = self._credentials.cloud_storage_bucket

    def login(self):
        assert self._credentials.cloud_storage_bucket  # noqa: S101
        assert self._credentials.config_json  # noqa: S101
        log.info("Login")
        self._rewrite_credentials_to_session()

    def _setup_storage_client(self) -> storage.Client:
        assert self._session.config_json  # noqa: S101

        storage_client = storage.Client.from_service_account_info(
            self._session.config_json.model_dump()
        )
        log.info("Storage client set up properly.")
        return storage_client

    def _setup_bucket(self) -> storage.Bucket:
        try:
            client = self._setup_storage_client()
            bucket = client.get_bucket(self._session.cloud_storage_bucket)
            log.info("Bucket set up properly.")
            return bucket
        except Forbidden as err:
            log.error("Not enough permissions.")
            raise MissingPermissionsException from err
        except (RefreshError, Unauthorized, NotFound) as err:
            log.error(
                "Incorrect credentials: it might be a invalid bucket name or key was removed"
            )
            raise InvalidCredentialsException from err

    def get_sales_blobs(self, bucket: storage.Bucket) -> list[storage.Blob]:
        blobs = bucket.list_blobs()
        sales_blobs = [blob for blob in blobs if blob.name.startswith("sales")]
        return sales_blobs

    def select_blobs(
        self, blobs: list[storage.Blob], date_from: date, date_to: date
    ) -> list[storage.Blob]:
        date_range = google_date_range(date_from=date_from, date_to=date_to)
        selected_blobs = []

        for blob in blobs:
            blob_date = blob.name.split("_")[1].split(".")[0]

            if blob_date in date_range:
                selected_blobs.append(blob)

        return selected_blobs

    def download_and_save_data(self, blobs: list[storage.Blob]):
        for blob in blobs:
            log.info("Downloading %s", blob.name)
            fullpath = f"{self.temp_main_dir_with_reports}/{blob.name}"
            blob.download_to_filename(fullpath)

    def _setup_paths_for_temp_dir(self, base_path: Path):
        self.temp_main_dir_with_reports = base_path / "tmp_google_sales_reports"
        self.temp_dir_with_reports = self.temp_main_dir_with_reports / "sales"

    def _create_temp_dir_for_reports(self):
        Path(self.temp_dir_with_reports).mkdir(parents=True, exist_ok=True)
        log.info("Created temp directory for reports: %s", self.temp_dir_with_reports)

    def _delete_temp_dir_for_reports(self):
        shutil.rmtree(self.temp_main_dir_with_reports, ignore_errors=True)
        log.info("Deleted temp directory for reports: %s", self.temp_dir_with_reports)

    def _prepare_csv_filenames(self, blobs: list[storage.Blob]):
        self.csv_filenames: dict[str, str] = {}

        for blob in blobs:
            csv_filename: str = blob.name.split("/")[1].replace("zip", "csv")
            self.csv_filenames[blob.name] = csv_filename

    def _get_correct_date_to(self, date_to: date) -> date:
        date_now = current_date()
        if date_to.year == date_now.year and date_to.month == date_now.month:
            return date_now
        return last_day_of_date(date_=date_to)

    def _get_dates_for_csv_file(self, csv_filename: str) -> dict[str, date]:
        """Returns a dict of date_from and date_to for csv filename.

        Args:
            csv_filename (str): filename in format ANYNAME_%Y%m.csv

        Returns:
            dict[str, date]: {"date_from": date_object, "date_to": date_object}
        """
        csv_date_str = csv_filename.split("_")[1].split(".")[0]
        csv_date_from = datetime.strptime(csv_date_str, "%Y%m").date()
        csv_date_to = self._get_correct_date_to(date_to=csv_date_from)
        to_return = {"date_from": csv_date_from, "date_to": csv_date_to}

        return to_return

    def move_downloaded_files_to_zip(
        self, blobs: list[storage.Blob], report_zip: ReportZip[ManifestFileFields]
    ):
        assert self._session.config_json  # noqa: S101
        for blob in blobs:
            downloaded_file_path = f"{self.temp_main_dir_with_reports}/{blob.name}"
            csv_filename = self.csv_filenames[blob.name]
            dates_for_csv = self._get_dates_for_csv_file(csv_filename=csv_filename)

            with (
                zipfile.ZipFile(downloaded_file_path, "r") as zf,
                zf.open(csv_filename) as cf,
            ):
                csv_data = cf.read()

            with report_zip.create_report(
                file_name=csv_filename,
                meta_data=ManifestFileFields(
                    vendor_id=self._session.config_json.client_id, **dates_for_csv
                ),
            ) as singe_report:
                singe_report.write(csv_data)

            log.info(
                "CSV file: %s from %s downloaded ZIP saved to the end report.",
                csv_filename,
                downloaded_file_path,
            )

    def scrape(
        self,
        from_: date,
        to: date,
        report_zip: ReportZip[ManifestFileFields],
        excluded_skus: list[str],
    ) -> ScrapeInfo:
        assert self._session.config_json  # noqa: S101

        bucket = self._setup_bucket()
        sales_blobs = self.get_sales_blobs(bucket=bucket)
        blobs_to_download = self.select_blobs(
            blobs=sales_blobs, date_from=from_, date_to=to
        )

        self._prepare_csv_filenames(blobs=blobs_to_download)
        self._setup_paths_for_temp_dir(base_path=report_zip.report_path)
        self._delete_temp_dir_for_reports()
        self._create_temp_dir_for_reports()
        self.download_and_save_data(blobs=blobs_to_download)
        self.move_downloaded_files_to_zip(
            blobs=blobs_to_download, report_zip=report_zip
        )
        self._delete_temp_dir_for_reports()

        return ScrapeInfo(no_data=not bool(blobs_to_download))
