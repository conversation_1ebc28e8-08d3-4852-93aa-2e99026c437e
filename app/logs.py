import collections
import logging
import logging.config
import os
import sys
import threading
import time
from contextlib import contextmanager
from copy import deepcopy
from typing import Any, Literal

import logging_json

import app.version
from app.core.exceptions import ErrorType, ScraperException
from app.util.logging.telemetry_type_filter import FilterSensitiveInfo

log = logging.getLogger(__name__)

_THREAD_LOCAL = threading.local()

_THREAD_LOCAL.pids = collections.defaultdict(dict)


def _except_logging(exc_type, exc_value, exc_traceback):  # pylint: disable=unused-argument
    log = logging.getLogger(__name__)
    log.exception(str(exc_value), exc_info=exc_value)  # noqa: LOG004


def _unraisable_logging(
    exc_type, exc_value=None, exc_traceback=None, err_msg=None, obj=None
):  # pylint: disable=unused-argument
    log = logging.getLogger(__name__)
    log.exception(str(exc_value), exc_info=exc_value)  # noqa: LOG004


def _threading_except_logging(
    exc_type, exc_value=None, exc_traceback=None, thread=None
):  # pylint: disable=unused-argument
    log = logging.getLogger(__name__)
    log.exception(str(exc_value), exc_info=exc_value)  # noqa: LOG004


def _init_extra():
    if not hasattr(_THREAD_LOCAL, "pids"):
        _THREAD_LOCAL.pids = collections.defaultdict(dict)


def _get_extra():
    _init_extra()
    pid = os.getpid()
    return _THREAD_LOCAL.pids[pid]


def _set_extra(extra):
    _init_extra()
    pid = os.getpid()
    _THREAD_LOCAL.pids[pid] = extra
    return _THREAD_LOCAL.pids[pid]


@contextmanager
def add_logging_context(**kwargs):
    new_extra = kwargs
    old_extra = _get_extra()
    _set_extra({**old_extra, **new_extra})
    yield
    _set_extra(old_extra)


def get_logging_context():
    return deepcopy(_get_extra())


class ContextFilter(logging.Filter):
    """
    Implements additional attributes set by 'add_logging_context' function.
    """

    LEVEL_MAP = {
        "DEBUG": 1,
        "INFO": 1,
        "WARNING": 2,
        "ERROR": 3,
        "CRITICAL": 3,
    }

    def filter(self, record) -> Literal[True]:
        context = _get_extra()
        for key, value in context.items():
            setattr(record, key, value)

        record.version = 2

        record.metadata = {
            "function_name": record.funcName,
            "line_no": record.lineno,
            "logger_name": record.name,
            "level_name": record.levelname,
        }

        record.logLevel = self.LEVEL_MAP[record.levelname]

        if record.exc_info is not None:
            record.errorType = (
                record.exc_info[0].error_type
                if record.exc_info[0] is not None
                and issubclass(record.exc_info[0], ScraperException)
                else ErrorType.UNEXPECTED_ERROR
            )

        if not hasattr(record, "type"):
            record.type = "trace" if record.levelname == "DEBUG" else "output"

        return True


def _set_global_context():
    _set_extra({
        "originId": f"Scraper-py-{app.version.__version__}",
    })


class CustomJSONFormatter(logging_json.JSONFormatter):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.converter = time.gmtime
        self.exception_field_name = None  # Disable exception field in logs


def configure_logger() -> None:
    logging_config: dict[str, Any] = {
        "version": 1,
        "formatters": {
            "json": {
                "()": CustomJSONFormatter,
                "default_time_format": "%Y-%m-%dT%H:%M:%S",
                "default_msec_format": "%s.%03dZ",
                "fields": {
                    "timestamp": "asctime",
                    "metadata": "metadata",
                    "message": "message",
                },
            },
            "text": {
                "format": "%(asctime)s - %(message)s",
                "datefmt": "%Y-%m-%d %H:%M:%S",
            },
        },
        "filters": {
            "context": {
                "()": "app.logs.ContextFilter",
            }
        },
        "handlers": {
            "standard_output": {
                "class": "logging.StreamHandler",
                "formatter": "json",
                "stream": "ext://sys.stdout",
                "filters": ["context"],
            },
        },
        "loggers": {
            "__main__": {"level": "DEBUG"},
            "app": {"level": "DEBUG"},
        },
        "root": {"level": "DEBUG", "handlers": ["standard_output"]},
    }

    logging.config.dictConfig(logging_config)

    sys.excepthook = _except_logging
    sys.unraisablehook = _unraisable_logging
    threading.excepthook = _threading_except_logging
    add_filter_to_all_loggers(FilterSensitiveInfo())

    _set_global_context()


def add_filter_to_all_loggers(filter_to_add: logging.Filter):
    # Access the root logger
    root_logger = logging.getLogger()
    # Add the filter_to_add to all handlers of the root logger
    for handler in root_logger.handlers:
        handler.addFilter(filter_to_add)
    # Access all other loggers
    for logger_instance in logging.Logger.manager.loggerDict.items():
        if isinstance(logger_instance, logging.Logger):
            for handler in logger_instance.handlers:
                handler.addFilter(filter_to_add)
