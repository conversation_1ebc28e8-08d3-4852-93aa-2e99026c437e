import logging
import traceback
from typing import Annotated, Any

# Third party libraries
from pydantic import BaseModel, ConfigDict, Field, alias_generators

from app.core.exceptions import ScraperException
from app.core.portal import Portal
from app.core.source import Source
from app.util.logging.sensitive_data_filter import filter_sensitive_string

log = logging.getLogger(__name__)


def send_result(result: Any):
    log.info(
        "",
        extra={
            "type": "result",
            "data": result,
        },
    )


class MFARequest(BaseModel):
    model_config = ConfigDict(
        populate_by_name=True,
        alias_generator=alias_generators.to_snake,
    )

    source: Source
    attempt: int = 1
    max_attempts: Annotated[int, Field(serialization_alias="maxAttempts")] = 1


def send_mfa_request(request: MFARequest):
    log.info(
        msg=None,
        extra={
            "type": "dualAuth",
            **request.model_dump(by_alias=True),
        },
    )


def send_exception(error: Exception | ScraperException):
    # logging exception as output entry
    log.exception(error)  # noqa: LOG004

    # building error entry
    error_traceback = filter_sensitive_string(
        "\n".join(traceback.format_tb(error.__traceback__))
    )
    extras = {
        "type": "error",
        "data": {
            "message": str(error),
            "stack": error_traceback,
        },
    }

    error_message = traceback.format_exception(error, limit=0)[0].strip().strip("\n")
    log.exception(  # noqa: LOG004
        error_message,
        extra=extras,
    )
