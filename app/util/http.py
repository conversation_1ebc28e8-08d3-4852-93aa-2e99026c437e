import httpx

from app.core.sessions import <PERSON><PERSON>, HTTPSession


def http_from_session(
    session: HTTPSession | None, proxy_url: str | None
) -> httpx.Client:
    """Create an httpx.Client configured with optional cookies and proxy.

    - Copies cookies from HTTPSession if provided.
    - Configures HTTP(S) proxy if proxy_url is provided.
    """
    proxies = None
    if proxy_url:
        # Use the same proxy for both http and https (httpx expects full scheme keys)
        proxies = {"http://": proxy_url, "https://": proxy_url}

    client = httpx.Client(proxies=proxies)

    if session:
        for cookie in session.cookies:
            client.cookies.set(
                cookie.name,
                cookie.value,
                domain=cookie.domain,
                path=cookie.path,
            )

    return client


def update_session(http: httpx.Client, session: HTTPSession):
    # Iterate over the underlying CookieJar to access Cookie objects
    session.cookies = [
        Cookie(
            name=c.name,
            value=c.value or "",
            domain=c.domain or "",
            path=c.path or "/",
            expires=c.expires if c.expires else -1,
        )
        for c in http.cookies.jar  # type: ignore[attr-defined]
    ]


class ProxyAdapter:  # Backward-compatibility placeholder for tests
    """No-op placeholder retained for compatibility with tests importing ProxyAdapter.

    httpx handles proxies via the `proxies` parameter on Client; if custom
    proxy behavior is required, consider using event hooks or a custom transport.
    """

    pass
