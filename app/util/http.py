import requests
import requests.adapters

from app.core.sessions import <PERSON><PERSON>, HTTPSession


def http_from_session(session: HTTPSession | None, proxy_url: str | None):
    http = requests.Session()

    if proxy_url:
        base_adapter = http.get_adapter("http://")
        proxy_adapter = ProxyAdapter(base_adapter, proxy_url)
        http.mount("http://", proxy_adapter)
        http.mount("https://", proxy_adapter)

    if session:
        for cookie in session.cookies:
            http.cookies.set(
                cookie.name,
                cookie.value,
                domain=cookie.domain,
                path=cookie.path,
                expires=None if cookie.expires == -1 else cookie.expires,
            )

    return http


def update_session(http: requests.Session, session: HTTPSession):
    session.cookies = [
        Cookie(
            name=cookie.name,
            value=cookie.value or "",
            domain=cookie.domain,
            path=cookie.path,
            expires=cookie.expires if cookie.expires else -1,
        )
        for cookie in http.cookies
    ]


class ProxyAdapter(requests.adapters.BaseAdapter):
    def __init__(
        self, base_adapter: requests.adapters.BaseAdapter, proxy_url: str
    ) -> None:
        super().__init__()
        self._base_adapter = base_adapter

        if proxy_url.startswith("http://"):
            proxy_url = proxy_url[len("http://") :]

        self._proxy_host = proxy_url

    def send(
        self,
        request: requests.PreparedRequest,
        stream=False,
        timeout=None,
        verify=True,
        cert=None,
        proxies=None,
    ):
        assert request.url  # noqa: S101

        original_url = request.url
        rewritten_url = None

        if original_url.startswith("https://"):
            rewritten_url = "http://" + original_url[len("https://") :]
            request.url = rewritten_url
            request.headers["x-ndbi-proxy-protocol"] = "https"

        response = self._base_adapter.send(
            request, stream, timeout, verify, cert, {"http": self._proxy_host}
        )
        request.url = original_url
        if response.url == rewritten_url:
            response.url = original_url

        return response

    def close(self) -> None:
        return self._base_adapter.close()
