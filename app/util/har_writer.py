"""
Simple HAR (HTTP Archive) writer replacement for httpx.
This module provides a memory-efficient HAR writer that appends entries incrementally.
"""

import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

import httpx

log = logging.getLogger(__name__)


class HarEntry:
    """Represents a single HAR entry for an HTTP request/response pair."""
    
    def __init__(self, request: httpx.Request, response: httpx.Response, started_datetime: datetime):
        self.request = request
        self.response = response
        self.started_datetime = started_datetime
        self.time = 0  # Will be calculated when response is received
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert HAR entry to dictionary format."""
        return {
            "startedDateTime": self.started_datetime.isoformat() + "Z",
            "time": self.time,
            "request": self._request_to_dict(),
            "response": self._response_to_dict(),
            "cache": {},
            "timings": {
                "send": 0,
                "wait": self.time,
                "receive": 0
            }
        }
    
    def _request_to_dict(self) -> Dict[str, Any]:
        """Convert request to HAR format."""
        return {
            "method": self.request.method,
            "url": str(self.request.url),
            "httpVersion": "HTTP/1.1",
            "headers": [
                {"name": name, "value": value}
                for name, value in self.request.headers.items()
            ],
            "queryString": [
                {"name": key, "value": value}
                for key, value in self.request.url.params.items()
            ],
            "postData": self._get_post_data() if self.request.content else {},
            "headersSize": -1,
            "bodySize": len(self.request.content) if self.request.content else 0
        }
    
    def _response_to_dict(self) -> Dict[str, Any]:
        """Convert response to HAR format."""
        return {
            "status": self.response.status_code,
            "statusText": self.response.reason_phrase or "",
            "httpVersion": "HTTP/1.1",
            "headers": [
                {"name": name, "value": value}
                for name, value in self.response.headers.items()
            ],
            "content": {
                "size": len(self.response.content),
                "mimeType": self.response.headers.get("content-type", ""),
                "text": self._get_response_text()
            },
            "redirectURL": "",
            "headersSize": -1,
            "bodySize": len(self.response.content)
        }
    
    def _get_post_data(self) -> Dict[str, Any]:
        """Get POST data in HAR format."""
        if not self.request.content:
            return {}
        
        content_type = self.request.headers.get("content-type", "")
        
        return {
            "mimeType": content_type,
            "text": self.request.content.decode("utf-8", errors="ignore")
        }
    
    def _get_response_text(self) -> str:
        """Get response text, handling encoding safely."""
        try:
            return self.response.text
        except Exception:
            return self.response.content.decode("utf-8", errors="ignore")


class SimpleHarWriter:
    """
    A simple, memory-efficient HAR writer that appends entries incrementally.
    Unlike requests-har, this doesn't load everything into memory.
    """
    
    def __init__(self, file_path: Optional[Path] = None):
        self.file_path = file_path
        self.entries: List[HarEntry] = []
        self._initialized = False
    
    def on_request(self, request: httpx.Request) -> None:
        """Called when a request is about to be sent."""
        # Store request start time for timing calculations
        request._har_start_time = datetime.utcnow()
    
    def on_response(self, response: httpx.Response) -> None:
        """Called when a response is received."""
        if not hasattr(response.request, '_har_start_time'):
            return
        
        start_time = response.request._har_start_time
        end_time = datetime.utcnow()
        
        entry = HarEntry(response.request, response, start_time)
        entry.time = int((end_time - start_time).total_seconds() * 1000)
        
        self.entries.append(entry)
        
        # If file path is set, append immediately to avoid memory buildup
        if self.file_path:
            self._append_entry_to_file(entry)
    
    def _append_entry_to_file(self, entry: HarEntry) -> None:
        """Append a single entry to the HAR file."""
        if not self.file_path:
            return
        
        try:
            # Initialize file if needed
            if not self._initialized:
                self._initialize_har_file()
                self._initialized = True
            
            # Read existing content, add entry, write back
            # This is not the most efficient for large files, but works for our use case
            self._add_entry_to_existing_file(entry)
            
        except Exception as e:
            log.warning(f"Failed to append HAR entry to file: {e}")
    
    def _initialize_har_file(self) -> None:
        """Initialize the HAR file with basic structure."""
        if not self.file_path:
            return
        
        har_structure = {
            "log": {
                "version": "1.2",
                "creator": {
                    "name": "scrapers-py",
                    "version": "1.0"
                },
                "entries": []
            }
        }
        
        self.file_path.parent.mkdir(parents=True, exist_ok=True)
        with open(self.file_path, 'w', encoding='utf-8') as f:
            json.dump(har_structure, f, indent=2)
    
    def _add_entry_to_existing_file(self, entry: HarEntry) -> None:
        """Add entry to existing HAR file."""
        if not self.file_path or not self.file_path.exists():
            return
        
        try:
            # Read existing HAR
            with open(self.file_path, 'r', encoding='utf-8') as f:
                har_data = json.load(f)
            
            # Add new entry
            har_data["log"]["entries"].append(entry.to_dict())
            
            # Write back
            with open(self.file_path, 'w', encoding='utf-8') as f:
                json.dump(har_data, f, indent=2)
                
        except Exception as e:
            log.warning(f"Failed to update HAR file: {e}")
    
    def save(self, file_path: Path) -> None:
        """Save all collected entries to a HAR file."""
        har_structure = {
            "log": {
                "version": "1.2",
                "creator": {
                    "name": "scrapers-py",
                    "version": "1.0"
                },
                "entries": [entry.to_dict() for entry in self.entries]
            }
        }
        
        file_path.parent.mkdir(parents=True, exist_ok=True)
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(har_structure, f, indent=2)
        
        log.info(f"Saved HAR file with {len(self.entries)} entries to {file_path}")


# Compatibility alias for existing code
HarDict = SimpleHarWriter
