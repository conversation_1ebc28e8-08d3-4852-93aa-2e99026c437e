import os
from datetime import date

from cryptography.fernet import Fernet
from slugify import slugify

from app.core.source import Source


def format_date(date_: date) -> str:
    return date_.strftime("%Y-%m-%d")


def generate_report_file_name(
    source: Source,
    start_date: date,
    end_date: date,
    extension: str,
    suffix: str | None = None,
) -> str:
    suffix = f"-{slugify(suffix, lowercase=False)}" if suffix is not None else ""

    return f"{source}-{start_date}-{end_date}{suffix}.{extension}"


def remove_quotation(text: str) -> str:
    unwanted_characters = "'\"`"
    return text.strip(unwanted_characters)


def crypt_text(text: str | bytes, encrypt: bool = True) -> str:
    f = Fernet(os.environ["SCP_CREDENTIALS_ENCRYPTION_KEY"])
    text_bytes = text.encode() if isinstance(text, str) else text

    result = f.encrypt(text_bytes) if encrypt else f.decrypt(text_bytes)

    return result.decode()
