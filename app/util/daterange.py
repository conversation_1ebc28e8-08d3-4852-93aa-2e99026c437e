# Standard libraries
import calendar
from collections.abc import Iterator
from datetime import date, datetime, timedelta


def daterange(date_from: date, date_to: date) -> Iterator[date]:
    for n in range(int((date_to - date_from).days + 1)):
        yield date_from + timedelta(n)


def google_date_range(date_from: date, date_to: date) -> list[str]:
    dates: set[str] = set()

    while date_from <= date_to:
        date_str = date_from.strftime("%Y%m")
        dates.add(date_str)
        date_from += timedelta(days=1)

    return sorted(dates)


def first_day_of_date(date_: date) -> date:
    return date_.replace(day=1)


def last_day_of_date(date_: date) -> date:
    last_day = calendar.monthrange(date_.year, date_.month)[1]
    date_str = f"{date_.year}-{date_.month}-{last_day}"
    return datetime.strptime(date_str, "%Y-%m-%d").date()


def current_date() -> date:
    return date.today()
