# flake8: noqa: ERA001
import logging

from app.core.exceptions import SessionExpiredException
from app.util import messaging

log = logging.getLogger(__name__)


def example_logs():
    log.info("Example info log")
    log.debug("Example debug log")
    log.warning("Example warning log")
    log.error("Example error log")

    log.exception("Example exception log")  # noqa: LOG004

    log.critical("Example critical log")

    log.info("Example info with extra param", extra={"extra_param": "example"})

    try:
        raise SessionExpiredException("Example exception")
    except Exception as e:
        messaging.send_exception(e)

        log.error("error log from except block")
        log.error("error log from except block, with exec_info=True", exc_info=True)
        log.exception("exception log from except block")
        log.exception(
            "exception log from except block, with error as extra param",
            extra={"error": e},
        )
        log.exception(
            "exception log from except block, with exec_info=True", exc_info=True
        )

        log.critical("critical log from except block")
        log.critical(
            "critical log from except block, with error as extra param",
            extra={"error": e},
        )
        log.critical(
            "critical log from except block, with exec_info=True", exc_info=True
        )

    messaging.send_exception(SessionExpiredException())
    messaging.send_result({"payload": "example-payload"})
