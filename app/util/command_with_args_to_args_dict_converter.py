import re
from collections.abc import Sequence
from copy import deepcopy
from itertools import pairwise
from typing import Any


class UnparsableArgsException(Exception):
    pass


_PAIR_PATTERN = re.compile("^(.+?)=(.+)$")
_OPTIONS_PATTERN = re.compile("^--(.+)$")


class CommandWithArgsToArgsDictConverter:
    """We want to be able to parse arguments no matter if the args
    table looks like: ["command", "--someArg", "test", "--anotherArg", "test2"]
    or ["command", "--someArg==test", "--anotherArg==test2"]. This converter
    returns a dictionary of argument names matched to argument values in both
    cases. Assumes first element of the qiven sequence is always a command name.
    It does not handle boolean flag without values.
    """

    def __init__(self, seq: Sequence[str]):
        if len(seq) <= 1:
            self.sequence = None
        else:
            self.sequence = seq[1:]

    def get(self) -> dict[str, str]:
        if not self.sequence:
            return {}

        sample_arg = self.sequence[0]
        if _PAIR_PATTERN.match(sample_arg):
            return self._handle_pairs()
        elif _OPTIONS_PATTERN.match(sample_arg):
            return self._handle_options()
        else:
            raise UnparsableArgsException

    def _handle_pairs(self) -> dict[str, str]:
        assert self.sequence  # noqa: S101
        result: dict[str, Any] = {}
        for arg_pair in self.sequence:
            matched = _PAIR_PATTERN.search(arg_pair)
            if not matched:
                raise UnparsableArgsException
            result[matched.group(1)] = matched.group(2)
        return result

    def _handle_options(self) -> dict[str, str]:
        assert self.sequence  # noqa: S101
        return dict(
            filter(
                lambda pair: pair[0].startswith("--"),
                pairwise(deepcopy(self.sequence)),
            )
        )
