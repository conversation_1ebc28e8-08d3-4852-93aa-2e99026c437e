TIMEOUT_SECONDS: int = 15


def wait_for_debugger():
    # to not import this on production
    # pylint: disable=import-outside-toplevel
    import threading

    import debugpy  # pyright: reportMissingTypeStubs=false

    threading.Timer(TIMEOUT_SECONDS, debugpy.wait_for_client.cancel()).start()
    # 5678 is the default attach port in the VS Code debug configurations.
    # Unless a host and port are specified, host defaults to 127.0.0.1
    debugpy.listen(5678)
    print("Waiting for debugger attach")  # noqa: T201
    debugpy.wait_for_client()
    debugpy.breakpoint()
