from bs4 import BeautifulSoup, Tag


def get_value(soup: BeautifulSoup, tag: str, identifier: dict[str, str]) -> str:
    return get_attribute(soup, tag, identifier, "value")


def get_attribute(
    soup: BeautifulSoup,
    tag: str,
    identifier: dict[str, str],
    attribute: str,
    allow_empty: bool = True,
) -> str:
    element = soup.find(tag, identifier)
    if element is None or not isinstance(element, Tag):
        raise ValueError(
            f"Element {tag} with identifier {identifier} not found or is not a Tag."
        )

    attr_value = element.get(attribute)

    if not isinstance(attr_value, str):
        raise ValueError(
            f"Attribute '{attribute}' for element {tag} with identifier {identifier} isn't a string"
        )

    if not allow_empty and not attr_value:
        raise ValueError(
            f"Attribute '{attribute}' for element {tag} with identifier {identifier} is empty."
        )
    return attr_value


def get_text_from_tag(tag: Tag | None, allow_empty: bool = True) -> str:
    if tag is None or (not allow_empty and not tag.text):
        raise ValueError("Tag not found. It")
    return tag.text
