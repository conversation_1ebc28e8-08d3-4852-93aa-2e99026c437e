# Any changes here should also be made in ScraperLib and Scrapers-js!!
from functools import singledispatch
from types import NoneType
from typing import Any

sensitive_parameters = {
    "apiToken",
    "apiSetupData",
    "clientId",
    "tenantId",
    "totpSecret",
    "cloudStorageBucket",
    "credentials",
    "api_token",
    "password",
    "api_setup_data",
    # "client_id", # TODO this breaks VCR invalid Microsoft login tests  # noqa: ERA001
    # "token", # TODO this breaks VCR invalid Microsoft login tests  # noqa: ERA001
    # the below request in particular
    # https://login.windows.net/common/oauth2/v2.0/authorize?client_id=xxxxxxx
    "tenant_id",
    "totp_secret",
    "cloud_storage_bucket",
    "cookies",
    "set-cookie",
    "clientSecret",
}


def filter_sensitive_string(message: str) -> str:
    for sensitive_param in sensitive_parameters:
        if sensitive_param in message:
            if sensitive_param == "credentials" and any(
                exception in message
                for exception in [
                    "MissingPermissionsException",
                    "InvalidCredentialsException",
                ]
            ):
                return message
            return "[REDACTED]"
    return message


@singledispatch
def filter_sensitive_data(properties: Any) -> Any:
    return {"message": "Redacted because of unhandled type"}


@filter_sensitive_data.register
def _(properties: list | set | tuple) -> list:
    return [filter_param_based_on_type(value) for value in properties]


@filter_sensitive_data.register
def _(properties: dict) -> dict:
    return {
        key: "[REDACTED]"
        if key in sensitive_parameters
        else filter_param_based_on_type(value)
        for key, value in properties.items()
    }


@filter_sensitive_data.register
def _(properties: bool | int | float | NoneType) -> Any:
    return properties


def filter_param_based_on_type(value):
    if isinstance(value, (list, set, tuple, dict)):
        return filter_sensitive_data(value)
    elif isinstance(value, str):
        return filter_sensitive_string(value)
    elif isinstance(value, (bool, int, float, NoneType)):
        return value
    return filter_sensitive_string(str(value))
