import logging
from typing import cast

from app.util.logging.sensitive_data_filter import (
    filter_sensitive_data,
    filter_sensitive_string,
)


class FilterSensitiveInfo(logging.Filter):
    def filter(self, record: logging.LogRecord) -> bool:
        record.msg = filter_sensitive_string(str(record.msg))

        args_values = cast(tuple, getattr(record, "args", ()))
        record.args = tuple(filter_sensitive_data(args_values))
        return True
