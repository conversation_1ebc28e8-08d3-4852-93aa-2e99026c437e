import enum
import functools
import logging
import typing
from collections.abc import Sequence
from datetime import date
from typing import Any, Callable, TypeVar, cast

import click
from pydantic import BaseModel, alias_generators
from pydantic.fields import FieldInfo

from app.core.exceptions import NonRetryableException
from app.version import __version__

log = logging.getLogger(__name__)

T = TypeVar("T", bound=BaseModel)
U = TypeVar("U")

# Used without a command as probe by scraperLib to decide if the dependency is working
_CLI_VERSION_OPTION_NAME = "--version"


class CLI:
    def __init__(self, envvar_prefix: str) -> None:
        self._envvar_prefix = envvar_prefix
        self._group = click.Group(
            invoke_without_command=True,
            callback=self.print_if_no_subcommand,
            params=[
                click.Option(
                    param_decls=[_CLI_VERSION_OPTION_NAME],
                    is_flag=True,
                    flag_value=True,
                )
            ],
        )

    @staticmethod
    @click.pass_context
    def print_if_no_subcommand(ctx: click.Context, version: bool):
        if ctx.invoked_subcommand:
            return
        if version:
            # pylint: disable=logging-fstring-interpolation
            log.info(f"Python scrapers - Version {__version__}")
        else:
            click.echo(ctx.get_help())

    def command(self, func: Callable[[T], None]):
        name = func.__name__.lower().replace("_", "-")
        help_ = func.__doc__

        model = cast(T, next(iter(func.__annotations__.values())))

        params: list[click.Parameter] = list(self._model_to_params(model))

        @functools.wraps(func)
        def parse_model(**kwargs: Any):
            args = model.model_validate(kwargs)
            func(args)

        cmd = click.Command(name=name, help=help_, params=params, callback=parse_model)
        self._group.add_command(cmd)

        return cmd

    def run(self, args: Sequence[str] | None = None):
        retries = 5
        for attempt in range(1, retries + 1):
            try:
                log.debug(f"Attempt {attempt} of {retries}")
                self._group(args, standalone_mode=False)
                break
            except NonRetryableException as e:
                log.warning("Non-retryable exception occurred", exc_info=e)
                log.error(e)  # TODO handle this better since the above does not work
                raise e
            except Exception as e:
                if attempt >= retries:
                    log.warning("Max retries reached")
                    log.error(
                        e
                    )  # TODO handle this better since the above does not work
                    raise e
                log.warning("Retrying after exception", exc_info=e)
                log.debug(e)  # TODO handle this better since the above does not work
                continue

    def _model_to_params(self, model: BaseModel):
        return (
            self._field_to_option(name, field)
            for name, field in model.model_fields.items()
        )

    def _field_to_option(self, field_name: str, field_info: FieldInfo):
        if field_info.alias:
            field_name = field_info.alias
        normalized_name = alias_generators.to_camel(field_name)
        option_name = "--" + normalized_name
        envvar_name = self._envvar_prefix + "_" + field_name.upper()

        option_type = str
        field_type = (
            t[0]
            if (t := typing.get_args(field_info.annotation))
            else field_info.annotation
        )
        if isinstance(field_type, type) and issubclass(field_type, enum.Enum):
            option_type = click.Choice([e.value for e in field_type])
        if field_type in (int, bool):
            option_type = field_type
        if field_type == date:
            option_type = click.DateTime(["%Y-%m-%d"])

        option = click.Option(
            [option_name, field_name],
            required=field_info.is_required(),
            type=option_type,
            help=field_info.description,
            is_flag=False,
            envvar=envvar_name,
            default=field_info.default,
        )
        return option
