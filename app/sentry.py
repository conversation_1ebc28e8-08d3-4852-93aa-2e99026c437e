import logging
from collections.abc import Sequence

import sentry_sdk
from sentry_sdk.integrations.logging import LoggingIntegration

import app.version
from app.arguments import SentryArguments
from app.core.exceptions import KnownExceptions

log = logging.getLogger(__name__)


def configure_sentry(args: Sequence[str] | None, context):
    sentry_params = SentryArguments.extract_from_raw_sequence(args)
    if sentry_params.enabled:
        log.debug("Sentry is enabled with env %s", sentry_params.environment)
        sentry_sdk.init(
            dsn=sentry_params.dsn,
            environment=sentry_params.environment,
            traces_sample_rate=1.0,
            release=app.version.__version__,
            send_default_pii=True,
            ignore_errors=[
                KnownExceptions,  # Ignore known exceptions that are used as communication
            ],
            shutdown_timeout=0,  # Don't wait for pending events to be sent at crash
            integrations=[
                LoggingIntegration(
                    event_level=logging.CRITICAL,  # Do not send error logs to sentry
                ),
            ],
        )
        # set both tags and context because I dont't what will be easier to use in search
        sentry_sdk.set_context("scraper", context)
        sentry_sdk.set_tags(context)
    else:
        log.debug("Sen<PERSON> is disabled")


def close_sentry():
    """
    Close Sentry client to ensure all events are sent before exiting.
    """
    client = sentry_sdk.get_client()
    if client is not None:
        client.close()  # Close the Sentry client to ensure all events are sent
