"""
Code for accessing the Reporter API, an unofficial Apple API intended for use
with their command-line app Reporter:
# https://help.apple.com/itc/appsreporterguide/#/itc469b4b7eb

Loosely based on Reporter's documentation and reverse engineering, as well as open source
clones of Reporter, such as:  https://github.com/fedoco/itc-reporter
"""

import json
import logging
import urllib.parse
import xml.etree.ElementTree as ET  # noqa: S405
from datetime import date, datetime

import httpx
from retry import retry

from app.core.exceptions import (
    NoSalesDataException,
    ScraperException,
    TemporaryApiIssueException,
    TryAgainLaterException,
)

BASE_URL = "https://reportingitc-reporter.apple.com/reportservice/sales/v1"

log = logging.getLogger(__name__)


def get_accounts(http: httpx.Client, token: str):
    xml_tree = _parse_xml(
        _api_request(http, "Sales.getAccounts", {"accesstoken": token})
    )

    for xml_account in xml_tree.findall("./Account"):
        yield {
            "name": xml_account.findtext("Name") or "",
            "number": xml_account.findtext("Number") or "",
        }


def get_vendors(
    http: httpx.Client,
    token: str,
    account: str,
    selected_vendors: list[str] | None = None,
):
    xml_tree = _parse_xml(
        _api_request(
            http, "Sales.getVendors", {"accesstoken": token, "account": account}
        )
    )
    for xml_vendor in xml_tree.findall("./Vendor"):
        if selected_vendors and xml_vendor.text not in selected_vendors:
            log.warning(
                "IGNORING DATA for account: %s; vendor: %s. VENDOR NOT SELECTED",
                account,
                xml_vendor.text,
            )
            continue
        log.info(
            "Generating results for account: %s; vendor: %s", account, xml_vendor.text
        )
        yield str(xml_vendor.text)


def _get_or_generate_token(
    http: httpx.Client,
    username: str,
    app_specific_password: str,
    generate: bool = False,
) -> httpx.Response:
    command = "Sales.generateToken" if generate else "Sales.viewToken"
    options = {"userid": username, "password": app_specific_password}
    response = _api_request(http, command, options)
    if generate:  # TODO: remove flag and extract this code to a separate method
        service_request_id = response.headers.get("service_request_id")
        response = _api_request(
            http,
            "Sales.generateToken",
            options,
            f"&isExistingToken=Y&requestId={service_request_id}",
        )
    return response


def get_token(
    http: httpx.Client, username: str, app_specific_password: str
) -> tuple[str, date]:
    response = _get_or_generate_token(
        http=http,
        username=username,
        app_specific_password=app_specific_password,
        generate=False,
    )
    xml_tree = _parse_xml(response)
    token_message = xml_tree.findtext("./Message")

    if token_message is not None:
        log.info("Token does not exist. Generating a new token.")
        response = _get_or_generate_token(
            http=http,
            username=username,
            app_specific_password=app_specific_password,
            generate=True,
        )
        xml_tree = _parse_xml(response)

    access_token = xml_tree.findtext("./AccessToken")
    expiration_date = xml_tree.findtext("./ExpirationDate")
    assert access_token  # noqa: S101
    assert expiration_date  # noqa: S101

    return access_token, datetime.strptime(expiration_date, "%Y-%m-%d").date()


def download_report(
    http: httpx.Client, token: str, account: str, vendor: str, day: date
):
    date_str = day.strftime("%Y%m%d")
    response = _api_request(
        http,
        # WARNING! adding/removing spaces from the below command breaks the apple api
        f"m=Robot.XML, Sales.getReport, {vendor},Sales,Summary,Daily,{date_str}",
        {"accesstoken": token, "account": account},
        stream=True,
    )

    yield from response.iter_content(chunk_size=8092)


@retry(
    (TemporaryApiIssueException, httpx.HTTPStatusError),
    tries=5,
    delay=10,
    backoff=2,
    logger=log,
)
def _api_request(
    http: httpx.Client,
    command: str,
    request_options: dict[str, str],
    url_params: str | None = None,
    stream: bool = False,
) -> httpx.Response:
    request = {
        "version": "2.2",
        "mode": "Robot.XML",
        "queryInput": f"[p=Reporter.properties, {command}]",
    }
    request = {**request, **request_options}
    request = {key: urllib.parse.quote_plus(value) for key, value in request.items()}
    data = f"jsonRequest={json.dumps(request)}"

    if url_params:
        data += url_params

    response = http.post(
        BASE_URL,
        headers={
            "Accept": "text/html,image/gif,image/jpeg; q=.2, */*; q=.2",
            "Content-Type": "application/x-www-form-urlencoded",
        },
        data=data,
        stream=stream,
    )

    if _is_error_response(response):
        _parse_error_and_raise_exception(response)

    response.raise_for_status()
    return response


def _parse_xml(response: httpx.Response):
    return ET.fromstring(response.text)  # noqa: S314


def _is_error_response(response: httpx.Response):
    return int(response.headers.get("EXITCODE", 0)) == 1


def _parse_error_and_raise_exception(response: httpx.Response):
    xml_tree = _parse_xml(response)
    message = xml_tree.findtext("./Message")
    code = xml_tree.findtext("./Code")
    if code == "211":
        raise TemporaryApiIssueException(message, code)
    if code == "213":
        raise NoSalesDataException
    if code == "403":
        log.info("Request to API failed with code %s", code)
        raise TryAgainLaterException(message, code)
    if message and code:
        raise ScraperException(message, code)
    raise ScraperException("Error occurred while fetching data from Apple API.")
