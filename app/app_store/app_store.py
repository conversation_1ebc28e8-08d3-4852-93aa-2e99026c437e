import logging
from collections.abc import Sequence
from datetime import date

import httpx

from app.app_store import reporter_api
from app.core.exceptions import (
    MissingPermissionsException,
    NoSalesDataException,
    TryAgainLaterException,
)
from app.core.scraper import (
    CSSSelector,
    ManualLoginDetails,
    Organization,
    ReportZip,
    ScrapeInfo,
    <PERSON>rap<PERSON>,
    SessionIdentifier,
)
from app.core.sessions import Session
from app.core.source import Source
from app.core.types import FeatureFlags
from app.logs import add_logging_context
from app.util.camel_case_model import CamelCaseModel
from app.util.daterange import daterange
from app.util.gzip import stream_gzip_decompress
from app.util.string_functions import generate_report_file_name


class AppStoreCredentials(CamelCaseModel):
    token: str | None = None
    user: str | None = None
    password: str | None = None


class Token(CamelCaseModel):
    value: str
    expiration_date: date

    def is_expired(self):
        return self.expiration_date <= date.today()

    def __str__(self) -> str:
        return f"[**{self.value[-4:]}, exp. {self.expiration_date}]"


class AppStoreSession(Session):
    token: Token | None = None


class AppStoreFileMetaData(CamelCaseModel):
    date: date
    vendor_id: str
    account_number: str


log = logging.getLogger(__name__)


class AppStoreSalesScraper(Scraper):
    manual_login_details = ManualLoginDetails(
        url="https://example.com",
        success_selector=CSSSelector(value="#header_menu"),
    )

    def __init__(
        self,
        credentials: AppStoreCredentials,
        session: AppStoreSession,
        http: httpx.Client,
        feature_flags: FeatureFlags,
    ) -> None:
        self._credentials = credentials
        self._session = session
        self._http = http
        self._feature_flags = feature_flags

    def login(self):
        if not self._session.token:
            log.info("Token is missing.")
            self._generate_token()
            return

        assert self._session.token  # noqa: S101
        if self._session.token.is_expired():
            log.info("Token [%s] is expired.", self._session.token)
            self._generate_token()
        else:
            log.info("Found valid token.")

    def scrape(
        self, from_: date, to: date, report_zip: ReportZip, excluded_skus: list[str]
    ):
        assert self._session.token  # noqa: S101

        self.get_organizations()  # Just to log it

        success_per_account = [
            self._scrape_account(account, from_, report_zip, to)
            for account in reporter_api.get_accounts(
                self._http, self._session.token.value
            )
        ]
        if True not in success_per_account:
            log.info(
                "No sales data found for given date range for any account and vendor."
            )
            raise TryAgainLaterException
        log.info("Successfully scraped %s - %s for all accounts", from_, to)
        return ScrapeInfo()

    def get_organizations(self) -> Sequence[Organization]:
        assert self._session.token  # noqa: S101
        organizations = [
            Organization(
                id=account["number"],
                name=account["name"],
                has_scrape_blocking_issues=False,
            )
            for account in reporter_api.get_accounts(
                self._http, self._session.token.value
            )
        ]

        if organizations:
            log.info("Found organizations: %s", organizations)
            return organizations
        else:
            log.debug("No organizations available.")
            raise MissingPermissionsException

    def check_session(self) -> SessionIdentifier:
        assert self._session.token  # noqa: S101
        accounts = reporter_api.get_accounts(self._http, self._session.token.value)

        identifier = "-".join(account["number"] for account in accounts)

        return SessionIdentifier(id=identifier)

    def _generate_token(self):
        assert self._credentials.user  # noqa: S101
        assert self._credentials.password  # noqa: S101
        log.info("Generating a new token using App-Specific Password...")
        token, expiration_date = reporter_api.get_token(
            self._http, self._credentials.user, self._credentials.password
        )
        self._session.token = Token(value=token, expiration_date=expiration_date)

        log.info(
            "Successfully regenerated the token %s. Saving it in session.",
            self._session.token,
        )

        return token

    def _scrape_account(
        self, account: dict[str, str], from_: date, report_zip: ReportZip, to: date
    ):
        assert self._session.token  # noqa: S101

        account_number = account["number"]
        log.info("Downloading account %s", account_number)

        selected_vendors = None
        if account_number == "*********":  # Indie.io
            selected_vendors = ["********"]

        success_per_vendor = [
            self._scrape_vendor(from_, to, report_zip, account_number, vendor_id)
            for vendor_id in reporter_api.get_vendors(
                self._http,
                self._session.token.value,
                account_number,
                selected_vendors,
            )
        ]
        log.info("Successfully scraped %s - %s for all vendors", from_, to)
        # TODO: should it pass with only one success?
        return True in success_per_vendor

    def _scrape_vendor(
        self,
        from_: date,
        to: date,
        report_zip: ReportZip,
        account_number: str,
        vendor_id: str,
    ):
        assert self._session.token  # noqa: S101
        log.info("Attempting to scrape dates %s - %s for %s", from_, to, vendor_id)
        downloaded_files = False
        for day in daterange(from_, to):
            with add_logging_context(
                date=str(day), vendor_id=vendor_id, account_number=account_number
            ):
                log.info(
                    "Attempting to download %s for %s",
                    day,
                    vendor_id,
                )

                file_name = generate_report_file_name(
                    Source.APP_STORE_SALES,
                    day,
                    day,
                    "tsv",
                    f"{account_number}_{vendor_id}",
                )
                meta_data = AppStoreFileMetaData(
                    date=day, vendor_id=vendor_id, account_number=account_number
                )

                try:
                    with report_zip.create_report(
                        file_name=file_name, meta_data=meta_data
                    ) as singe_report:
                        singe_report.writelines(
                            stream_gzip_decompress(
                                reporter_api.download_report(
                                    self._http,
                                    self._session.token.value,
                                    account_number,
                                    vendor_id,
                                    day,
                                )
                            )
                        )
                    downloaded_files = True
                    log.info("Day successfully scraped.")
                except NoSalesDataException:
                    log.warning("Received no sales error for this date.")

        if downloaded_files:
            log.info(
                "Dates %s - %s successfully scraped for vendor %s.",
                from_,
                to,
                vendor_id,
            )
        else:
            log.info(
                "Dates %s - %s successfully scraped for vendor %s              "
                "       but no sales data found.",
                from_,
                to,
                vendor_id,
            )

        return downloaded_files
