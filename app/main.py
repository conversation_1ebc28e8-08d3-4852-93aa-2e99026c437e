from app.logs import configure_logger

configure_logger()

import logging
import os
import sys
from collections.abc import Sequence
from sys import exit as exit_

import sentry_sdk

from app.arguments import (
    CheckSessionArguments,
    CoreArguments,
    GetOrganizationsArguments,
    LoginArguments,
    ManualLoginDetailsArguments,
    NoArguments,
    ScrapeArguments,
)
from app.core.available_sources import available_sources
from app.core.exceptions import TryAgainLaterException
from app.core.runner import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from app.core.scraper import Organization
from app.logs import add_logging_context
from app.sentry import close_sentry, configure_sentry
from app.util import example_logs, messaging
from app.util.cli import CLI

if os.environ.get("NDBI_DEBUG_PY") == "1":
    from app.util.debug import wait_for_debugger

    wait_for_debugger()

cli = CLI("NDBI")
log = logging.getLogger(__name__)


def get_main_context(args: Sequence[str] | None):
    parsing_result = CoreArguments.extract_from_raw_sequence(args)
    source, command = parsing_result

    context: dict[str, str] = {}
    if command is not None:
        context["command"] = command

    if source is not None:
        context["source"] = source

    return context


def main(args: Sequence[str] | None, standalone: bool) -> int:
    configure_logger()  # TODO it's here because it's a hack for VCR tests that use multiple scrapers command, remove it

    context = get_main_context(args)

    with add_logging_context(**context):
        try:
            # we don't configure sentry at the top of the file because DNS is fetched from cli args
            configure_sentry(args, context)
            cli.run(args)
            log.debug("Process completed.")
            return 0
        except Exception as e:
            if not standalone:
                raise e
            sentry_sdk.capture_exception(e)

            messaging.send_exception(e)

            close_sentry()

            return 1


@cli.command
def login(args: LoginArguments):
    """
    Using given credentials, log in to the portal
    and create a session file at the specified path.
    """
    runner = ScraperRunner(args)
    messaging.send_result(runner.login().model_dump(by_alias=True))


@cli.command
def scrape(args: ScrapeArguments):
    """
    Download a report using the given session file and a specified date range.
    Fail if not logged in, don't try to log in again.
    """
    scrape_run_basic_data = {
        "source": args.source,
        "startDate": args.from_,
        "dateTo": args.to,
    }

    try:
        runner = ScraperRunner(args)
        result = [runner.scrape(args).model_dump(by_alias=True)]
        log.debug("Scraper succeeded! Params: %s", str(scrape_run_basic_data))
        messaging.send_result(result)
    except TryAgainLaterException:
        log.debug(
            "Scraper failed with try again later exception! Params: %s",
            scrape_run_basic_data,
        )
        messaging.send_result([])
    except Exception as e:
        log.debug("Scraper failed unexpectedly! Params: %s", str(scrape_run_basic_data))
        raise e


@cli.command
def get_source_side_organizations(args: GetOrganizationsArguments):
    """
    Using cookies from the session file, scrape all source side
    organization names. If no organization is found (or multi orgs
    are not supported) then a default organization will be returned.
    """
    runner = ScraperRunner(args)
    orgs = runner.get_organizations()
    # organizations may be subclassed, but we need an output that strictly adheres to Organization
    # schema. creating a new Organization object from each org lets us do that without explicitly
    # stating fields to export.
    org_dicts = [
        Organization.model_validate(org).model_dump(
            by_alias=True, exclude_defaults=True
        )
        for org in orgs
    ]
    messaging.send_result(org_dicts)


@cli.command
def check_session(args: CheckSessionArguments):
    """
    Using cookies from the session file, check against the portal
    whether the session is valid.
    """
    runner = ScraperRunner(args)
    messaging.send_result(runner.check_session().model_dump(by_alias=True))


@cli.command
def get_manual_login_details(args: ManualLoginDetailsArguments):
    """
    Get a JSON definition of manual login details for given source.
    """
    messaging.send_result(
        available_sources[args.source].manual_login_details.model_dump(by_alias=True)
    )


@cli.command
def example_output(args: NoArguments):
    example_logs.example_logs()


if __name__ == "__main__":
    exit_(main(sys.argv[1:], True))
