import json
from collections.abc import Sequence
from datetime import date
from typing import Any, Literal

from pydantic import BaseModel, Field, Json

from app import version
from app.util.command_with_args_to_args_dict_converter import (
    CommandWithArgsToArgsDictConverter,
    UnparsableArgsException,
)


class SentryParams(BaseModel):
    enabled: bool = False
    dsn: str | None = None
    environment: str = "development"


_SENTRY_PARAMS_NAME = "--sentryParams"
_SOURCE_NAME = "--source"

Source = str
Command = str

CoreArgsParsingResult = tuple[Source | None, Command | None]
DEFAULT_CORE_ARGUMENTS = None, None


class CoreArguments(BaseModel):
    # We want to configure logging before we have a Click context active and can rely on itsargument
    # parsing capabilities, so we're manually parsing raw arguments for now.
    @staticmethod
    def extract_from_raw_sequence(
        args: Sequence[str] | None,
    ) -> CoreArgsParsingResult:
        if not args or len(args) <= 1:
            return DEFAULT_CORE_ARGUMENTS

        try:
            command = args[0]
            parsed_args = CommandWithArgsToArgsDictConverter(args).get()
            source = parsed_args.get(_SOURCE_NAME, None)
            return source, command
        except UnparsableArgsException:
            return DEFAULT_CORE_ARGUMENTS


class SentryArguments(BaseModel):
    sentry_params: Json[SentryParams] = Field(
        default="""{"enabled": false, "dsn": "N/A", "environment": "development"}""",
        description="A JSON-encoded dictionary of sentry parameters, \
            {enabled, dsn, environment}",
    )

    # We want to configure logging before we have a Click context active and can rely on itsargument
    # parsing capabilities, so we're manually parsing raw arguments for now.

    @staticmethod
    def _get_default_sentry_params() -> SentryParams:
        if version.__version__ == "0.0.0":
            return SentryParams()
        else:
            # TODO: move sentry config into hidden place. For now we keep workaround like in JS scrapers
            return SentryParams(
                enabled=True,
                dsn="https://<EMAIL>/4508811788615680",
                environment="production",
            )

    @classmethod
    def extract_from_raw_sequence(
        cls,
        args: Sequence[str] | None,
    ) -> SentryParams:
        if not args or len(args) <= 1:
            return cls._get_default_sentry_params()

        try:
            parsed_args = CommandWithArgsToArgsDictConverter(args).get()
        except UnparsableArgsException:
            return cls._get_default_sentry_params()

        if _SENTRY_PARAMS_NAME in parsed_args:
            return SentryParams(**json.loads(parsed_args[_SENTRY_PARAMS_NAME]))

        return cls._get_default_sentry_params()


class SourceArguments(BaseModel):
    source: Source = Field(description="The data source to scrape.")


class BrowserArguments(BaseModel):
    headless: bool = Field(
        True, description="If true, browser windows would not be shown."
    )
    chrome_path: str | None = Field(
        default=None, description="Path to the Chromium binary to use for Puppeteer."
    )
    proxy_url: str | None = Field(
        default=None, description="URL of a scraper proxy to use."
    )


class SessionArguments(BaseModel):
    session_file: str = Field(
        description="File in which to store/load session data (cookies)."
    )
    output_session_file: str | None = Field(
        default=None,
        description="If provided, store updates to session data here \
            rather than in sessionFile.",
    )
    encrypt: bool = Field(
        default=True,
        description="If set, encrypt the session file with \
            the encryption token.",
    )
    encryption_token: str | None = Field(
        default=None,
        description="A secret used to encrypt/decrypt the session file. \
        Ideally, should be machine-dependent, like cookie encryption in \
        Google Chrome.",
    )


class BasicArguments(CoreArguments, SourceArguments, BrowserArguments):
    api_url: str | None = Field(
        default=None, description="URL used to query IndieBI Scraper API."
    )
    api_token: str | None = Field(
        default=None, description="JWT token used to query IndieBI Scraper API."
    )
    credentials: Json[Any] | None = Field(
        default=None,
        description="A JSON-encoded dictionary of portal-specific \
            credentials, used to create a session.",
    )
    output: (
        Literal[
            "json",
            "text",
            "simplest",
            "simplest-colored",
            "short",
            "short-colored",
            "verbose",
            "verbose-colored",
        ]
        | None
    ) = Field(
        default="json",
        description="Specify output format. Available formats: "
        "json, text, simplest, simplest-colored, short, short-colored, verbose, verbose-colored",
    )
    dump_dir: str | None = Field(
        default=None,
        description="If set, create HTML/JSON dumps and screenshots in \
            the given directory after a scraping error.",
    )
    feature_flags: Json[list[str]] | None = Field(
        default=None,
        description="A JSON-encoded array of strings with names of enabled features",
    )


class ManualLoginDetailsArguments(BasicArguments):
    pass


class LoginArguments(BasicArguments, SessionArguments):
    pass


class GetOrganizationsArguments(BasicArguments, SessionArguments):
    pass


class CheckSessionArguments(BasicArguments, SessionArguments):
    pass


class ScrapeArguments(BasicArguments, SessionArguments):
    from_: date = Field(
        description="Date from which to start scraping. (YYYY-MM-DD)",
        alias="from",
    )
    to: date = Field(description="Date on which to stop scraping. (YYYY-MM-DD)")
    report_path: str = Field(
        description="The directory to which the report file should be downloaded."
    )
    excluded_skus: Json[list[str]] | None = Field(
        default=None,
        description="A JSON-encoded array of Ids of the products \
            that should NOT be downloaded by the scraper.",
    )
    excluded_orgs: Json[list[str]] | None = Field(
        default=None,
        description="A JSON-encoded array of Ids of organizations \
            that should NOT be included in the reports.",
    )


class NoArguments(BaseModel):
    pass
