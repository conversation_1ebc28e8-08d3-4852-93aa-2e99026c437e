from typing import Annotated, <PERSON>V<PERSON>

from cryptography.fernet import <PERSON><PERSON><PERSON>
from pydantic import BaseModel, Before<PERSON><PERSON>da<PERSON>

from app.util.string_functions import remove_quotation

# Int type that will also strings representing float
NonStrictInt = Annotated[int, BeforeValidator(lambda x: int(x))]


class Session(BaseModel):
    pass


class Cookie(BaseModel):
    name: str
    value: str
    domain: str
    path: str
    expires: NonStrictInt | None = None


class HTTPSession(Session):
    cookies: list[<PERSON><PERSON>] = []

    def has_required_cookies(self) -> bool:
        """
        The basic logic.

        If we want a more sophisticated check based on cookie content, create
        a dedicated session class for each portal because cookies are different
        for each of them.
        """
        return len(self.cookies) > 0


T = TypeVar("T", bound=Session)


class SessionFile:
    def __init__(
        self,
        input_filename: str,
        output_filename: str,
        encryption_key: str | None,
    ) -> None:
        # It's because problem with passing paths between ScraperLib
        # and Python binary
        # TODO: check if we can improve options parsing in CLI
        self._input_filename = remove_quotation(text=input_filename)
        self._output_filename = remove_quotation(text=output_filename)
        self.fernet = Fernet(encryption_key) if encryption_key else None

    def load(self, session_type: type[T]) -> T:
        try:
            with open(self._input_filename, "rb") as file:
                encrypted_content = file.read()
                if self.fernet:
                    decrypted_content = self.fernet.decrypt(encrypted_content)
                else:
                    decrypted_content = encrypted_content

                return session_type.parse_raw(decrypted_content)
        except FileNotFoundError:
            pass

        return session_type()

    def save(self, session: Session):
        with open(self._output_filename, "wb") as file:
            decrypted_content = session.model_dump_json().encode("utf8")
            if self.fernet:
                encrypted_content = self.fernet.encrypt(decrypted_content)
            else:
                encrypted_content = decrypted_content
            file.write(encrypted_content)
