from enum import Enum


class Source(str, Enum):
    APP_STORE_SALES = "app_store_sales"
    EPIC_SALES = "epic_sales"
    GOOGLE_SALES = "google_sales"
    MICROSOFT_SALES = "microsoft_sales"
    NINTENDO_SALES = "nintendo_sales"
    NINTENDO_DISCOUNTS = "nintendo_discounts"
    NINTENDO_WISHLISTS = "nintendo_wishlists"

    def __str__(self) -> str:
        return self.value

    @classmethod
    def values(cls):
        return [member.value for member in cls]
