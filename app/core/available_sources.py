from app.app_store.app_store import AppStoreSalesScraper
from app.core.scraper import Scraper
from app.core.source import Source
from app.google.google_sales import GoogleSalesScraper
from app.microsoft.microsoft_sales import MicrosoftSalesScraper
from app.nintendo.nintendo_discounts import NintendoDiscountsScraper
from app.nintendo.nintendo_sst import (
    NintendoSalesScraper,
    NintendoWishlistsScraper,
)

available_sources: dict[Source, type[Scraper]] = {
    Source.APP_STORE_SALES: AppStoreSalesScraper,
    Source.MICROSOFT_SALES: MicrosoftSalesScraper,
    Source.GOOGLE_SALES: GoogleSalesScraper,
    Source.NINTENDO_SALES: NintendoSalesScraper,
    Source.NINTENDO_DISCOUNTS: NintendoDiscountsScraper,
    Source.NINTENDO_WISHLISTS: NintendoWishlistsScraper,
}
