from typing import TypedDict, Union

from pydantic import RootModel

JSON = Union[  # pylint: disable=invalid-name
    str,
    int,
    float,
    bool,
    None,
    list["JSON"],
    dict[str, "JSON"],
]

ListJSON = list[JSON]  # pylint: disable=invalid-name
DictJSON = dict[str, JSON]  # pylint: disable=invalid-name


class FeatureFlags(RootModel):
    root: list[str]

    def __contains__(self, flag: str) -> bool:
        return flag in self.root


class CSV(TypedDict):
    """
    Allows define a CSV file structure:
    {
        "headers": ["header1", "header2", ...],
        "rows": [
            {"header1": "value1", "header2": "value2", ...},
            {"header1": "value1", "header2": "value2", ...},
            ...
        ]
    }
    """

    headers: list[str]
    rows: list[dict[str, str | int | float | bool | None]]
