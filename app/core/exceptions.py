from enum import StrEnum


class ErrorType(StrEnum):
    INCORRECT_2FA = "INCORRECT_2FA"
    INCORRECT_TOTP = "INCORRECT_TOTP"
    INCORRECT_CREDENTIALS = "INCORRECT_CREDENTIALS"
    IP_BANNED = "IP_BANNED"
    MISSING_2FA = "MISSING_2FA"
    MISSING_CAPTCHA = "MISSING_CAPTCHA"
    MISSING_PERMISSIONS = "MISSING_PERMISSIONS"
    SESSION_EXPIRED = "SESSION_EXPIRED"
    TEMPORARY_PORTAL_ISSUE = "TEMPORARY_PORTAL_ISSUE"
    TOO_MANY_2FA_ATTEMPTS = "TOO_MANY_2FA_ATTEMPTS"
    UNEXPECTED_ERROR = "UNEXPECTED_ERROR"


class NonRetryableException(Exception):
    """Exception that should not be retried."""

    can_be_fixed_by_retry: bool = False

    def __init__(self, msg: str | None = None, *args: object):  # pylint: disable=keyword-arg-before-vararg
        # If no message is given, the exception's docstr is
        # used as the default message
        super().__init__(msg or self.__doc__, *args)


class ScraperException(Exception):
    """Unknown exception occurred. We should avoid using this exception for know cases,
    because UNEXPECTED_ERROR are reported to Sentry or other notification system."""

    # supported error types are in electron-app repository:
    # https://gitlab.com/bluebrick/indiebi/electron-app/-/blob/master/packages/shared/features/scrapers/types.ts?ref_type=heads#L57
    error_type: ErrorType = ErrorType.UNEXPECTED_ERROR

    def __init__(self, msg: str | None = None, *args: object):  # pylint: disable=keyword-arg-before-vararg
        # If no message is given, the exception's docstr is used as the default message
        self.message = msg or self.__doc__
        super().__init__(self.message, *args)


class KnownExceptions(ScraperException):
    """Class to group all known exceptions handled and used to inform users
    about known but unwanted scraper behavior. This is usually information that the user
    needs to take some action on his side.

    Mainly used for Sentry error reporting. We don't want to report errors
    like SessionExpired or InvalidCredentials in Sentry. But we want to know if ScraperException
    (aka UNEXPECTED_ERROR) or any unhandled exception happened.
    """

    pass


class InvalidCredentialsException(KnownExceptions, NonRetryableException):
    """Invalid credentials. Did you enter them correctly?"""

    error_type = ErrorType.INCORRECT_CREDENTIALS


class SessionExpiredException(KnownExceptions):
    """Session expired."""

    error_type = ErrorType.SESSION_EXPIRED


class InvalidSessionException(KnownExceptions):
    """Session is invalid."""

    error_type = ErrorType.SESSION_EXPIRED


class NoSessionException(KnownExceptions, NonRetryableException):
    """Session was not initialized yet."""

    error_type = ErrorType.SESSION_EXPIRED


class CaptchaRequiredException(KnownExceptions, NonRetryableException):
    """Logging in requires solving captcha."""

    error_type = ErrorType.MISSING_CAPTCHA


class MFARequiredException(KnownExceptions, NonRetryableException):
    """Logging in requires 2-factor authentication."""

    error_type = ErrorType.MISSING_2FA


class InvalidTOTPSecretException(KnownExceptions, NonRetryableException):
    """Provided 2-factor authentication TOTP secret is invalid."""

    error_type = ErrorType.INCORRECT_TOTP


class MFAInvalid(KnownExceptions):
    """Provided 2-factor authentication code is invalid. \
        Did you provide correct shared secret?"""

    error_type = ErrorType.INCORRECT_2FA


class MFATooManyAttemptsException(KnownExceptions, NonRetryableException):
    """Too many attempts to provide 2-factor authentication code. \
        Please wait a while before trying again."""

    error_type = ErrorType.TOO_MANY_2FA_ATTEMPTS


class TemporaryApiIssueException(KnownExceptions):
    """Temporary API issue occurred."""

    error_type = ErrorType.TEMPORARY_PORTAL_ISSUE


class NoSalesDataException(ScraperException, NonRetryableException):
    """There's no sales data for given period"""


class BannedException(KnownExceptions):
    """Your IP is temporarily blocked by the portal."""

    error_type = ErrorType.IP_BANNED


class TryAgainLaterException(KnownExceptions):
    """Temporary platform issue occurred."""

    error_type = ErrorType.TEMPORARY_PORTAL_ISSUE


class MissingPermissionsException(ScraperException, NonRetryableException):
    """You don't have enough permissions to scrape with those credentials. Please check your permissions or use different account."""

    error_type = ErrorType.MISSING_PERMISSIONS
