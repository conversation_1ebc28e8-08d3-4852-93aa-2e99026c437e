import logging
import time
from abc import ABC, abstractmethod
from datetime import datetime
from functools import singledispatchmethod
from typing import Any
from urllib.parse import urljoin

import httpx
from pydantic import AnyUrl, BaseModel
from pyotp import TOTP

from app.core.exceptions import (
    InvalidTOTPSecretException,
    MFARequiredException,
    MFATooManyAttemptsException,
)
from app.core.source import Source
from app.util.messaging import MFARequest, send_mfa_request

log = logging.getLogger(__name__)

SCRAPER_API_REQUIRED_HEADERS = {
    "Accept-Language": "en-US",
    "Accept-encoding": "gzip, deflate, br",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/99.0.4844.51 Safari/537.36",
}


class TOTPConfig(BaseModel):
    code: str
    source: Source
    for_time: datetime | None = None


class ManualMFAConfig(BaseModel):
    api_url: AnyUrl
    api_token: str
    source: Source
    max_attempts: int = 5


class MFA(ABC):
    @singledispatchmethod
    @staticmethod
    def get_authenticator(config: Any) -> "MFA":
        return DisabledMFA()

    @get_authenticator.register
    @staticmethod
    def _(config: TOTPConfig):
        return TOTPMFA(config)

    @get_authenticator.register
    @staticmethod
    def _(config: ManualMFAConfig):
        return ManualMFA(config)

    @abstractmethod
    def request(self) -> str:
        pass


class DisabledMFA(MFA):
    def request(self) -> str:
        raise MFARequiredException(
            "MFA is disabled, but a code is required for authentication."
        )


class TOTPMFA(MFA):
    def __init__(self, config: TOTPConfig) -> None:
        self._totp_secret = config.code
        self._fixed_for_time: datetime | None = config.for_time

    @property
    def _for_time(self) -> datetime:
        if self._fixed_for_time is not None:
            return self._fixed_for_time
        return datetime.now()

    def request(self) -> str:
        log.debug("Getting 2fa code from TOTP secret")
        try:
            return TOTP(self._totp_secret).at(for_time=self._for_time)
        except Exception as e:
            log.error("Invalid TOTP secret: %s", e)
            raise InvalidTOTPSecretException from e


class ManualMFA(MFA):
    def __init__(self, config: ManualMFAConfig) -> None:
        self._api_url = config.api_url
        self._api_token = config.api_token
        self._max_attempts = config.max_attempts
        self._source = config.source
        self._attempts = 0

    def request(self) -> str:
        log.debug("Getting 2fa code from API")
        self._attempts += 1
        if self._attempts > self._max_attempts:
            log.error("Too many attempts to request MFA code")
            raise MFATooManyAttemptsException

        send_mfa_request(
            request=MFARequest(
                source=self._source,
                attempt=self._attempts,
                max_attempts=self._max_attempts,
            )
        )

        return self._wait_for_mfa_code()

    def _wait_for_mfa_code(self) -> str:
        retries = 0
        while not (auth_code := self._get_mfa_code()):
            retries += 1
            if retries > 30:  # 5 minutes with 10 seconds interval
                log.error("MFA code not received after 5 minutes")
                raise MFARequiredException("MFA code not received after 5 minutes")
            time.sleep(10)
        return auth_code

    def _get_mfa_code(self) -> str | None:
        log.debug("Requesting auth code from API")
        response = httpx.get(
            urljoin(str(self._api_url), f"auth-code/{self._source.value}"),
            headers={
                "Authorization": f"Bearer {self._api_token}",
                **SCRAPER_API_REQUIRED_HEADERS,
            },
            timeout=30,
        )
        response.raise_for_status()
        return response.json()["authCode"]
