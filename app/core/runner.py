import logging
from collections.abc import Sequence
from datetime import datetime
from functools import wraps
from pathlib import Path
from typing import Any, Callable, Generic, TypeVar

import httpx
from pydantic import BaseModel

# Optional HAR support: requests-har is requests-specific; guard its import
try:
    from requests_har.har import HarDict  # type: ignore
except Exception:  # pragma: no cover - optional dependency or incompatible with httpx
    HarDict = None  # type: ignore


from app.arguments import BasicArguments, ScrapeArguments
from app.core.available_sources import available_sources
from app.core.exceptions import InvalidSessionException
from app.core.mfa import MFA, ManualMFAConfig, TOTPConfig
from app.core.scraper import (
    Organization,
    ReportZip,
    Scraper,
    ScrapeResult,
    SessionIdentifier,
)
from app.core.sessions import HTTPSession, Session, SessionFile
from app.core.source import Source
from app.core.types import FeatureFlags
from app.util.http import http_from_session, update_session

log = logging.getLogger(__name__)


def emergency_close_handler(method):
    @wraps(method)
    def wrapper(self, *args, **kwargs):
        try:
            return method(self, *args, **kwargs)
        except Exception as e:
            self.emergency_close(e)
            raise e

    return wrapper


class ScraperRunner:
    """
    A proxy over Scraper implementations. Prepares the environment for Scraper implementations,
    including all dependencies and injecting them, instantiates the source-specific Scraper code
    and exposes functions to run its commands safely.
    """

    def __init__(self, args: BasicArguments) -> None:
        self._session_file = SessionFile(
            args.session_file,
            args.output_session_file or args.session_file,
            args.encryption_token if args.encrypt else None,
        )
        self._api_url = (
            args.api_url
            if args.api_url and args.api_url.endswith("/1")
            else f"{args.api_url}/1/"
        )
        self._api_token = args.api_token

        self._credentials = args.credentials or {}
        self._proxy_url = args.proxy_url
        self._feature_flags = FeatureFlags(root=args.feature_flags or [])
        self._http: httpx.Client | None = None
        self._source = args.source
        self._dump_dir = args.dump_dir
        self._http_response_dump: httpx.Response | None = None
        self._har: object | None = None

        self._scraper_cls = available_sources[Source(args.source)]
        scraper_kwargs = self._get_scraper_kwargs(self._scraper_cls)
        self._scraper: Scraper = self._scraper_cls(**scraper_kwargs)

    @emergency_close_handler
    def login(self) -> SessionIdentifier:
        self._scraper.login()
        self._update_session()
        result = self._scraper.check_session()
        self.close()
        return result

    @emergency_close_handler
    def scrape(self, args: ScrapeArguments) -> ScrapeResult:
        with ReportZip[Any](
            report_path=Path(args.report_path),
            source=Source(args.source),
            date_from=args.from_,
            date_to=args.to,
            manifest_version=self._scraper.manifest_version,
        ) as report_zip:
            scrape_summary = self._scraper.scrape(
                from_=args.from_,
                to=args.to,
                report_zip=report_zip,
                excluded_skus=args.excluded_skus or [],
            )
            result = ScrapeResult(
                report_file_name=report_zip.file_name,
                start_date=args.from_.isoformat(),
                end_date=args.to.isoformat(),
                source=Source(args.source),
                no_data=scrape_summary.no_data,
            )
        self.close()
        return result

    @emergency_close_handler
    def get_organizations(self) -> Sequence[Organization]:
        result = self._scraper.get_organizations()
        self.close()
        return result

    @emergency_close_handler
    def check_session(self) -> SessionIdentifier:
        result = self._scraper.check_session()
        self.close()
        return result

    def close(self):
        self._save_session()
        if self._http:
            self._http.close()

    def emergency_close(self, error):
        if isinstance(error, InvalidSessionException):
            log.warning("Session is invalid. Clearing session")
            self._clear_session()

        log.error("Emergency close", exc_info=error)
        log.error(error)  # TODO workaround, should be fixes
        self._save_response_dump()
        self._save_har()
        if self._http:
            self._http.close()

    def _get_scraper_kwargs(self, scraper_cls: type[Scraper]) -> dict[str, Any]:
        available_dependencies: list[Dependency[Any]] = [
            Dependency[BaseModel](
                field_name="credentials",
                allowed_supertype=BaseModel,
                constructor=lambda t: t.model_validate(self._credentials),
            ),
            Dependency[Session](
                field_name="session",
                allowed_supertype=Session,
                constructor=lambda t: self._init_session(t),
            ),
            # TODO: if http is using HTTPSession with cookies we need to still define
            # session in scraper even if it is not needed in the code (NintendoScraper)
            Dependency[httpx.Client](
                field_name="http",
                allowed_supertype=httpx.Client,
                constructor=lambda _: self._init_http(),
            ),
            Dependency[MFA](
                field_name="mfa",
                allowed_supertype=MFA,
                constructor=lambda _: self._init_mfa(),
            ),
            Dependency[FeatureFlags](
                field_name="feature_flags",
                allowed_supertype=FeatureFlags,
                constructor=lambda _: self._feature_flags,
            ),
        ]
        return generate_init_kwargs(scraper_cls, available_dependencies)

    def _init_http(self):
        session = self._session if isinstance(self._session, HTTPSession) else None
        self._http = http_from_session(session, self._proxy_url)
        self._configure_html_dumps()
        self._configure_har()

        return self._http

    def _init_session(self, session_type: type[Session]):
        self._session = self._session_file.load(session_type)
        return self._session

    def _init_mfa(self) -> MFA:
        if "totpSecret" in self._credentials and self._credentials["totpSecret"]:
            config = TOTPConfig(
                code=self._credentials["totpSecret"],
                source=Source(self._source),
            )
        elif self._api_url and self._api_token:
            config = ManualMFAConfig(
                api_url=self._api_url,
                api_token=self._api_token,
                source=Source(self._source),
                max_attempts=self._scraper_cls.max_mfa_attempts,
            )
        else:
            config = None

        return MFA.get_authenticator(config)

    def _clear_session(self):
        if self._http:
            self._http.cookies.clear()
        self._save_session()

    def _save_session(self):
        self._update_session()
        self._session_file.save(self._session)

    def _update_session(self):
        if self._http and isinstance(self._session, HTTPSession):
            update_session(self._http, self._session)

    # because current HAR implementation is buggy, we need to catch exceptions and ignore them just to make scraper safe
    def _har_response_handler_with_catch(
        self, response: httpx.Response, *args, **kwargs
    ):
        try:
            self._har.on_response(response, *args, **kwargs)
        except Exception as e:
            log.warning("Error while adding HAR entry", exc_info=e)

    def _configure_har(self):
        if "full-history-dump" in self._feature_flags and not self._dump_dir:
            log.warning("Full history dump enabled, but dump directory not set")
            return

        if "full-history-dump" in self._feature_flags:
            if HarDict is None:
                log.warning("HAR capture not available; skipping")
                return
            self._har = HarDict()
            self._http.event_hooks["response"].insert(
                0, self._har_response_handler_with_catch
            )

    def _set_http_response_dump(self, response: httpx.Response, *args, **kwargs):
        self._http_response_dump = response

    def _save_har(self):
        if self._har and self._dump_dir:
            log.info("Saving HAR file")
            date_str = datetime.now().strftime("%Y-%m-%d-%H-%M-%S.%f")
            filename = f"{date_str}_{self._source}_emergency_close.har"
            try:
                self._har.save(Path(self._dump_dir, filename))
            except OSError as e:
                log.warning("Error while saving HAR file", exc_info=e)

    def _save_response_dump(self):
        if self._http_response_dump and self._dump_dir:
            log.info("Saving response dump")
            date_str = datetime.now().strftime("%Y-%m-%d-%H-%M-%S.%f")
            file_extension = (
                "html"
                if "text/html"
                in self._http_response_dump.headers.get("content-type", "")
                else "json"
            )
            filename = f"{date_str}_{self._source}_emergency_close.{file_extension}"
            try:
                with open(Path(self._dump_dir, filename), "w", encoding="utf-8") as f:
                    f.write(self._http_response_dump.text)
            except OSError as e:
                log.warning("Error while saving response dump", exc_info=e)

    def __exit__(self):
        self.close()

    def _configure_html_dumps(self):
        if self._dump_dir:
            self._http.event_hooks["response"].insert(0, self._set_http_response_dump)


T = TypeVar("T")


class Dependency(BaseModel, Generic[T]):
    field_name: str
    allowed_supertype: type[T]
    constructor: Callable[[type[T]], T]


def generate_init_kwargs(
    cls: type, dependencies: list[Dependency[Any]]
) -> dict[str, Any]:
    """
    Generate init kwargs in from of a dict for the class `cls`.
    It calls `constructor(FieldType)` to create a value for each param in
    `cls` __init__ method.

    If FieldType is not a subclass of `allowed_supertype`, raise a TypeError.
    If `instance` does not have such a field, do nothing.
    """

    cls_dependencies: dict[str, Any] = {}
    for dependency in dependencies:
        annotation = cls.__init__.__annotations__.get(dependency.field_name)
        if annotation is None:
            continue

        if (
            annotation.__name__ is not dependency.allowed_supertype.__name__
            and not issubclass(annotation, dependency.allowed_supertype)
        ):
            raise TypeError(
                f"Cannot inject {dependency.allowed_supertype} to {annotation}"
            )
        cls_dependencies[dependency.field_name] = dependency.constructor(annotation)
    return cls_dependencies
