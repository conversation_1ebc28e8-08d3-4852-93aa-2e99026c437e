import contextlib
import json
from collections.abc import Sequence
from datetime import date
from io import String<PERSON>
from pathlib import Path
from typing import ClassVar, Generic, Literal, Protocol, TypeVar
from zipfile import ZipFile

from pydantic import BaseModel, Field

from app.core.source import Source
from app.core.types import CSV, JSO<PERSON>
from app.util.camel_case_model import CamelCaseModel
from app.util.csv import to_raw_csv
from app.version import __version__


class ScrapeInfo(CamelCaseModel):
    no_data: bool = False


class ScrapeResult(ScrapeInfo):
    report_file_name: str
    start_date: str
    end_date: str
    source: Source


T = TypeVar("T")


class ReportManifest(CamelCaseModel, Generic[T]):
    date_from: date
    date_to: date
    manifest_version: int = 1
    file_meta_data: dict[str, T] = Field(default={})
    scraper_version: str = __version__

    def add_file_meta_data(self, file_name: str, meta_data: T):
        self.file_meta_data[file_name] = meta_data


class ManifestFileFields(CamelCaseModel):
    date_from: date
    date_to: date
    raw_data: bool


class SessionIdentifier(CamelCaseModel):
    id: str
    has_scrape_blocking_issues: bool = False


class Organization(CamelCaseModel):
    id: str
    name: str
    has_scrape_blocking_issues: bool = False


class CSSSelector(CamelCaseModel):
    type: Literal["css"] = "css"
    value: str


class XPathSelector(CamelCaseModel):
    type: Literal["xpath"] = "xpath"
    value: str


class ManualLoginDetails(CamelCaseModel):
    url: str
    success_selector: CSSSelector | XPathSelector


class ReportZip(contextlib.AbstractContextManager, Generic[T]):
    def __init__(
        self,
        report_path: Path,
        source: Source,
        date_from: date,
        date_to: date,
        manifest_version: int = 1,
    ):
        self.report_path: Path = report_path
        self.file_name: str = f"{source}-{date_from}_{date_to}.zip"

        self.manifest = ReportManifest[T](
            date_from=date_from,
            date_to=date_to,
            manifest_version=manifest_version,
        )

    def __enter__(self) -> "ReportZip[T]":
        self._file = (self.report_path / self.file_name).open("wb")
        self._zip = ZipFile(file=self._file, mode="w")
        return self

    def __exit__(self, exc_type, exc_value, traceback) -> None:
        self._save_manifest()
        self._zip.close()
        self._file.close()

    def create_report(self, file_name: str, meta_data: T | None = None):
        if meta_data is not None:
            self.manifest.add_file_meta_data(file_name=file_name, meta_data=meta_data)
        return self._zip.open(file_name, "w")

    def add_json_file(
        self,
        data: JSON,
        file_name: str,
        date_from: date,
        date_to: date,
        raw_data: bool = True,
    ) -> None:
        metadata = ManifestFileFields(
            date_from=date_from, date_to=date_to, raw_data=raw_data
        )
        buffer = StringIO()
        json.dump(data, buffer, indent=2)
        buffer.seek(0)
        with self.create_report(file_name, metadata) as file:
            file.write(buffer.getvalue().encode())

    def add_csv_file(
        self,
        data: CSV,
        file_name: str,
        date_from: date,
        date_to: date,
        raw_data: bool = True,
    ) -> None:
        metadata = ManifestFileFields(
            date_from=date_from, date_to=date_to, raw_data=raw_data
        )
        with self.create_report(file_name, metadata) as file:
            file.write(to_raw_csv(data).encode())

    def add_file(
        self,
        data: str | bytes,
        file_name: str,
        date_from: date,
        date_to: date,
        raw_data: bool = True,
    ) -> None:
        metadata = ManifestFileFields(
            date_from=date_from, date_to=date_to, raw_data=raw_data
        )
        with self.create_report(file_name, metadata) as file:
            file.write(data.encode() if isinstance(data, str) else data)

    def _save_manifest(self):
        with self._zip.open("manifest.json", "w") as manifest_file:
            manifest_file.write(self.manifest.model_dump_json(by_alias=True).encode())


class Scraper(Protocol):
    """
    The basic protocol for scraper implementations. Scrapers for different sources should subclass
    this Protocol. Instances of these subclasses are typically done by ScraperRunner,  which also
    provides a basic form of dependency injection to provide the scraper with its dependencies.
    """

    manual_login_details: ClassVar[ManualLoginDetails]

    """
    Assign a value to manual_login_details in the subclass to specify
    values that should be used by the get_manual_login_details command.
    """

    manifest_version: int = 1
    """
    Version that will be saved in the manifest.json file.
    """

    max_mfa_attempts: int = 0
    """
    Maximum number of attempts to request MFA code. 0 means disabled."""

    # TODO: we may want to get rid of pylint warnings by using @abstractmethod
    # TODO: but it needs checking if ABCMeta works with Protocol subclasses
    def login(self) -> None:
        """
        Use the data provided in self.credentials and the loaded session to
        login and update the session accordingly
        """
        ...  # pylint: disable=unnecessary-ellipsis

    def scrape(
        self,
        from_: date,
        to: date,
        report_zip: ReportZip,
        excluded_skus: list[str],
    ) -> ScrapeInfo:
        """
        Using the loaded session, generate a report for the given date range
        and write it to report_file.
        """
        ...  # pylint: disable=unnecessary-ellipsis

    def get_organizations(self) -> Sequence[Organization]:
        """
        Using the loaded session, retrieve a list of available organizations.
        """
        ...  # pylint: disable=unnecessary-ellipsis

    def check_session(self) -> SessionIdentifier:
        """
        Check if the loaded session contains a valid login and return
        a source-specific session identifier.
        """
        ...  # pylint: disable=unnecessary-ellipsis
