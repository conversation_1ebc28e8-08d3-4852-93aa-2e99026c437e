[tool.poetry]
package-mode = false
name = "scrapers-py"
version = "0.1.0"
description = ""
authors = ["<PERSON><PERSON><PERSON> <<EMAIL>>"]

[tool.poetry.dependencies]
python = "~3.11"
click = "^8.1.3"
httpx = "^0.27.0"
pydantic = "^2.10.6"
beautifulsoup4 = "^4.12.2"
retry = "0.9.2"
logging-json = "^0.6.0"
google-cloud-storage = "^3.0.0"
requests-toolbelt = "^1.0.0"
types-beautifulsoup4 = "^********"
pyinstaller = "6.6.0"
poethepoet = "^0.32.0"
python-dateutil = "^2.9.0.post0"
python-slugify = "^8.0.4"
respx = "^0.22.0"
requests-har = "^1.1.0"
cryptography = "^44.0.0"
msal = "^1.31.1"
sentry-sdk = "^2.0.0"
pyotp = "^2.9.0"

[tool.poetry.group.dev.dependencies]
debugpy = "^1.6.3"
pyright = "^1.1.280"
pre-commit = "^4.0.0"
colored-traceback = "^0.4.0"
ruff = "^0.9.0"
ipykernel = "^6.29.5"
responses = "^0.25.7"

[tool.poetry.group.test.dependencies]
pytest-cov = "^6.1.1"
pytest = "^8.3.5"
pytest-deadfixtures = "^2.2.1"
pytest-xdist = "^3.6.1"
vcrpy = "^7.0.0"
pytest-recording = "^0.13.2"
time-machine = "^2.16.0"

[tool.pyright]
typeCheckingMode = "basic"
#strict = ["app"]
reportUnusedImport = "warning"

[tool.ruff]
preview = true

[tool.ruff.lint]
select = [
    "F",     # Pyflakes
    "E",     # Pycodestyle errors
    "W",     # Pycodestyle warnings
    "I",     # isort
    "ASYNC", # flake8-async (asyncio)
    "S",     # flake8-bandit (security)
    "ERA",   # flake8-eradicate (remove commented out code)
    "FLY",   # flynt (f-string)
    # "PTH",   # flake8-use-pathlib TODO Try to enable it in the future
    "A",   # flake8-builtins
    "C4",  # flake8-comprehensions
    "EXE", # flake8-executable
    "LOG", # flake8-logging
    "INP", # flake8-no-pep420 (no implicit namespace packages)
    "T20", # flake8-print (print statements)
    "PT",  # flake8-pytest-style (pytest specific style issues)
    "RSE", # flake8-raise (issues with raise statements)
    "SIM", # flake8-simplify (simplifiable constructs)
    "C90", # mccabe (complexity checker)
    "N",   # pep8-naming (naming conventions)
    "UP",  # pyupgrade (Python syntax upgrades)
    # "TRY",   # tryceratops (anti-patterns in try/except blocks) TODO Try to enable it in the future
    "PGH", # pygrep-hooks
    # "FBT",   # flake8-boolean-trap TODO Try to enable it in the future
    # "ISC",   # flake8-implicit-str-concat
    # "B",     # flake8-bugbear
    # "DTZ",   # flake8-datetimez
]
ignore = [
    "E501", # Line too long
    "E712", # Comparison to False should be 'if cond is False:' or 'if not cond:'
    "N818", # Exception name {name} should be named with an Error suffix
    "F401", # Unused imports - annoying in dev process when it constantly removes imports
]

[tool.ruff.lint.per-file-ignores]
"app/main.py" = [
    "E402", # Module level import not at top of file
]
"app/*/types.py" = [
    "A005", # Module `types` is shadowing a Python builtin module
]
"app/util/*" = [
    "A005", # Module `csv` is shadowing a Python builtin module
]
"tests/*" = [
    "S101",   # Bandit: Use of assert detected
    "S404",   # `subprocess` module is possibly insecure
    "S105",   # Possible hardcoded password assigned to: "password"
    "S106",   # Possible hardcoded password in func argument
    "INP001", # Implicit namespace package
    "LOG004", # `.exception()` call outside exception handlers
]
"scripts/*" = [
    "INP001", # Implicit namespace package. Add an `__init__.py`.
    "T201",   # flake8-print: print found

    "S605", # TODO: Starting a process with a shell
    "S607", # TODO: Starting a process with a partial executable path
]

[tool.ruff.lint.flake8-pytest-style]
fixture-parentheses = false

[tool.ruff.lint.isort]
known-first-party = ["app"]


[tool.pytest.ini_options]
# uncomment this to enable logging output in pytest
log_cli = false
log_cli_level = "INFO"
log_cli_format = "%(asctime)s,%(msecs)03d [%(levelname)8s] %(message)s (%(filename)s:%(lineno)s)"
log_cli_date_format = "%Y-%m-%d %H:%M:%S"
filterwarnings = ["ignore", "default:::app"]

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.poe.tasks]
decrypt = "python scripts/encrypt_credentials.py --decrypt"
encrypt = "python scripts/encrypt_credentials.py --encrypt"
build_bin = "pyinstaller -y --onefile app/main.py -n scrape"
cli = "python -m app.main"
test = "pytest -s"
test_unit = "pytest -s tests/unit"
test_integration = "pytest -s tests/integration"
test_vcr = "pytest -s tests/vcr"
setup_version = "sh scripts/replace-version.sh"
setup_version_win = "powershell scripts/replace-version.ps1"
ruff-format = "ruff format"
ruff-check = "ruff check --fix"
lint = ["ruff-format", "ruff-check"]
hooks = "poetry run pre-commit install"


[tool.poe.executor]
type = "poetry"
