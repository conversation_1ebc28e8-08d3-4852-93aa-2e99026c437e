from datetime import date

import httpx
import pytest

from app.microsoft.api import MicrosoftAPIClient, get_microsoft_auth_token_provider
from app.microsoft.microsoft_sales import (
    ADDONS_ACQUISITIONS_GROUP_BY,
    ANALYTICS_BASE_URL,
    APP_ACQUISITIONS_GROUP_BY,
    DEFAULT_QUERY,
    QUERY_2Y,
    REPORT_BASE_URL,
    SCOPE_URL,
    SKUsFetcher,
)
from app.microsoft.models import APISetupData, MSApplication


@pytest.fixture
def megagon_auth_token_provider(get_credentials):
    return get_microsoft_auth_token_provider(
        api_setup_data=APISetupData(
            **get_credentials("microsoft_store_megagon")["apiSetupData"][0]
        ),
        scope=SCOPE_URL,
    )


@pytest.fixture
def ripstone():
    return get_microsoft_auth_token_provider(
        api_setup_data=APISetupData(
            company_name="Ripstone",
            client_id="31bb07b9-93d5-4556-b9d0-abade63330cd",
            client_secret="****************************************",
            tenant_id="2cdddf32-26e4-49c7-a099-ef64d05cad93",
        ),
        scope=SCOPE_URL,
    )


@pytest.fixture
def ripstone_reports_client(ripstone):
    with MicrosoftAPIClient(
        auth_token_provider=ripstone,
        base_url=REPORT_BASE_URL,
    ) as client:
        yield client


@pytest.fixture
def ripstone_analytics_client(ripstone):
    with MicrosoftAPIClient(
        auth_token_provider=ripstone,
        base_url=ANALYTICS_BASE_URL,
    ) as client:
        yield client


@pytest.fixture
def megagon_reports_client(megagon_auth_token_provider):
    with MicrosoftAPIClient(
        auth_token_provider=megagon_auth_token_provider,
        base_url=REPORT_BASE_URL,
    ) as client:
        yield client


@pytest.fixture
def megagon_analytics_client(megagon_auth_token_provider):
    with MicrosoftAPIClient(
        auth_token_provider=megagon_auth_token_provider,
        base_url=ANALYTICS_BASE_URL,
    ) as client:
        yield client


other_applications = [
    MSApplication(
        product_id="9MV6MCVLT8GR",
        title_name="Lonely Mountains: Downhill ",
        parent_product_id="9MV6MCVLT8GR",
        parent_product_name="Lonely Mountains: Downhill ",
        in_app=False,
    ),
    MSApplication(
        product_id="9PKF4TZFMDTQ",
        title_name="LMD_DLC02",
        parent_product_id="9MV6MCVLT8GR",
        parent_product_name="Lonely Mountains: Downhill ",
        in_app=True,
    ),
    MSApplication(
        product_id="9P9FL43P6SR8",
        title_name="LMD_DLC04",
        parent_product_id="9MV6MCVLT8GR",
        parent_product_name="Lonely Mountains: Downhill ",
        in_app=True,
    ),
    MSApplication(
        product_id="9N6P2L9P2VTF",
        title_name="LMD_DLC05",
        parent_product_id="9MV6MCVLT8GR",
        parent_product_name="Lonely Mountains: Downhill ",
        in_app=True,
    ),
    MSApplication(
        product_id="9NQCX3D06LF7",
        title_name="LMD_DLC03",
        parent_product_id="9MV6MCVLT8GR",
        parent_product_name="Lonely Mountains: Downhill ",
        in_app=True,
    ),
    MSApplication(
        product_id="9NP1NRZF1Q5Z",
        title_name="E89ED786-898C-4EFB-BC05-06B732A9A793",
        parent_product_id="9MV6MCVLT8GR",
        parent_product_name="Lonely Mountains: Downhill ",
        in_app=True,
    ),
    MSApplication(
        product_id="9PF3MC3V9Q27",
        title_name="Lonely Mountains: Snow Riders",
        parent_product_id="9PF3MC3V9Q27",
        parent_product_name="Lonely Mountains: Snow Riders",
        in_app=False,
    ),
    MSApplication(
        product_id="9PKJ5PHQ34WM",
        title_name="lmsr_dlc_supporter_pack",
        parent_product_id="9PF3MC3V9Q27",
        parent_product_name="Lonely Mountains: Snow Riders",
        in_app=True,
    ),
    MSApplication(
        product_id="9NC65K09D5VM",
        title_name="Lonely Mountains: Snow Riders - Supporter Bundle",
        parent_product_id="9NC65K09D5VM",
        parent_product_name="Lonely Mountains: Snow Riders - Supporter Bundle",
        in_app=False,
    ),
    MSApplication(
        product_id="9N412R0RP1TT",
        title_name="Megagon Foo",
        parent_product_id="9N412R0RP1TT",
        parent_product_name="Megagon Foo",
        in_app=False,
    ),
]


def _test_x(megagon_analytics_client: MicrosoftAPIClient):
    applications = [
        MSApplication(
            product_id="9MX1M5WNBCSS",
            title_name="ELDFJALL_LMD_DLC01",
            parent_product_id="9MX1M5WNBCSS",
            parent_product_name="ELDFJALL_LMD_DLC01",
            in_app=False,
        )
    ]  # + other_applications

    date_from = date(2025, 7, 29)
    date_to = date(2025, 8, 5)

    for app in applications:
        response: httpx.Response = megagon_analytics_client.get(
            "analytics/acquisitions",
            params={
                "applicationId": app.product_id,
                "startDate": str(date_from),
                "endDate": str(date_to),
                "groupby": ADDONS_ACQUISITIONS_GROUP_BY
                if app.in_app
                else APP_ACQUISITIONS_GROUP_BY,
                "orderby": "date",
            },
        )
        # assert response.status_code == 200, response.text
        response_json = response.json()
        print(response_json)


def _test_y(megagon_reports_client: MicrosoftAPIClient):
    result = SKUsFetcher(client=megagon_reports_client, query=QUERY_2Y).get_skus()
    assert result != []


def test_ripstone(ripstone_analytics_client):
    # apps = SKUsFetcher(client=ripstone_analytics_client, query=DEFAULT_QUERY).get_skus()
    response: httpx.Response = ripstone_analytics_client.get(
        "analytics/acquisitions",
        params={
            "applicationId": "BRJCQK0F4RM8",
            "startDate": "2025-07-29",
            "endDate": "2025-08-05",
            "groupby": APP_ACQUISITIONS_GROUP_BY,
            "orderby": "date",
        },
    )
    assert response.status_code == 200, response.text
