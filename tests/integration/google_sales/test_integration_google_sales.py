# Standard libraries
# Third party libraries
import pytest

from app.core.source import Source
from tests.process_runner import ScraperRunConfig


@pytest.fixture
def google_sales_scraper(
    default_scraper_run_config: ScraperRunConfig,
    get_credentials,
) -> ScraperRunConfig:
    scraper = default_scraper_run_config
    scraper.source = Source.GOOGLE_SALES
    scraper.date_from = "2023-07-01"
    scraper.date_to = "2023-07-31"
    scraper.proxy_url = None
    scraper.credentials = get_credentials("google_sales")
    scraper.direct = True
    return scraper


def test_google_sales_login(google_sales_scraper: ScraperRunConfig):
    result = google_sales_scraper.login()
    result.expect_success()
    expected_result = "109813773605477149319"
    assert expected_result == result.data["id"]
    assert result.data["hasScrapeBlockingIssues"] is False


def test_google_sales_check_session(google_sales_scraper: ScraperRunConfig):
    google_sales_scraper.login()
    result = google_sales_scraper.check_session()
    result.expect_success()
    expected_result = "109813773605477149319"
    assert expected_result == result.data["id"]
    assert result.data["hasScrapeBlockingIssues"] is False


def test_google_sales_scrape(google_sales_scraper: ScraperRunConfig):
    google_sales_scraper.login()
    result = google_sales_scraper.scrape()
    result.expect_success()
    result.expect_zip_with_manifest()
    assert result.data
    assert result.manifest_json == {
        "dateFrom": "2023-07-01",
        "dateTo": "2023-07-31",
        "fileMetaData": {
            "salesreport_202307.csv": {
                "vendorId": "109813773605477149319",
                "dateFrom": "2023-07-01",
                "dateTo": "2023-07-31",
            }
        },
        "manifestVersion": 1,
        "scraperVersion": "0.0.0",
    }


def test_google_sales_login_and_scrape(google_sales_scraper: ScraperRunConfig):
    result = google_sales_scraper.login_and_scrape()
    result.expect_success()
    result.expect_zip_with_manifest()
    assert result.data


def test_google_sales_get_organizations(google_sales_scraper: ScraperRunConfig):
    google_sales_scraper.login()
    result = google_sales_scraper.get_source_side_organizations()
    result.expect_success()
    expected_result = [{"id": "indiebi-387909", "name": "indiebi-387909"}]
    assert expected_result == result.data
