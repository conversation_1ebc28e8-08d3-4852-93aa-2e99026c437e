import pytest

from app.core.exceptions import InvalidCredentialsException
from app.core.source import Source
from tests.process_runner import ScraperRunConfig


@pytest.fixture
def microsoft_sales_scraper(
    default_scraper_run_config: ScraperRunConfig, get_credentials
) -> ScraperRunConfig:
    scraper = default_scraper_run_config
    scraper.source = Source.MICROSOFT_SALES
    scraper.date_from = "2024-08-21"
    scraper.date_to = "2024-09-07"
    scraper.proxy_url = None
    scraper.credentials = get_credentials("microsoft_store")

    scraper.direct = True

    return scraper


def test_microsoft_sales_login(microsoft_sales_scraper: ScraperRunConfig):
    result = microsoft_sales_scraper.login()

    result.expect_success()
    assert result.data == {
        "id": "6c1d1753-ece6-47fa-ab50-251e2b746666",
        "hasScrapeBlockingIssues": False,
    }


def test_microsoft_sales_check_session(
    microsoft_sales_scraper: ScraperRunConfig,
):
    microsoft_sales_scraper.login()
    result = microsoft_sales_scraper.check_session()

    result.expect_success()
    assert result.data == {
        "id": "6c1d1753-ece6-47fa-ab50-251e2b746666",
        "hasScrapeBlockingIssues": False,
    }


def test_microsoft_sales_check_session_without_login(
    microsoft_sales_scraper: ScraperRunConfig,
):
    with pytest.raises(InvalidCredentialsException):
        microsoft_sales_scraper.check_session()


def test_microsoft_sales_scrape(
    microsoft_sales_scraper: ScraperRunConfig,
):
    microsoft_sales_scraper.login()
    result = microsoft_sales_scraper.scrape()
    result.expect_success()
    result.expect_zip_with_manifest()

    assert result.data
    assert result.manifest_json == {
        "dateFrom": "2024-08-21",
        "dateTo": "2024-09-07",
        "manifestVersion": 5,
        "scraperVersion": "0.0.0",
        "fileMetaData": {
            "microsoft_sales-2024-08-21-2024-09-07-BVZ0D05W8MP2-SUPERHOT-SP-Z-OO.json": {
                "humanName": "SUPERHOT",
                "skuId": "BVZ0D05W8MP2",
                "parentSkuId": "BVZ0D05W8MP2",
            },
            "microsoft_sales-2024-08-21-2024-09-07-9NT60N3XPF7T-SUPERHOT-SP-Z-OO.json": {
                "humanName": "SUPERHOT VR",
                "parentSkuId": "9NT60N3XPF7T",
                "skuId": "9NT60N3XPF7T",
            },
            "microsoft_sales-2024-08-21-2024-09-07-9NV17MJB26PG-SUPERHOT-SP-Z-OO.json": {
                "humanName": "SUPERHOT WINDOWS 10",
                "skuId": "9NV17MJB26PG",
                "parentSkuId": "9NV17MJB26PG",
            },
            "microsoft_sales-2024-08-21-2024-09-07-9N64WZTX2K64-SUPERHOT-SP-Z-OO.json": {
                "humanName": "SUPERHOT VR DEMO",
                "skuId": "9N64WZTX2K64",
                "parentSkuId": "9N64WZTX2K64",
            },
            "microsoft_sales-2024-08-21-2024-09-07-9P81RV5VG5H8-SUPERHOT-SP-Z-OO.json": {
                "humanName": "SUPERHOT ONE OF US BUNDLE",
                "skuId": "9P81RV5VG5H8",
                "parentSkuId": "9P81RV5VG5H8",
            },
            "microsoft_sales-2024-08-21-2024-09-07-9NRH78B682L8-SUPERHOT-SP-Z-OO.json": {
                "humanName": "SUPERHOT: MIND CONTROL DELETE",
                "skuId": "9NRH78B682L8",
                "parentSkuId": "9NRH78B682L8",
            },
        },
    }


def test_microsoft_sales_get_organizations(
    microsoft_sales_scraper: ScraperRunConfig,
):
    microsoft_sales_scraper.login()
    result = microsoft_sales_scraper.get_source_side_organizations()
    result.expect_success()

    assert result.data == [
        {
            "id": "6c1d1753-ece6-47fa-ab50-251e2b746666",
            "name": "SUPERHOT-SP-Z-OO",
        },
    ]
