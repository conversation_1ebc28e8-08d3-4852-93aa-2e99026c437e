# Standard libraries
from datetime import date, datetime

# Third party libraries
import pytest
from dateutil import relativedelta

from app.core.source import Source
from tests.process_runner import ScraperRunConfig


@pytest.fixture
def app_store_sales_scraper(
    default_scraper_run_config: ScraperRunConfig, get_credentials
):
    scraper = default_scraper_run_config
    scraper.source = Source.APP_STORE_SALES
    # Set the date to the beginning of previous month
    previous_month = datetime.now() - relativedelta.relativedelta(months=1)
    scraper.date_from = previous_month.replace(day=1).strftime("%Y-%m-%d")
    scraper.date_to = previous_month.replace(day=8).strftime("%Y-%m-%d")
    scraper.proxy_url = None
    scraper.credentials = get_credentials("app_store_app_specific_password")
    return scraper


def test_app_store_sales_login(app_store_sales_scraper: ScraperRunConfig):
    result = app_store_sales_scraper.login()
    result.expect_success()

    result_data = result.data
    assert result_data
    assert result_data["id"] == "*********"
    assert result.data["hasScrapeBlockingIssues"] is False


def test_app_store_sales_check_session(app_store_sales_scraper: ScraperRunConfig):
    app_store_sales_scraper.login().expect_success()
    result = app_store_sales_scraper.check_session()
    result.expect_success()
    assert result.data["id"] == "*********"
    assert result.data["hasScrapeBlockingIssues"] is False


def test_app_store_sales_login_and_scrape(
    app_store_sales_scraper: ScraperRunConfig,
):
    scraping_date = date.fromisoformat(app_store_sales_scraper.date_from)
    scraping_year_and_month = f"{scraping_date.year}-{scraping_date.month:02d}"

    result = app_store_sales_scraper.login_and_scrape()
    result.expect_success()
    result.expect_zip_with_manifest()
    assert result.data
    assert result.manifest_json == {
        "dateFrom": f"{scraping_year_and_month}-01",
        "dateTo": f"{scraping_year_and_month}-08",
        "fileMetaData": {
            f"app_store_sales-{scraping_year_and_month}-01-{scraping_year_and_month}-01-*********-********.tsv": {
                "date": f"{scraping_year_and_month}-01",
                "vendorId": "********",
                "accountNumber": "*********",
            },
            f"app_store_sales-{scraping_year_and_month}-02-{scraping_year_and_month}-02-*********-********.tsv": {
                "date": f"{scraping_year_and_month}-02",
                "vendorId": "********",
                "accountNumber": "*********",
            },
            f"app_store_sales-{scraping_year_and_month}-03-{scraping_year_and_month}-03-*********-********.tsv": {
                "date": f"{scraping_year_and_month}-03",
                "vendorId": "********",
                "accountNumber": "*********",
            },
            f"app_store_sales-{scraping_year_and_month}-04-{scraping_year_and_month}-04-*********-********.tsv": {
                "date": f"{scraping_year_and_month}-04",
                "vendorId": "********",
                "accountNumber": "*********",
            },
            f"app_store_sales-{scraping_year_and_month}-05-{scraping_year_and_month}-05-*********-********.tsv": {
                "date": f"{scraping_year_and_month}-05",
                "vendorId": "********",
                "accountNumber": "*********",
            },
            f"app_store_sales-{scraping_year_and_month}-06-{scraping_year_and_month}-06-*********-********.tsv": {
                "date": f"{scraping_year_and_month}-06",
                "vendorId": "********",
                "accountNumber": "*********",
            },
            f"app_store_sales-{scraping_year_and_month}-07-{scraping_year_and_month}-07-*********-********.tsv": {
                "date": f"{scraping_year_and_month}-07",
                "vendorId": "********",
                "accountNumber": "*********",
            },
            f"app_store_sales-{scraping_year_and_month}-08-{scraping_year_and_month}-08-*********-********.tsv": {
                "date": f"{scraping_year_and_month}-08",
                "vendorId": "********",
                "accountNumber": "*********",
            },
        },
        "manifestVersion": 1,
        "scraperVersion": "0.0.0",
    }


def test_app_store_sales_get_organizations(
    app_store_sales_scraper: ScraperRunConfig,
):
    app_store_sales_scraper.login().expect_success()
    result = app_store_sales_scraper.get_source_side_organizations()
    result.expect_success()
    expected_result = [{"id": "*********", "name": "InnerSloth LLC"}]
    assert expected_result == result.data
