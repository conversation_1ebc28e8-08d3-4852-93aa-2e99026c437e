import pytest

from app.core.source import Source
from tests.process_runner import ScraperRunConfig


@pytest.fixture
def nintendo_sales_scraper(shared_session_default_scraper_run_config):
    scraper = shared_session_default_scraper_run_config
    scraper.source = Source.NINTENDO_WISHLISTS
    scraper.date_from = "2025-01-01"
    scraper.date_to = "2025-01-30"
    return scraper


def test_nintendo_wishlist_combined(
    nintendo_sales_scraper: ScraperRunConfig,
):
    # test get_source_side_organizations
    result = nintendo_sales_scraper.get_source_side_organizations()
    result.expect_success()
    expected_result: list = []
    assert result.data == expected_result

    # test scraping
    result = nintendo_sales_scraper.scrape()
    result.expect_success()
    result.expect_zip_with_manifest()
    assert result.data
    assert result.manifest_json == {
        "dateFrom": "2025-01-01",
        "dateTo": "2025-01-30",
        "fileMetaData": {
            "nintendo_wishlists-2025-01-01-2025-01-30-0.csv": {
                "dateFrom": "2025-01-01",
                "dateTo": "2025-01-30",
                "rawData": True,
            }
        },
        "manifestVersion": 1,
        "scraperVersion": "0.0.0",
    }
