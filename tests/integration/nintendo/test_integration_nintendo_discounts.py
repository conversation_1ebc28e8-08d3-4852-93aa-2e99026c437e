import pytest

from app.core.source import Source
from tests.process_runner import ScraperRunConfig


@pytest.fixture
def unauthorized_nintendo_discounts_scraper(
    default_scraper_run_config, get_credentials
):
    scraper = default_scraper_run_config

    scraper.source = Source.NINTENDO_DISCOUNTS
    scraper.date_from = "2021-01-01"
    scraper.date_to = "2023-10-30"
    scraper.proxy_url = None
    scraper.credentials = get_credentials("nintendo")

    return scraper


@pytest.fixture
def nintendo_discounts_scraper(shared_session_default_scraper_run_config):
    scraper = shared_session_default_scraper_run_config

    scraper.source = Source.NINTENDO_DISCOUNTS
    scraper.date_from = "2021-01-01"
    scraper.date_to = "2023-10-30"

    return scraper


def test_nintendo_discounts_check_session_without_existing_sessions_raises_session_expired(
    unauthorized_nintendo_discounts_scraper: ScraperRunConfig,
):
    result = unauthorized_nintendo_discounts_scraper.check_session()
    result.expect_failure()

    logs = result.stdout_messages_json

    attempts_number = len([log for log in logs if log["message"].startswith("Attempt")])

    assert attempts_number == 1


def test_nintendo_discounts_combined(nintendo_discounts_scraper: ScraperRunConfig):
    """
    Login to Nintendo Discounts and checking session takes a long time. Using combined
    test will save us a lot of time.
    """
    # test get_source_side_organizations
    result = nintendo_discounts_scraper.get_source_side_organizations()
    result.expect_success()
    expected_result: list = []
    assert result.data == expected_result

    # test scraping
    result = nintendo_discounts_scraper.scrape()
    result.expect_success()
    result.expect_zip_with_manifest()
    assert result.data
    assert result.manifest_json == {
        "dateFrom": "2021-01-01",
        "dateTo": "2023-10-30",
        "fileMetaData": {
            "prices.json": {
                "dateFrom": "2021-01-01",
                "dateTo": "2023-10-30",
                "rawData": True,
            },
            "content_lists.json": {
                "dateFrom": "2021-01-01",
                "dateTo": "2023-10-30",
                "rawData": True,
            },
            "discount_groups.json": {
                "dateFrom": "2021-01-01",
                "dateTo": "2023-10-30",
                "rawData": True,
            },
            "country_currency_mapping.json": {
                "dateFrom": "2021-01-01",
                "dateTo": "2023-10-30",
                "rawData": True,
            },
            "discounts.csv": {
                "dateFrom": "2021-01-01",
                "dateTo": "2023-10-30",
                "rawData": False,
            },
        },
        "manifestVersion": 1,
        "scraperVersion": "0.0.0",
    }
