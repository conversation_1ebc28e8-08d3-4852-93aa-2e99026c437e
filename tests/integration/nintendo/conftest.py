import pytest

from app.core.source import Source
from tests.process_runner import ScraperRunConfig


@pytest.fixture(scope="session")
def shared_session_default_scraper_run_config(
    tmp_path_factory: pytest.TempPathFactory, get_credentials
) -> ScraperRunConfig:
    tmp_path = tmp_path_factory.mktemp("shared_session")
    config = ScraperRunConfig(
        source=Source.NINTENDO_SALES,
        date_from="2025-01-01",
        date_to="2025-01-30",
        proxy_url=None,
        report_path=str(tmp_path),
        session_file=str(tmp_path / "session.json"),
        out_session_file=str(tmp_path / "out_session.json"),
        credentials=get_credentials("nintendo"),
    )

    # call login to create session that will be shared across other nintendo tests
    result = config.login()
    result.expect_success()

    result_data = result.data
    assert result_data
    assert result_data["id"] == "Data Aggregation"
    assert result.data["hasScrapeBlockingIssues"] is False

    return config
