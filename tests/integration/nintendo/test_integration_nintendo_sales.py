import json
from pathlib import Path
from unittest.mock import patch

import pytest

from app.core.exceptions import InvalidSessionException
from app.core.source import Source
from tests.process_runner import ScraperRunConfig


@pytest.fixture
def nintendo_sales_scraper(shared_session_default_scraper_run_config):
    scraper = shared_session_default_scraper_run_config
    scraper.source = Source.NINTENDO_SALES
    scraper.date_from = "2025-01-01"
    scraper.date_to = "2025-01-30"
    return scraper


def test_nintendo_sales_combined(
    nintendo_sales_scraper: ScraperRunConfig,
):
    # test get_source_side_organizations
    result = nintendo_sales_scraper.get_source_side_organizations()
    result.expect_success()
    expected_result: list = []
    assert result.data == expected_result

    # test scraping
    result = nintendo_sales_scraper.scrape()
    result.expect_success()
    result.expect_zip_with_manifest()
    assert result.data
    assert result.manifest_json == {
        "dateFrom": "2025-01-01",
        "dateTo": "2025-01-30",
        "fileMetaData": {
            "nintendo_sales-2025-01-01-2025-01-30-0.csv": {
                "dateFrom": "2025-01-01",
                "dateTo": "2025-01-30",
                "rawData": True,
            },
        },
        "manifestVersion": 1,
        "scraperVersion": "0.0.0",
    }


@patch(
    "app.nintendo.nintendo_sst._NintendoSSTScraper.check_session",
    side_effect=InvalidSessionException,
)
def test_nintendo_login_clears_session_after_invalid_session_exception(
    login_mock,
    tmp_path: Path,
    get_credentials,
) -> None:
    input_session_path = tmp_path / "session.json"
    # Create a simple HTTPSession with one cookie
    session_data = {
        "cookies": [
            {
                "name": "sessionid",
                "value": "dummy_value",
                "domain": ".nintendo.com",
                "path": "/",
                "secure": True,
                "httpOnly": True,
            }
        ]
    }
    input_session_path.write_text(json.dumps(session_data))

    output_session_path = tmp_path / "out_session.json"

    scraper = ScraperRunConfig(
        source=Source.NINTENDO_SALES,
        date_from="2025-01-01",
        date_to="2025-01-30",
        proxy_url=None,
        report_path=str(tmp_path),
        session_file=str(input_session_path),
        out_session_file=str(output_session_path),
        credentials=get_credentials("nintendo"),
        direct=True,
    )
    with pytest.raises(InvalidSessionException):
        scraper.check_session()

    cleared_session = output_session_path.read_text()
    assert cleared_session == '{"cookies":[]}'
