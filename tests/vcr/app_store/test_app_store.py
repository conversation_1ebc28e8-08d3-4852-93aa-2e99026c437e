from datetime import datetime, timedelta

import pytest

from app.util.string_functions import format_date
from tests.process_runner import ScraperRunConfig


@pytest.fixture(name="app_store_scraper")
def fixture_app_store_scraper(
    default_scraper_run_config: ScraperRunConfig, get_credentials
):
    now = datetime.now()
    scraper = default_scraper_run_config
    scraper.source = "app_store_sales"
    scraper.date_from = format_date(now - timedelta(days=5))
    scraper.date_to = format_date(now - timedelta(days=2))
    scraper.credentials = get_credentials("app_store_app_specific_password")
    scraper.proxy_url = None
    scraper.direct = True
    return scraper


@pytest.mark.vcr
def test_generate_token_on_login(app_store_scraper: ScraperRunConfig, any_int, any_str):
    result = app_store_scraper.login()
    result.expect_success()

    result_data = result.data
    assert result_data
    assert result_data["id"] == "117350033"

    stdout_json = result.stdout_messages_json

    assert stdout_json[3] == {
        "timestamp": any_str,
        "type": "output",
        "command": "login",
        "originId": "Scraper-py-0.0.0",
        "source": "app_store_sales",
        "metadata": {
            "level_name": "INFO",
            "function_name": "login",
            "line_no": any_int,
            "logger_name": "app.app_store.app_store",
        },
        "message": "Token is missing.",
        "logLevel": 1,
        "version": 2,
    }

    assert stdout_json[-2] == {
        "timestamp": any_str,
        "type": "result",
        "command": "login",
        "originId": "Scraper-py-0.0.0",
        "source": "app_store_sales",
        "metadata": {
            "function_name": "send_result",
            "logger_name": "app.util.messaging",
            "level_name": "INFO",
            "line_no": any_int,
        },
        "message": "",
        "data": {"id": "117350033", "hasScrapeBlockingIssues": False},
        "logLevel": 1,
        "version": 2,
    }

    assert stdout_json[-1] == {
        "timestamp": any_str,
        "type": "trace",
        "command": "login",
        "originId": "Scraper-py-0.0.0",
        "source": "app_store_sales",
        "metadata": {
            "level_name": "DEBUG",
            "function_name": "main",
            "logger_name": "app.main",
            "line_no": any_int,
        },
        "message": "Process completed.",
        "logLevel": 1,
        "version": 2,
    }


@pytest.mark.vcr
def test_get_source_side_organizations(app_store_scraper: ScraperRunConfig):
    app_store_scraper.login()
    result = app_store_scraper.get_source_side_organizations()
    result.expect_success()

    result_data = result.data
    assert result_data


@pytest.mark.vcr
def test_download_report(app_store_scraper: ScraperRunConfig):
    result = app_store_scraper.login_and_scrape()
    result.expect_zip_with_manifest()
