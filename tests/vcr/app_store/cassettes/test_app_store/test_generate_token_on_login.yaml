interactions:
- request:
    body: 'jsonRequest={"version": "2.2", "mode": "Robot.XML", "queryInput": "%5Bp%3DReporter.properties%2C+Sales.viewToken%5D",
      "userid": "indiebi%40innersloth.com", "password": "tklr-fhnd-rbpa-iiao"}'
    headers:
      Accept:
      - text/html,image/gif,image/jpeg; q=.2, */*; q=.2
      Accept-Encoding:
      - gzip, deflate
      Connection:
      - keep-alive
      Content-Length:
      - '190'
      Content-Type:
      - application/x-www-form-urlencoded
      User-Agent:
      - python-requests/2.32.3
    method: POST
    uri: https://reportingitc-reporter.apple.com/reportservice/sales/v1
  response:
    body:
      string: "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>\n<ViewToken>\n
        \   <AccessToken>4108b147-bc18-4a45-94e7-5ce1cf1bf80a</AccessToken>\n    <ExpirationDate>2025-02-20</ExpirationDate>\n</ViewToken>\n"
    headers:
      Connection:
      - keep-alive
      Content-Length:
      - '197'
      Content-Security-Policy:
      - 'upgrade-insecure-requests; style-src  ''self'' *.apple.com blob: *.apple.com;
        font-src  ''self'' *.apple.com *.cdn-apple.com data: *.apple.com; img-src  ''self''
        *.apple.com data: *.apple.com; child-src  ''self'' *.apple.com blob: *.apple.com;
        script-src ''self'' *.apple.com blob: *.apple.com; frame-ancestors ''self''
        *.apple.com; default-src  ''self'' *.apple.com;'
      Content-Type:
      - text/html;charset=iso-8859-1
      Date:
      - Thu, 10 Oct 2024 07:28:55 GMT
      Host:
      - reportingitc-reporter-sh.apple.com
      Server:
      - Apple
      Strict-Transport-Security:
      - max-age=********; includeSubDomains
      - max-age=********; includeSubdomains
      X-Apple-Jingle-Correlation-Key:
      - IQIWIZOEQZCP3YOVUOTHGVMFJM
      X-Content-Type-Options:
      - nosniff
      X-Frame-Options:
      - SAMEORIGIN
      X-XSS-Protection:
      - 1; mode=block
      requestId:
      - 7db699a7-8f38-4aff-b434-2078cc0e8b9a
      x-daiquiri-instance:
      - daiquiri:10001:daiquiri-all-shared-int-679cc46948-vn7l7:7987:24RELEASE221:daiquiri-amp-kubernetes-shared-int-ak8s-prod-as4-amp-daiquiri-ingress-prod
    status:
      code: 200
      message: OK
- request:
    body: 'jsonRequest={"version": "2.2", "mode": "Robot.XML", "queryInput": "%5Bp%3DReporter.properties%2C+Sales.getAccounts%5D",
      "accesstoken": "4108b147-bc18-4a45-94e7-5ce1cf1bf80a"}'
    headers:
      Accept:
      - text/html,image/gif,image/jpeg; q=.2, */*; q=.2
      Accept-Encoding:
      - gzip, deflate
      Connection:
      - keep-alive
      Content-Length:
      - '174'
      Content-Type:
      - application/x-www-form-urlencoded
      User-Agent:
      - python-requests/2.32.3
    method: POST
    uri: https://reportingitc-reporter.apple.com/reportservice/sales/v1
  response:
    body:
      string: "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>\n<Accounts>\n
        \   <Account>\n        <Name>InnerSloth LLC</Name>\n        <Number>*********</Number>\n
        \   </Account>\n</Accounts>\n"
    headers:
      Connection:
      - keep-alive
      Content-Length:
      - '179'
      Content-Security-Policy:
      - 'upgrade-insecure-requests; style-src  ''self'' *.apple.com blob: *.apple.com;
        font-src  ''self'' *.apple.com *.cdn-apple.com data: *.apple.com; img-src  ''self''
        *.apple.com data: *.apple.com; child-src  ''self'' *.apple.com blob: *.apple.com;
        script-src ''self'' *.apple.com blob: *.apple.com; frame-ancestors ''self''
        *.apple.com; default-src  ''self'' *.apple.com;'
      Content-Type:
      - text/html;charset=iso-8859-1
      Date:
      - Thu, 10 Oct 2024 07:28:56 GMT
      Host:
      - reportingitc-reporter-sh.apple.com
      Server:
      - Apple
      Strict-Transport-Security:
      - max-age=********; includeSubDomains
      - max-age=********; includeSubdomains
      X-Apple-Jingle-Correlation-Key:
      - YNPIDR2H2Z4BF5HKRGXYVZTD3Y
      X-Content-Type-Options:
      - nosniff
      X-Frame-Options:
      - SAMEORIGIN
      X-XSS-Protection:
      - 1; mode=block
      requestId:
      - 447b3a11-f3a3-400f-8dd7-3a57ada34062
      x-daiquiri-instance:
      - daiquiri:10001:daiquiri-all-shared-int-679cc46948-sx4k8:7987:24RELEASE221:daiquiri-amp-kubernetes-shared-int-ak8s-prod-as4-amp-daiquiri-ingress-prod
    status:
      code: 200
      message: OK
version: 1
