import pytest

from app.core.exceptions import InvalidCredentialsException
from app.microsoft.api import InvalidClientIdException
from tests.process_runner import ScraperRunConfig


@pytest.mark.vcr
def test_invalid_tenant_settings_raises_invalid_auth_credentials(
    microsoft_sales_scraper: ScraperRunConfig,
):
    microsoft_sales_scraper.credentials = {
        "apiSetupData": [
            {
                "shouldDownload": True,
                "companyName": "Client organization",
                "index": 0,
                "clientId": "random",
                "clientSecret": "not_existing",
                "tenantId": "credentials",
            }
        ]
    }

    with pytest.raises(InvalidCredentialsException):
        microsoft_sales_scraper.login()


@pytest.mark.vcr
def test_invalid_client_id_or_secret_raises_invalid_auth_credentials(
    microsoft_sales_scraper: ScraperRunConfig, get_credentials
):
    ms_credentials = get_credentials("microsoft_store")
    microsoft_sales_scraper.credentials = {
        "apiSetupData": [
            {
                "shouldDownload": True,
                "companyName": "Client organization",
                "index": 0,
                "clientId": "random",
                "clientSecret": "not_existing",
                "tenantId": ms_credentials["apiSetupData"][0]["tenantId"],
            }
        ]
    }

    with pytest.raises(InvalidClientIdException):
        microsoft_sales_scraper.login()
