from unittest.mock import patch

import pytest
import time_machine

from tests.process_runner import ScraperRunConfig


@pytest.fixture(autouse=True)
def mock_sleep_on_microsoft_sales_when_using_recording(record_mode):
    if record_mode == "none":
        with (
            patch("app.microsoft.microsoft_sales.sleep"),
            patch("app.microsoft.api.sleep"),
        ):
            yield
    else:
        yield


@pytest.mark.vcr
@time_machine.travel("2024-10-22 00:00 +0000")
def test_microsoft_sales_scrape(
    microsoft_sales_scraper: ScraperRunConfig,
):
    microsoft_sales_scraper.login()
    result = microsoft_sales_scraper.scrape()
    result.expect_success()
    result.expect_zip_with_manifest()

    assert result.data == [
        {
            "source": "microsoft_sales",
            "startDate": "2024-08-21",
            "endDate": "2024-09-07",
            "noData": False,
            "reportFileName": "microsoft_sales-2024-08-21_2024-09-07.zip",
        }
    ]
    assert result.manifest_json == {
        "dateFrom": "2024-08-21",
        "dateTo": "2024-09-07",
        "scraperVersion": "0.0.0",
        "manifestVersion": 5,
        "fileMetaData": {
            "microsoft_sales-2024-08-21-2024-09-07-BVZ0D05W8MP2-SUPERHOT-SP-Z-OO.json": {
                "humanName": "SUPERHOT",
                "skuId": "BVZ0D05W8MP2",
                "parentSkuId": "BVZ0D05W8MP2",
            },
            "microsoft_sales-2024-08-21-2024-09-07-9NV17MJB26PG-SUPERHOT-SP-Z-OO.json": {
                "humanName": "SUPERHOT WINDOWS 10",
                "skuId": "9NV17MJB26PG",
                "parentSkuId": "9NV17MJB26PG",
            },
            "microsoft_sales-2024-08-21-2024-09-07-9NT60N3XPF7T-SUPERHOT-SP-Z-OO.json": {
                "humanName": "SUPERHOT VR",
                "parentSkuId": "9NT60N3XPF7T",
                "skuId": "9NT60N3XPF7T",
            },
            "microsoft_sales-2024-08-21-2024-09-07-9N64WZTX2K64-SUPERHOT-SP-Z-OO.json": {
                "humanName": "SUPERHOT VR DEMO",
                "skuId": "9N64WZTX2K64",
                "parentSkuId": "9N64WZTX2K64",
            },
            "microsoft_sales-2024-08-21-2024-09-07-9P81RV5VG5H8-SUPERHOT-SP-Z-OO.json": {
                "humanName": "SUPERHOT ONE OF US BUNDLE",
                "skuId": "9P81RV5VG5H8",
                "parentSkuId": "9P81RV5VG5H8",
            },
            "microsoft_sales-2024-08-21-2024-09-07-9NRH78B682L8-SUPERHOT-SP-Z-OO.json": {
                "humanName": "SUPERHOT: MIND CONTROL DELETE",
                "skuId": "9NRH78B682L8",
                "parentSkuId": "9NRH78B682L8",
            },
        },
    }
