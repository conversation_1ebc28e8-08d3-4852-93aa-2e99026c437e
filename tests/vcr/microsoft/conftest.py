import pytest

from app.core.source import Source
from tests.process_runner import ScraperRunConfig


@pytest.fixture
@pytest.mark.block_network
def microsoft_sales_scraper(
    default_scraper_run_config: ScraperRunConfig, get_credentials
) -> ScraperRunConfig:
    scraper = default_scraper_run_config
    scraper.source = Source.MICROSOFT_SALES
    scraper.date_from = "2024-08-21"
    scraper.date_to = "2024-09-07"
    scraper.proxy_url = None
    scraper.credentials = get_credentials("microsoft_store")

    scraper.direct = True

    return scraper


@pytest.fixture(scope="session", autouse=True)
def vcr_config():
    return {
        "filter_headers": ["authorization", "Cookie", "Set-Cookie"],
        "filter_post_data_parameters": [
            "client_id",
            "client_secret",
            "access_token",
            "Date",
            "MS-CorrelationId",
            "x-azure-ref",
            "Set-Cookie",
        ],
    }
