interactions:
- request:
    body: null
    headers:
      Accept:
      - '*/*'
      Accept-Encoding:
      - gzip, deflate
      Connection:
      - keep-alive
      User-Agent:
      - python-requests/2.32.3
    method: GET
    uri: https://login.microsoftonline.com/bc7d1737-04e9-4cc3-8926-a67f1055ca16/v2.0/.well-known/openid-configuration
  response:
    body:
      string: '{"token_endpoint":"https://login.microsoftonline.com/bc7d1737-04e9-4cc3-8926-a67f1055ca16/oauth2/v2.0/token","token_endpoint_auth_methods_supported":["client_secret_post","private_key_jwt","client_secret_basic"],"jwks_uri":"https://login.microsoftonline.com/bc7d1737-04e9-4cc3-8926-a67f1055ca16/discovery/v2.0/keys","response_modes_supported":["query","fragment","form_post"],"subject_types_supported":["pairwise"],"id_token_signing_alg_values_supported":["RS256"],"response_types_supported":["code","id_token","code
        id_token","id_token token"],"scopes_supported":["openid","profile","email","offline_access"],"issuer":"https://login.microsoftonline.com/bc7d1737-04e9-4cc3-8926-a67f1055ca16/v2.0","request_uri_parameter_supported":false,"userinfo_endpoint":"https://graph.microsoft.com/oidc/userinfo","authorization_endpoint":"https://login.microsoftonline.com/bc7d1737-04e9-4cc3-8926-a67f1055ca16/oauth2/v2.0/authorize","device_authorization_endpoint":"https://login.microsoftonline.com/bc7d1737-04e9-4cc3-8926-a67f1055ca16/oauth2/v2.0/devicecode","http_logout_supported":true,"frontchannel_logout_supported":true,"end_session_endpoint":"https://login.microsoftonline.com/bc7d1737-04e9-4cc3-8926-a67f1055ca16/oauth2/v2.0/logout","claims_supported":["sub","iss","cloud_instance_name","cloud_instance_host_name","cloud_graph_host_name","msgraph_host","aud","exp","iat","auth_time","acr","nonce","preferred_username","name","tid","ver","at_hash","c_hash","email"],"kerberos_endpoint":"https://login.microsoftonline.com/bc7d1737-04e9-4cc3-8926-a67f1055ca16/kerberos","tenant_region_scope":"NA","cloud_instance_name":"microsoftonline.com","cloud_graph_host_name":"graph.windows.net","msgraph_host":"graph.microsoft.com","rbac_url":"https://pas.windows.net"}'
    headers:
      Access-Control-Allow-Methods:
      - GET, OPTIONS
      Access-Control-Allow-Origin:
      - '*'
      Cache-Control:
      - max-age=86400, private
      Content-Length:
      - '1753'
      Content-Type:
      - application/json; charset=utf-8
      Date:
      - Wed, 09 Oct 2024 19:21:25 GMT
      P3P:
      - CP="DSP CUR OTPi IND OTRi ONL FIN"
      Set-Cookie:
      - fpc=AtBwACm_zd9OoT0fg6XjvXU; expires=Fri, 08-Nov-2024 19:21:25 GMT; path=/;
        secure; HttpOnly; SameSite=None
      - esctx=PAQABBwEAAADW6jl31mB3T7ugrWTT8pFewQ5VkHnOIF5ZrkiuaQfE_v0UKp-5wHDe3IHtfOuagW-gWGucd_AdvO78MQJrEArOKZljwhTY1fo8PLG7H6zDtd4iEBcE4u-V_J0VfU7JUJhXPAmlRrT2wpewxkwG70UX-AehJ2kCCpRq1Tvz3ctJMt5emZevv4NoACTNavwSAA8gAA;
        domain=.login.microsoftonline.com; path=/; secure; HttpOnly; SameSite=None
      - x-ms-gateway-slice=estsfd; path=/; secure; samesite=none; httponly
      - stsservicecookie=estsfd; path=/; secure; samesite=none; httponly
      Strict-Transport-Security:
      - max-age=31536000; includeSubDomains
      X-Content-Type-Options:
      - nosniff
      X-XSS-Protection:
      - '0'
      x-ms-ests-server:
      - 2.1.19005.9 - SCUS ProdSlices
      x-ms-request-id:
      - 1663da8d-45c0-4785-9352-f79a6d641101
      x-ms-srs:
      - 1.P
    status:
      code: 200
      message: OK
- request:
    body: grant_type=client_credentials&client_info=1&scope=https%3A%2F%2Fmanage.devcenter.microsoft.com%2F.default
    headers:
      Accept:
      - application/json
      Accept-Encoding:
      - gzip, deflate
      Connection:
      - keep-alive
      Content-Length:
      - '149'
      Content-Type:
      - application/x-www-form-urlencoded
      Cookie:
      - fpc=AtBwACm_zd9OoT0fg6XjvXU; x-ms-gateway-slice=estsfd; stsservicecookie=estsfd;
        esctx=PAQABBwEAAADW6jl31mB3T7ugrWTT8pFewQ5VkHnOIF5ZrkiuaQfE_v0UKp-5wHDe3IHtfOuagW-gWGucd_AdvO78MQJrEArOKZljwhTY1fo8PLG7H6zDtd4iEBcE4u-V_J0VfU7JUJhXPAmlRrT2wpewxkwG70UX-AehJ2kCCpRq1Tvz3ctJMt5emZevv4NoACTNavwSAA8gAA
      User-Agent:
      - python-requests/2.32.3
      client-request-id:
      - e309fccf-5615-4a96-88e2-31841e29e23b
      x-client-current-telemetry:
      - 4|730,2|
      x-client-last-telemetry:
      - 4|0|||
      x-client-os:
      - linux
      x-client-sku:
      - MSAL.Python
      x-client-ver:
      - 1.27.0
      x-ms-lib-capability:
      - retry-after, h429
    method: POST
    uri: https://login.microsoftonline.com/bc7d1737-04e9-4cc3-8926-a67f1055ca16/oauth2/v2.0/token
  response:
    body:
      string: '{"error":"unauthorized_client","error_description":"AADSTS700016: Application
        with identifier ''random'' was not found in the directory ''SUPERHOT Sp. z
        o.o.''. This can happen if the application has not been installed by the administrator
        of the tenant or consented to by any user in the tenant. You may have sent
        your authentication request to the wrong tenant. Trace ID: c173d41c-c7ac-4b5e-8167-bd5ccae27b00
        Correlation ID: e309fccf-5615-4a96-88e2-31841e29e23b Timestamp: 2024-10-09
        19:21:26Z","error_codes":[700016],"timestamp":"2024-10-09 19:21:26Z","trace_id":"c173d41c-c7ac-4b5e-8167-bd5ccae27b00","correlation_id":"e309fccf-5615-4a96-88e2-31841e29e23b","error_uri":"https://login.microsoftonline.com/error?code=700016"}'
    headers:
      Cache-Control:
      - no-store, no-cache
      Content-Length:
      - '723'
      Content-Type:
      - application/json; charset=utf-8
      Date:
      - Wed, 09 Oct 2024 19:21:25 GMT
      Expires:
      - '-1'
      P3P:
      - CP="DSP CUR OTPi IND OTRi ONL FIN"
      Pragma:
      - no-cache
      Set-Cookie:
      - fpc=AtBwACm_zd9OoT0fg6XjvXWd812IAQAAALXOmN4OAAAA; expires=Fri, 08-Nov-2024
        19:21:26 GMT; path=/; secure; HttpOnly; SameSite=None
      - x-ms-gateway-slice=estsfd; path=/; secure; samesite=none; httponly
      Strict-Transport-Security:
      - max-age=31536000; includeSubDomains
      X-Content-Type-Options:
      - nosniff
      X-XSS-Protection:
      - '0'
      client-request-id:
      - e309fccf-5615-4a96-88e2-31841e29e23b
      x-ms-clitelem:
      - 1,700016,0,,
      x-ms-ests-server:
      - 2.1.19005.9 - EUS ProdSlices
      x-ms-request-id:
      - c173d41c-c7ac-4b5e-8167-bd5ccae27b00
      x-ms-srs:
      - 1.P
    status:
      code: 400
      message: Bad Request
- request:
    body: null
    headers:
      Accept:
      - application/json
      Accept-Encoding:
      - gzip, deflate
      Connection:
      - keep-alive
      Cookie:
      - fpc=AtBwACm_zd9OoT0fg6XjvXWd812IAQAAALXOmN4OAAAA; x-ms-gateway-slice=estsfd;
        stsservicecookie=estsfd; esctx=PAQABBwEAAADW6jl31mB3T7ugrWTT8pFewQ5VkHnOIF5ZrkiuaQfE_v0UKp-5wHDe3IHtfOuagW-gWGucd_AdvO78MQJrEArOKZljwhTY1fo8PLG7H6zDtd4iEBcE4u-V_J0VfU7JUJhXPAmlRrT2wpewxkwG70UX-AehJ2kCCpRq1Tvz3ctJMt5emZevv4NoACTNavwSAA8gAA
      User-Agent:
      - python-requests/2.32.3
    method: GET
    uri: https://login.microsoftonline.com/common/discovery/instance?api-version=1.1&authorization_endpoint=https://login.microsoftonline.com/common/oauth2/authorize
  response:
    body:
      string: '{"tenant_discovery_endpoint":"https://login.microsoftonline.com/common/.well-known/openid-configuration","api-version":"1.1","metadata":[{"preferred_network":"login.microsoftonline.com","preferred_cache":"login.windows.net","aliases":["login.microsoftonline.com","login.windows.net","login.microsoft.com","sts.windows.net"]},{"preferred_network":"login.partner.microsoftonline.cn","preferred_cache":"login.partner.microsoftonline.cn","aliases":["login.partner.microsoftonline.cn","login.chinacloudapi.cn"]},{"preferred_network":"login.microsoftonline.de","preferred_cache":"login.microsoftonline.de","aliases":["login.microsoftonline.de"]},{"preferred_network":"login.microsoftonline.us","preferred_cache":"login.microsoftonline.us","aliases":["login.microsoftonline.us","login.usgovcloudapi.net"]},{"preferred_network":"login-us.microsoftonline.com","preferred_cache":"login-us.microsoftonline.com","aliases":["login-us.microsoftonline.com"]}]}'
    headers:
      Access-Control-Allow-Methods:
      - GET, OPTIONS
      Access-Control-Allow-Origin:
      - '*'
      Cache-Control:
      - max-age=86400, private
      Content-Length:
      - '945'
      Content-Type:
      - application/json; charset=utf-8
      Date:
      - Wed, 09 Oct 2024 19:21:25 GMT
      P3P:
      - CP="DSP CUR OTPi IND OTRi ONL FIN"
      Set-Cookie:
      - fpc=AtBwACm_zd9OoT0fg6XjvXWd812IAQAAALXOmN4OAAAA; expires=Fri, 08-Nov-2024
        19:21:26 GMT; path=/; secure; HttpOnly; SameSite=None
      - x-ms-gateway-slice=estsfd; path=/; secure; samesite=none; httponly
      Strict-Transport-Security:
      - max-age=31536000; includeSubDomains
      X-Content-Type-Options:
      - nosniff
      X-XSS-Protection:
      - '0'
      x-ms-ests-server:
      - 2.1.19005.9 - FRC ProdSlices
      x-ms-request-id:
      - 5273582e-b038-4a84-9a0a-65a10f823801
      x-ms-srs:
      - 1.P
    status:
      code: 200
      message: OK
version: 1
