import json
import logging
import os
from pathlib import Path

import pytest
import responses
from cryptography.fernet import Ferne<PERSON>

from app.logs import <PERSON><PERSON><PERSON><PERSON><PERSON>, CustomJSONFormatter, _set_global_context
from tests.process_runner import ScraperRunConfig


class AnyStr(str):
    def __eq__(self, other):
        return type(other) is str


@pytest.fixture
def any_str() -> AnyStr:
    return AnyStr()


class AnyInt:
    def __eq__(self, other):
        return isinstance(other, int)


@pytest.fixture
def any_int() -> AnyInt:
    return AnyInt()


@pytest.fixture(scope="session", autouse=True)
def disable_cassette_spamming_logs():
    # Disable this logger because it sends to log whole content of request (also reports)
    logging.getLogger("vcr.cassette").setLevel(logging.WARNING)


@pytest.fixture(scope="session")
def get_credentials():
    credentials_file = Path(__file__).parent / "credentials_encrypted.json"
    with open(credentials_file, "rb") as f:
        encrypted_data = f.read()

        fernet = Fernet(os.environ["SCP_CREDENTIALS_ENCRYPTION_KEY"])
        decrypted_data = fernet.decrypt(encrypted_data)
        credentials = json.loads(decrypted_data)

    def _get_credentials(name):
        return credentials[name]

    return _get_credentials


@pytest.fixture
def default_scraper_run_config(tmp_path: Path, request: pytest.FixtureRequest):
    config = ScraperRunConfig(
        source=None,
        date_from="2021-11-01",
        date_to="2021-11-30",
        report_path=str(tmp_path),
        session_file=str(tmp_path / "session.json"),
        out_session_file=str(tmp_path / "out_session.json"),
    )

    exec_path = request.config.getoption("--executable-path")
    if exec_path and isinstance(exec_path, str):
        config.executable = exec_path

    return config


def record_mode(request):
    return request.config.getoption("--record-mode")


def pytest_addoption(parser: pytest.Parser):
    parser.addoption(
        "--executable-path",
        action="store",
        default=None,
        help="path of the executable to test.",
    )


@pytest.fixture
def responses_mock():
    with responses.RequestsMock() as mock:
        yield mock


@pytest.fixture(autouse=True)
def _configure_logger(caplog: pytest.LogCaptureFixture):
    formatter = CustomJSONFormatter(
        default_time_format="%Y-%m-%dT%H:%M:%S",
        default_msec_format="%s.%03dZ",
        fields={
            "timestamp": "asctime",
            "metadata": "metadata",
            "message": "message",
        },
    )
    caplog.handler.setFormatter(formatter)
    _set_global_context()
    with caplog.filtering(ContextFilter()), caplog.at_level(logging.DEBUG):
        yield caplog


@pytest.fixture
def get_all_logs(caplog: pytest.LogCaptureFixture):
    def _all_logs():
        return [json.loads(log) for log in caplog.text.splitlines()]

    return _all_logs
