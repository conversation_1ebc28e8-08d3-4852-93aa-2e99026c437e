import glob
import io
import json
import logging
import os
import subprocess
import sys
import time
from collections.abc import Mapping, Sequence
from contextlib import contextmanager, redirect_stderr, redirect_stdout
from dataclasses import dataclass, field
from pathlib import Path
from typing import Any, Optional
from zipfile import ZipFile

log: logging.Logger = logging.getLogger(__name__)


@dataclass
class ScraperRunResult:
    exit_code: int
    execution_time_seconds: float
    report_file: Optional[str]
    output_session_file: str
    scraper: "ScraperRunConfig"

    stdout: Sequence[str]

    @property
    def manifest_json(self):
        return json.loads(self.read_from_report_zip("manifest.json"))

    @property
    def additional_data_json(self):
        s = self.scraper
        filename = f"additionalData_{s._source_no_version}\
            _{s.date_from}_{s.date_to}.json"  # pylint: disable=protected-access
        return json.loads(self.read_from_report_zip(filename))

    @contextmanager
    def _as_zipfile(self):
        assert self.report_file
        with ZipFile(self.report_file) as zip_:
            yield zip_

    def read_from_report_zip(self, filename: str):
        with self._as_zipfile() as report_zip:
            return report_zip.read(filename)

    def get_info_from_report_zip(self, filename: str):
        with self._as_zipfile() as report_zip:
            return report_zip.getinfo(filename)

    @property
    def output_session_json(self):
        with open(self.output_session_file) as f:  # pylint: disable=unspecified-encoding
            return json.load(f)

    @property
    def stdout_messages_json(self):
        messages: list[Any] = []

        for line in self.stdout:
            try:
                messages.append(json.loads(line))
            except json.JSONDecodeError as e:
                log.warning(
                    "Could not parse line as JSON -possible bug in scraper: %s, %s",
                    line,
                    e,
                )

        return messages

    @property
    def telemetry_events(self):
        return [
            message
            for message in self.stdout_messages_json
            if message.get("type") == "telemetry event"
        ]

    @property
    def result_message(self):
        result_messages = [
            message
            for message in self.stdout_messages_json
            if message.get("type") == "result"
        ]
        if result_messages == []:
            return None
        assert len(result_messages) == 1
        return result_messages[0]

    @property
    def data(self):
        assert self.result_message
        return self.result_message["data"]

    def expect_csv(self):
        self._expect_report(".csv")

    def expect_zip(self):
        self._expect_report(".zip")

    def expect_success(self):
        assert self.exit_code == 0

    def _expect_report(self, extension: str):
        self.expect_success()
        assert self.report_file
        assert os.path.basename(self.report_file) == self._report_name(extension)

    def expect_zip_with_manifest(self):
        self.expect_zip()
        manifest = self.manifest_json
        assert isinstance(manifest, dict)
        assert len(manifest) > 0

    def expect_zip_with_additional_data(self):
        self.expect_zip()
        additional_data = self.additional_data_json
        s = self.scraper
        assert (
            additional_data["scraper"] == s._source_no_version  # pylint: disable=protected-access
        )
        assert additional_data["startDate"] == f"{s.date_from}T00:00:00.000Z"
        assert additional_data["endDate"] == f"{s.date_to}T00:00:00.000Z"

    def expect_nonempty_report_in_zip(self, index: str | None):
        filename_in_zip = self._report_name(".csv", index)
        assert self.get_info_from_report_zip(filename_in_zip).file_size > 0
        assert self.read_from_report_zip(filename_in_zip).count(b"\n") > 10

    def get_report_file_size(self, index: str):
        filename_in_zip = self._report_name(".csv", index)
        return self.get_info_from_report_zip(filename_in_zip).file_size

    def expect_failure(
        self,
        exit_code: int | None = None,
        expected_error_message_part: Any | None = None,
    ):
        if exit_code is None:
            assert self.exit_code != 0, (
                f"Expected scraper run to fail, but exit code was {self.exit_code}"
            )
        else:
            assert self.exit_code == exit_code, (
                f"Expected scraper run to fail with exit code {exit_code}, \
                but exit code was {self.exit_code}"
            )

        if expected_error_message_part:
            error_message = self.stdout_messages_json[-2]
            assert expected_error_message_part in error_message.get("data", "")
            assert error_message.get("type", "") == "error"

    def expect_telemetry_event(self, event_message: str, count: int = 1):
        event_messages = [event.get("message") for event in self.telemetry_events]
        assert event_messages.count(event_message) == count

    def list_files_in_zip(self):
        with self._as_zipfile() as report_zip:
            return [info.filename for info in report_zip.filelist]

    def _report_name(self, extension: str = ".csv", index: str | None = None):
        suffix = f"-{index}" if index else ""
        s = self.scraper
        source_no_version = s._source_no_version  # pylint: disable=protected-access
        return f"{source_no_version}-{s.date_from}_{s.date_to}{suffix}{extension}"


@dataclass
class ScraperRunConfig:
    executable = "python -m app.main"
    working_directory: str = str(Path(__file__).parents[1])
    proxy_url: Optional[str] = "http://127.0.0.1:8000"
    credentials: Optional[dict[str, Any]] = field(default=None, repr=False)
    excluded_skus: Optional[list[str]] = None
    excluded_orgs: Optional[str] = None
    command: Optional[str] = None
    source: Optional[str] = None
    date_from: Optional[str] = None
    date_to: Optional[str] = None
    report_path: Optional[str] = "data"
    output: Optional[str] = "json"
    encryption_token: Optional[str] = None
    session_file: str = "session.json"
    out_session_file: str = "session-out.json"
    debug: bool = False
    direct: bool = False

    @property
    def _source_no_version(self):
        return self.source.split(":")[0] if self.source else None

    def scrape(self):
        return self.run("scrape")

    def check_session(self):
        return self.run("check-session")

    def get_source_side_organizations(self):
        return self.run("get-source-side-organizations")

    def get_manual_login_details(self):
        return self.run("get-manual-login-details")

    def login_and_scrape(self):
        login_result = self.login()
        login_result.expect_success()
        return self.scrape()

    def login(self):
        result = self.run("login")
        self.session_file = result.output_session_file
        return result

    def run(self, cmd: str | None = None) -> ScraperRunResult:
        env_vars = {
            "NDBI_PROXY_URL": self.proxy_url,
            "NDBI_CREDENTIALS": json.dumps(self.credentials)
            if self.credentials is not None
            else None,
            "NDBI_ENCRYPTION_TOKEN": self.encryption_token,
            "NDBI_DEBUG_PY": "1" if self.debug else None,
        }

        arguments = {
            "--source": self.source,
            "--from": self.date_from if cmd == "scrape" else None,
            "--to": self.date_to if cmd == "scrape" else None,
            "--reportPath": self.report_path if cmd == "scrape" else None,
            "--excludedSkus": json.dumps(self.excluded_skus)
            if self.excluded_skus is not None and cmd == "scrape"
            else None,
            "--excludedOrgs": json.dumps(self.excluded_orgs)
            if self.excluded_orgs is not None and cmd == "scrape"
            else None,
            "--output": self.output,
            "--encrypt": self.encryption_token is not None
            if cmd != "get-manual-login-details"
            else None,
            "--sessionFile": self.session_file
            if cmd != "get-manual-login-details"
            else None,
            "--outputSessionFile": self.out_session_file
            if cmd != "get-manual-login-details"
            else None,
        }

        start_time = time.monotonic()

        command = [*self.executable.split(" "), cmd or self.command or ""]
        command += [
            f"{key}={value}" for key, value in arguments.items() if value is not None
        ]

        run_fn = self._run_direct if self.direct else self._run_subprocess

        exit_code, output_lines = run_fn(command, env_vars)

        time_elapsed = time.monotonic() - start_time

        report_file = None

        if self.report_path:  # TODO: it is never set to None
            source = self._source_no_version
            report_files = glob.glob(
                os.path.join(
                    self.report_path, f"{source}-{self.date_from}_{self.date_to}.*"
                )
            )
            if report_files:
                report_file = report_files[0]

        return ScraperRunResult(
            exit_code=exit_code,
            execution_time_seconds=time_elapsed,
            report_file=report_file,
            output_session_file=self.out_session_file,
            stdout=output_lines,
            scraper=self,
        )

    def _run_subprocess(
        self, command: list[str], env_vars: dict[str, str]
    ) -> tuple[int, list[str]]:
        log.info("Running scraper: %s", command)

        with subprocess.Popen(  # noqa: S603
            command,
            cwd=self.working_directory,
            env=_extend_env_vars(env_vars),
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
        ) as process:
            assert process.stdout
            output_lines: list[str] = []
            for line in process.stdout:
                sys.stdout.write(line)
                output_lines.append(line)
            process.wait()

        return process.returncode, output_lines

    def _run_direct(
        self, command: list[str], env_vars: dict[str, str]
    ) -> tuple[int, list[str]]:
        f = io.StringIO()
        with _modify_env(env_vars), redirect_stdout(f), redirect_stderr(f):
            from app import main  # pylint: disable=import-outside-toplevel

            return_code = main.main(command[3:], standalone=False)
            logging.shutdown()  # make sure all logs are flushed so we can parse them properly

        output = f.getvalue()
        print(output)  # noqa: T201

        return return_code, output.splitlines()


@contextmanager
def _modify_env(env_vars: dict[str, str]):
    old_environ = dict(os.environ)
    os.environ.update({k: v for k, v in env_vars.items() if v is not None})
    try:
        yield
    finally:
        os.environ.clear()
        os.environ.update(old_environ)


def _extend_env_vars(vars_: Mapping[str, str | None]):
    env = os.environ.copy()
    for k, v in vars_.items():
        if v is not None:
            env[k] = v
    return env
