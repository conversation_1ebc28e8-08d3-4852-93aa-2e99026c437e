import json
from datetime import date
from pathlib import Path
from typing import Any
from zipfile import ZipFile

import pytest

from app.core.scraper import Manifest<PERSON>ileFields, ReportZip
from app.core.source import Source
from app.core.types import CSV, JSON


@pytest.fixture
def date_from() -> date:
    return date(2020, 1, 1)


@pytest.fixture
def date_to() -> date:
    return date(2020, 1, 31)


@pytest.fixture
def report_zip(tmp_path: Path, date_from, date_to) -> ReportZip[Any]:
    return ReportZip[Any](
        report_path=tmp_path,
        source=Source.NINTENDO_DISCOUNTS,
        date_from=date_from,
        date_to=date_to,
    )


@pytest.fixture
def expected_zip_path(tmp_path: Path) -> Path:
    return tmp_path / "nintendo_discounts-2020-01-01_2020-01-31.zip"


def test_create_report(report_zip: ReportZip[Any], expected_zip_path: Path):
    with report_zip:
        test_meta_data = ManifestFileFields(
            date_from=date(2020, 1, 1), date_to=date(2020, 1, 31), raw_data=True
        )
        test_data = "content of the file"

        with report_zip.create_report("test_report.txt", test_meta_data) as file:
            file.write(test_data.encode())

    assert expected_zip_path.exists()

    created_zip = ZipFile(expected_zip_path)
    assert created_zip.namelist() == ["test_report.txt", "manifest.json"]
    assert created_zip.read("test_report.txt").decode() == test_data
    assert json.loads(created_zip.read("manifest.json").decode()) == {
        "dateFrom": "2020-01-01",
        "dateTo": "2020-01-31",
        "fileMetaData": {
            "test_report.txt": {
                "dateFrom": "2020-01-01",
                "dateTo": "2020-01-31",
                "rawData": True,
            },
        },
        "manifestVersion": 1,
        "scraperVersion": "0.0.0",
    }


def test_add_json_file(report_zip: ReportZip[Any], expected_zip_path: Path):
    test_data: JSON = {"test": "data"}
    test_file_name = "test_report.json"
    with report_zip:
        report_zip.add_json_file(
            test_data,
            test_file_name,
            date(2020, 1, 1),
            date(2020, 1, 31),
        )

    assert expected_zip_path.exists()
    created_zip = ZipFile(expected_zip_path)
    assert created_zip.namelist() == [test_file_name, "manifest.json"]

    assert json.loads(created_zip.read(test_file_name).decode()) == test_data

    assert json.loads(created_zip.read("manifest.json").decode()) == {
        "dateFrom": "2020-01-01",
        "dateTo": "2020-01-31",
        "fileMetaData": {
            "test_report.json": {
                "dateFrom": "2020-01-01",
                "dateTo": "2020-01-31",
                "rawData": True,
            },
        },
        "manifestVersion": 1,
        "scraperVersion": "0.0.0",
    }


def test_add_csv_file(report_zip: ReportZip[Any], expected_zip_path: Path):
    test_data: CSV = {
        "headers": ["Country", "City"],
        "rows": [
            {"Country": "Germany", "City": "Berlin"},
            {"Country": "France", "City": "Paris"},
        ],
    }
    test_file_name = "test_report.csv"

    with report_zip:
        report_zip.add_csv_file(
            test_data,
            test_file_name,
            date(2020, 1, 1),
            date(2020, 1, 31),
        )

    assert expected_zip_path.exists()
    created_zip = ZipFile(expected_zip_path)
    assert created_zip.namelist() == [test_file_name, "manifest.json"]

    assert (
        created_zip.read(test_file_name).decode()
        == "Country,City\r\nGermany,Berlin\r\nFrance,Paris\r\n"
    )

    assert json.loads(created_zip.read("manifest.json").decode()) == {
        "dateFrom": "2020-01-01",
        "dateTo": "2020-01-31",
        "fileMetaData": {
            "test_report.csv": {
                "dateFrom": "2020-01-01",
                "dateTo": "2020-01-31",
                "rawData": True,
            },
        },
        "manifestVersion": 1,
        "scraperVersion": "0.0.0",
    }


def test_multiple_files_can_be_added_to_a_report(
    report_zip: ReportZip[Any], expected_zip_path: Path
):
    data_1: JSON = {"test": "data"}
    file_name_1 = "test_report_1.json"
    date_from_1 = date(2021, 6, 1)
    date_to_1 = date(2021, 6, 30)

    data_2: JSON = {"test": "data"}
    file_name_2 = "test_report_2.json"
    date_from_2 = date(2021, 7, 1)
    date_to_2 = date(2021, 7, 31)

    data_3: CSV = {
        "headers": ["Country", "City"],
        "rows": [
            {"Country": "Germany", "City": "Berlin"},
            {"Country": "France", "City": "Paris"},
        ],
    }
    file_name_3 = "test_report_3.csv"
    date_from_3 = date(2021, 8, 1)
    date_to_3 = date(2021, 8, 31)
    with report_zip:
        report_zip.add_json_file(
            data_1,
            file_name_1,
            date_from_1,
            date_to_1,
        )
        report_zip.add_json_file(
            data_2,
            file_name_2,
            date_from_2,
            date_to_2,
            raw_data=False,
        )
        report_zip.add_csv_file(
            data_3,
            file_name_3,
            date_from_3,
            date_to_3,
        )

    assert expected_zip_path.exists()

    created_zip = ZipFile(expected_zip_path)
    assert created_zip.namelist() == [
        file_name_1,
        file_name_2,
        file_name_3,
        "manifest.json",
    ]

    assert json.loads(created_zip.read(file_name_1).decode()) == data_1
    assert json.loads(created_zip.read(file_name_2).decode()) == data_2
    assert (
        created_zip.read(file_name_3).decode()
        == "Country,City\r\nGermany,Berlin\r\nFrance,Paris\r\n"
    )

    assert json.loads(created_zip.read("manifest.json").decode()) == {
        "dateFrom": "2020-01-01",
        "dateTo": "2020-01-31",
        "fileMetaData": {
            "test_report_1.json": {
                "dateFrom": "2021-06-01",
                "dateTo": "2021-06-30",
                "rawData": True,
            },
            "test_report_2.json": {
                "dateFrom": "2021-07-01",
                "dateTo": "2021-07-31",
                "rawData": False,
            },
            "test_report_3.csv": {
                "dateFrom": "2021-08-01",
                "dateTo": "2021-08-31",
                "rawData": True,
            },
        },
        "manifestVersion": 1,
        "scraperVersion": "0.0.0",
    }
