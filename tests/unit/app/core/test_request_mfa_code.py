from copy import deepcopy
from datetime import UTC, datetime
from unittest.mock import patch

import pytest
from responses import RequestsMock, matchers

from app.core.exceptions import (
    InvalidTOTPSecretException,
    MFARequiredException,
    MFATooManyAttemptsException,
)
from app.core.mfa import M<PERSON>, ManualMFAConfig, TOTPConfig
from app.core.source import Source


@pytest.fixture
def totp_config() -> TOTPConfig:
    return TOTPConfig(
        code="FCKGWRHQQ2",
        source=Source.NINTENDO_SALES,
        for_time=datetime(year=2025, month=1, day=1, tzinfo=UTC),
    )


@pytest.fixture
def manual_mfa_config() -> ManualMFAConfig:
    return ManualMFAConfig(
        api_url="https://example.com",
        api_token="token",
        source=Source.NINTENDO_DISCOUNTS,
    )


@pytest.fixture(autouse=True)
def patched_time_sleep():
    with patch("time.sleep", return_value=None) as mock_sleep:
        yield mock_sleep


def test_request_mfa_code_with_valid_totp_code_returns_mfa(totp_config) -> None:
    mfa_code = MFA.get_authenticator(totp_config).request()

    assert mfa_code == "811386"


def test_request_mfa_code_with_invalid_totp_raises_invalid_totp_code(
    totp_config,
) -> None:
    totp_config.code = "INVALID_CODE"
    with pytest.raises(InvalidTOTPSecretException):
        MFA.get_authenticator(totp_config).request()


def test_request_without_totp_sends_request_to_stdout(
    get_all_logs,
    manual_mfa_config,
    responses_mock: RequestsMock,
    any_int,
    any_str,
) -> None:
    responses_mock.get(
        "https://example.com/auth-code/nintendo_discounts", json={"authCode": 1234}
    )
    mfa: MFA = MFA.get_authenticator(manual_mfa_config)
    mfa.request()
    mfa.request()

    expected_message = {
        "type": "dualAuth",
        "logLevel": 1,
        "source": "nintendo_discounts",
        "originId": "Scraper-py-0.0.0",
        "attempt": 1,
        "maxAttempts": 5,
        "message": "None",
        "metadata": {
            "function_name": "send_mfa_request",
            "level_name": "INFO",
            "line_no": any_int,
            "logger_name": "app.util.messaging",
        },
        "timestamp": any_str,
        "version": 2,
    }
    expected_second_try_message = deepcopy(expected_message)
    expected_second_try_message["attempt"] = 2

    all_dual_auth_logs = list(filter(lambda x: x["type"] == "dualAuth", get_all_logs()))

    assert all_dual_auth_logs == [
        expected_message,
        expected_second_try_message,
    ]


def test_request_without_totp_waits_for_mfa_from_scraper_api_using_JWT_token(  # noqa: N802
    manual_mfa_config, responses_mock: RequestsMock
) -> None:
    responses_mock.get(
        "https://example.com/auth-code/nintendo_discounts",
        match=[
            matchers.header_matcher({
                "Authorization": "Bearer token",
            })
        ],
        json={"authCode": 1234},
    )

    mfa = MFA.get_authenticator(manual_mfa_config)

    assert mfa.request() == 1234


def test_request_without_totp_waits_for_non_empty_code(
    patched_time_sleep,
    manual_mfa_config,
    responses_mock: RequestsMock,
) -> None:
    for returned_code in [None, "1234"]:
        responses_mock.get(
            "https://example.com/auth-code/nintendo_discounts",
            json={"authCode": returned_code},
        )

    mfa = MFA.get_authenticator(manual_mfa_config)

    assert mfa.request() == "1234"
    assert patched_time_sleep.call_count == 1


def test_request_without_totp_raises_MISSING_2FA_after_5_minutes(  # noqa: N802
    patched_time_sleep,
    manual_mfa_config,
    responses_mock: RequestsMock,
) -> None:
    responses_mock.get(
        "https://example.com/auth-code/nintendo_discounts",
        json={"authCode": None},
    )

    mfa = MFA.get_authenticator(manual_mfa_config)

    with pytest.raises(MFARequiredException):
        mfa.request()

    assert patched_time_sleep.call_count == 30  # 5 minutes / 10 seconds per request


def test_request_with_too_many_attempts_throws_TOO_MANY_2FA_ATTEMPTS(  # noqa: N802
    manual_mfa_config,
    responses_mock: RequestsMock,
) -> None:
    responses_mock.get(
        "https://example.com/auth-code/nintendo_discounts",
        json={"authCode": "1234"},
    )

    mfa = MFA.get_authenticator(manual_mfa_config)

    mfa.request()
    mfa.request()
    mfa.request()
    mfa.request()
    mfa.request()

    with pytest.raises(MFATooManyAttemptsException):
        mfa.request()
