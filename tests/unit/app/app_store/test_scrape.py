from datetime import date
from pathlib import Path
from unittest.mock import MagicMock

import pytest

from app.app_store import reporter_api
from app.app_store.app_store import (
    AppStoreSalesScraper,
)
from app.core.exceptions import (
    MissingPermissionsException,
    TryAgainLaterException,
)
from app.core.scraper import ReportZip, ScrapeInfo
from app.core.source import Source
from app.core.types import FeatureFlags


def test_app_store_scrape_get_no_organization(appstore_session):
    app_store_scraper: AppStoreSalesScraper = AppStoreSalesScraper(
        credentials=None,
        session=appstore_session,
        http=None,
        feature_flags=FeatureFlags(root=[]),
    )

    reporter_api.get_accounts = MagicMock(return_value=[])

    with pytest.raises(MissingPermissionsException):
        app_store_scraper.scrape(
            from_=date(2021, 1, 1),
            to=date(2021, 1, 31),
            report_zip=ReportZip(
                report_path=Path("test_report.zip"),
                source=Source.APP_STORE_SALES,
                date_from=date(2021, 1, 1),
                date_to=date(2021, 1, 31),
            ),
            excluded_skus=[],
        )


def test_app_store_scrape_get_one_organization(
    appstore_credentials,
    appstore_session,
    account,
    organization,
):
    app_store_scraper: AppStoreSalesScraper = AppStoreSalesScraper(
        credentials=appstore_credentials,
        session=appstore_session,
        http=None,
        feature_flags=FeatureFlags(root=[]),
    )

    app_store_scraper.get_organizations = MagicMock(return_value=[organization])
    app_store_scraper._scrape_account = MagicMock(return_value=True)
    reporter_api.get_accounts = MagicMock(return_value=[account])

    result: ScrapeInfo = app_store_scraper.scrape(
        from_=date(2021, 1, 1),
        to=date(2021, 1, 31),
        report_zip=ReportZip(
            report_path=Path("test_report.zip"),
            source=Source.APP_STORE_SALES,
            date_from=date(2021, 1, 1),
            date_to=date(2021, 1, 31),
        ),
        excluded_skus=[],
    )
    assert result == ScrapeInfo(no_data=False)


def test_app_store_scrape_get_one_organization_but_no_data_for_given_period(
    appstore_session, account, organization
):
    app_store_scraper: AppStoreSalesScraper = AppStoreSalesScraper(
        credentials=None,
        session=appstore_session,
        http=None,
        feature_flags=FeatureFlags(root=[]),
    )

    app_store_scraper.get_organizations = MagicMock(return_value=[organization])
    reporter_api.get_accounts = MagicMock(return_value=[account])
    app_store_scraper._scrape_account = MagicMock(return_value=False)

    with pytest.raises(TryAgainLaterException):
        app_store_scraper.scrape(
            from_=date(2021, 1, 1),
            to=date(2021, 1, 31),
            report_zip=ReportZip(
                report_path=Path("test_report.zip"),
                source=Source.APP_STORE_SALES,
                date_from=date(2021, 1, 1),
                date_to=date(2021, 1, 31),
            ),
            excluded_skus=[],
        )
