import requests

from app.app_store import reporter_api
from app.app_store.reporter_api import get_vendors


def test_get_vendors_no_vendors(mock_response_no_vendors):
    vendors = list(
        reporter_api.get_vendors(
            http=requests.Session(),
            token="sample_token",
            account="Sample_Account_1",
        )
    )
    assert vendors == []


def test_get_vendors_one_vendor(mock_response_one_vendor):
    vendors = list(
        reporter_api.get_vendors(
            http=requests.Session(),
            token="sample_token",
            account="Sample_Account_1",
        )
    )
    assert vendors == ["Sample_name_1"]


def test_get_vendors_two_vendors(mock_response_two_vendors):
    vendors = list(
        reporter_api.get_vendors(
            http=requests.Session(),
            token="sample_token",
            account="Sample_Account_1",
        )
    )
    assert vendors == ["Sample_name_1", "Sample_name_2"]


def test_get_vendors_two_vendors_get_first_one_only(mock_response_one_vendor):
    vendors = list(
        get_vendors(
            http=requests.Session(),
            token="sample_token",
            account="Sample_Account_1",
            selected_vendors=["Sample_name_1"],
        )
    )
    assert vendors == ["Sample_name_1"]


def test_get_vendors_two_vendors_not_selected(mock_response_no_vendors):
    vendors = list(
        get_vendors(
            http=requests.Session(),
            token="sample_token",
            account="Sample_Account_1",
            selected_vendors=["Sample_name_not_in_the_list"],
        )
    )
    assert vendors == []
