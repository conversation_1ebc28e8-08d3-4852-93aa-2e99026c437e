import json
from datetime import date
from unittest.mock import Mock

import pytest
import responses
import responses.matchers

from app.app_store.app_store import AppStoreCredentials, AppStoreSession, Token
from app.core.scraper import Organization


@pytest.fixture
def account(number: int = 123) -> dict:
    return {"number": number, "text": f"sample_account_{number}"}


@pytest.fixture
def organization(number: int = ********) -> Organization:
    return Organization(
        id=str(number),
        name=f"sample_organization_{number}",
        has_scrape_blocking_issues=False,
    )


@pytest.fixture
def appstore_credentials() -> AppStoreCredentials:
    return AppStoreCredentials(
        token="test_token",
        user="test_user",
        password="test_password",
    )


@pytest.fixture
def appstore_session() -> AppStoreSession:
    return AppStoreSession(
        token=Token(
            value="test_token",
            expiration_date=date(2025, 12, 31),
        )
    )


_vendor_request: dict = {
    "version": "2.2",
    "mode": "Robot.XML",
    "queryInput": "%5Bp%3DReporter.properties%2C+Sales.getVendors%5D",
    "accesstoken": "sample_token",
    "account": "Sample_Account_1",
}

_headers: dict = {
    "Accept": "text/html,image/gif,image/jpeg; q=.2, */*; q=.2",
    "Content-Type": "application/x-www-form-urlencoded",
}


@pytest.fixture
def mock_response_no_vendors(responses_mock: responses.RequestsMock):
    responses_mock.post(
        url="https://reportingitc-reporter.apple.com/reportservice/sales/v1",
        headers=_headers,
        body='<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\n'
        "<Vendors>\n</Vendors>\n",
        match=[
            responses.matchers.body_matcher(
                params=f"jsonRequest={json.dumps(_vendor_request)}"
            )
        ],
    )


@pytest.fixture
def mock_response_one_vendor(responses_mock: responses.RequestsMock):
    responses_mock.post(
        url="https://reportingitc-reporter.apple.com/reportservice/sales/v1",
        headers=_headers,
        body='<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\n'
        "<Vendors>\n<Vendor>Sample_name_1</Vendor>\n</Vendors>\n",
        match=[
            responses.matchers.body_matcher(
                params=f"jsonRequest={json.dumps(_vendor_request)}"
            )
        ],
    )


@pytest.fixture
def mock_response_two_vendors(responses_mock: responses.RequestsMock):
    responses_mock.post(
        url="https://reportingitc-reporter.apple.com/reportservice/sales/v1",
        headers=_headers,
        body='<?xml version="1.0" encoding="UTF-8" standalone="yes"?>\n'
        "<Vendors>\n"
        "<Vendor>Sample_name_1</Vendor>\n"
        "<Vendor>Sample_name_2</Vendor>\n"
        "</Vendors>\n",
        match=[
            responses.matchers.body_matcher(
                params=f"jsonRequest={json.dumps(_vendor_request)}"
            )
        ],
    )
