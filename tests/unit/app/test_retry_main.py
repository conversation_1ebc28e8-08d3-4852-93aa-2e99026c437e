from unittest.mock import Mock

import pytest

from app.core.exceptions import MissingPermissionsException, TryAgainLaterException
from app.util.cli import <PERSON><PERSON><PERSON>


def test_command_retries_on_exception():
    cli = CLI("TEST")
    mock_group = Mock()
    cli._group = mock_group

    # Configure mock to raise exception 3 times before succeeding
    mock_group.side_effect = [
        TryAgainLaterException,
        TryAgainLaterException,
        TryAgainLaterException,
        None,
    ]

    cli.run(["test"])

    assert mock_group.call_count == 4
    mock_group.assert_called_with(["test"], standalone_mode=False)


def test_command_stops_on_non_retryable_exception():
    cli = CLI("TEST")
    mock_group = Mock()
    cli._group = mock_group

    # Configure mock to raise non-retryable exception
    mock_group.side_effect = MissingPermissionsException

    with pytest.raises(MissingPermissionsException):
        cli.run(["test"])

    assert mock_group.call_count == 1
    mock_group.assert_called_once_with(["test"], standalone_mode=False)


def test_command_retries_up_to_5_times_and_throws():
    cli = CLI("TEST")
    mock_group = Mock()
    cli._group = mock_group

    # Configure mock to raise exception 5 times
    mock_group.side_effect = TryAgainLaterException

    with pytest.raises(TryAgainLaterException):
        cli.run(["test"])

    assert mock_group.call_count == 5
    mock_group.assert_called_with(["test"], standalone_mode=False)
