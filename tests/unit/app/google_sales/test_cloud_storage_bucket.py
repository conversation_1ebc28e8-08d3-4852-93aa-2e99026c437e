import pytest
from pydantic import ValidationError

from app.google.google_sales import GoogleSalesCredentials


@pytest.mark.parametrize(
    "valid_bucket",
    [
        "pubsite_prod_rev_123456",
        "pubsite_prod_123456",
        "pubsite__rev_123456",
    ],
)
def test_valid_cloud_storage_bucket(valid_bucket):
    model = GoogleSalesCredentials(cloud_storage_bucket=valid_bucket, config_json=None)
    assert model.cloud_storage_bucket == valid_bucket


@pytest.mark.parametrize(
    "invalid_bucket",
    [
        "pubsite_",
        "pubsite_prod_",
        "pubsite_rev_",
        "pubsite_prod_rev_",
        "pubsite_abc",
        "pubsite_prod_abc",
        "pubsite_rev_abc",
        "pubsite_prod_rev_abc",
    ],
)
def test_invalid_cloud_storage_bucket(invalid_bucket):
    with pytest.raises(ValidationError):
        GoogleSalesCredentials(cloud_storage_bucket=invalid_bucket, config_json=None)
