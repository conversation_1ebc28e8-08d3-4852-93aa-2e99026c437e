from app.util.logging.sensitive_data_filter import sensitive_parameters
from app.util.logging.telemetry_type_filter import FilterSensitiveInfo


def test_should_check_filter_sensitive_data_filters_data():
    for sensitive_param in sensitive_parameters:
        log_record = type(
            "Record",
            (),
            {"msg": sensitive_param},
        )
        assert FilterSensitiveInfo().filter(log_record) is True
        assert log_record.msg == "[REDACTED]"


def test_should_not_filter_error_type_incorrect_credentials():
    incorrect_credentials_error = (
        '{"output":True, "errorType": "INCORRECT_CREDENTIALS"}'
    )
    log_record = type("Record", (), {"msg": incorrect_credentials_error})
    assert FilterSensitiveInfo().filter(record=log_record) is True
    assert log_record.msg == '{"output":True, "errorType": "INCORRECT_CREDENTIALS"}'
