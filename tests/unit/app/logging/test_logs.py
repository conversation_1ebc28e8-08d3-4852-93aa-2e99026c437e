import logging

import pytest
import time_machine

from app.core.exceptions import CaptchaRequiredException


@pytest.fixture(autouse=True)
def _set_fixed_time():
    with time_machine.travel("2023-01-01T00:00:00.000Z", tick=False):
        yield


def test_log_info(get_all_logs, any_int):
    logger_name = "test_log_info"
    log = logging.getLogger(logger_name)

    log.info("Test log message", extra={"a": 1, "b": "2"})

    all_logs = get_all_logs()
    assert all_logs == [
        {
            "timestamp": "2023-01-01T00:00:00.000Z",
            "metadata": {
                "function_name": "test_log_info",
                "logger_name": logger_name,
                "level_name": "INFO",
                "line_no": any_int,
            },
            "message": "Test log message",
            "originId": "Scraper-py-0.0.0",
            "type": "output",
            "logLevel": 1,
            "version": 2,
            "a": 1,
            "b": "2",
        }
    ]


def test_log_info_with_type_trace(get_all_logs, any_int):
    logger_name = "test_log_info"
    log = logging.getLogger(logger_name)

    log.info("Test log message", extra={"a": 1, "b": "2", "type": "trace"})

    all_logs = get_all_logs()
    assert all_logs == [
        {
            "timestamp": "2023-01-01T00:00:00.000Z",
            "metadata": {
                "function_name": "test_log_info_with_type_trace",
                "logger_name": logger_name,
                "level_name": "INFO",
                "line_no": any_int,
            },
            "message": "Test log message",
            "originId": "Scraper-py-0.0.0",
            "type": "trace",
            "logLevel": 1,
            "version": 2,
            "a": 1,
            "b": "2",
        }
    ]


def test_log_warning(get_all_logs, any_int):
    logger_name = "test_log_warning"
    log = logging.getLogger(logger_name)

    log.warning("Test log message", extra={"a": 1, "b": "2"})

    all_logs = get_all_logs()
    assert all_logs == [
        {
            "timestamp": "2023-01-01T00:00:00.000Z",
            "metadata": {
                "function_name": "test_log_warning",
                "logger_name": logger_name,
                "level_name": "WARNING",
                "line_no": any_int,
            },
            "message": "Test log message",
            "originId": "Scraper-py-0.0.0",
            "type": "output",
            "logLevel": 2,
            "version": 2,
            "a": 1,
            "b": "2",
        }
    ]


def test_log_error_with_missing_error_param(get_all_logs, any_int):
    logger_name = "test_log_error"
    log = logging.getLogger(logger_name)

    log.error("Test log message", extra={"a": 1, "b": "2"})

    all_logs = get_all_logs()
    assert all_logs == [
        {
            "timestamp": "2023-01-01T00:00:00.000Z",
            "metadata": {
                "function_name": "test_log_error_with_missing_error_param",
                "logger_name": logger_name,
                "level_name": "ERROR",
                "line_no": any_int,
            },
            "message": "Test log message",
            "originId": "Scraper-py-0.0.0",
            "type": "output",
            "logLevel": 3,
            "version": 2,
            "a": 1,
            "b": "2",
        }
    ]


def test_log_error__from_try_except_block(get_all_logs, any_int):
    logger_name = "test_log_error"
    log = logging.getLogger(logger_name)

    try:
        raise CaptchaRequiredException("Test log message")
    except CaptchaRequiredException:
        log.exception("Test log message", extra={"a": 1, "b": "2"})

    all_logs = get_all_logs()
    assert all_logs == [
        {
            "timestamp": "2023-01-01T00:00:00.000Z",
            "metadata": {
                "function_name": "test_log_error__from_try_except_block",
                "logger_name": logger_name,
                "level_name": "ERROR",
                "line_no": any_int,
            },
            "message": "Test log message",
            "originId": "Scraper-py-0.0.0",
            "type": "output",
            "errorType": "MISSING_CAPTCHA",
            "logLevel": 3,
            "version": 2,
            "a": 1,
            "b": "2",
        }
    ]


def test_log_critical(get_all_logs, any_int):
    logger_name = "test_log_critical"
    log = logging.getLogger(logger_name)

    log.critical("Test log message", extra={"a": 1, "b": "2"})

    all_logs = get_all_logs()
    assert all_logs == [
        {
            "timestamp": "2023-01-01T00:00:00.000Z",
            "metadata": {
                "function_name": "test_log_critical",
                "logger_name": logger_name,
                "level_name": "CRITICAL",
                "line_no": any_int,
            },
            "message": "Test log message",
            "originId": "Scraper-py-0.0.0",
            "type": "output",
            "logLevel": 3,
            "version": 2,
            "a": 1,
            "b": "2",
        }
    ]


def test_log_debug(get_all_logs, any_int):
    logger_name = "test_log_debug"
    log = logging.getLogger(logger_name)

    log.debug("Test log message", extra={"a": 1, "b": "2"})

    all_logs = get_all_logs()
    assert all_logs == [
        {
            "timestamp": "2023-01-01T00:00:00.000Z",
            "metadata": {
                "function_name": "test_log_debug",
                "logger_name": logger_name,
                "level_name": "DEBUG",
                "line_no": any_int,
            },
            "message": "Test log message",
            "originId": "Scraper-py-0.0.0",
            "type": "trace",
            "logLevel": 1,
            "version": 2,
            "a": 1,
            "b": "2",
        }
    ]


def test_log_exception(get_all_logs, any_int):
    logger_name = "test_log_exception"
    log = logging.getLogger(logger_name)

    try:
        1 / 0
    except ZeroDivisionError:
        log.exception("Test log message", extra={"a": 1, "b": "2"})

    all_logs = get_all_logs()
    assert all_logs == [
        {
            "timestamp": "2023-01-01T00:00:00.000Z",
            "metadata": {
                "function_name": "test_log_exception",
                "logger_name": logger_name,
                "level_name": "ERROR",
                "line_no": any_int,
            },
            "errorType": "UNEXPECTED_ERROR",
            "originId": "Scraper-py-0.0.0",
            "logLevel": 3,
            "version": 2,
            "a": 1,
            "b": "2",
            "type": "output",
            "message": "Test log message",
        }
    ]


def test_log_exception_without_message(get_all_logs, any_int):
    logger_name = "test_log_exception"
    log = logging.getLogger(logger_name)

    try:
        1 / 0
    except ZeroDivisionError as e:
        log.exception(e, extra={"a": 1, "b": "2"})

    all_logs = get_all_logs()
    assert all_logs == [
        {
            "timestamp": "2023-01-01T00:00:00.000Z",
            "metadata": {
                "function_name": "test_log_exception_without_message",
                "logger_name": logger_name,
                "level_name": "ERROR",
                "line_no": any_int,
            },
            "errorType": "UNEXPECTED_ERROR",
            "originId": "Scraper-py-0.0.0",
            "logLevel": 3,
            "version": 2,
            "a": 1,
            "b": "2",
            "type": "output",
            "message": "division by zero",
        }
    ]


def test_log_exception_with_type_trace(get_all_logs, any_int):
    logger_name = "test_log_exception"
    log = logging.getLogger(logger_name)

    try:
        1 / 0
    except ZeroDivisionError:
        log.exception("Test log message", extra={"a": 1, "b": "2", "type": "trace"})

    all_logs = get_all_logs()
    assert all_logs == [
        {
            "timestamp": "2023-01-01T00:00:00.000Z",
            "metadata": {
                "function_name": "test_log_exception_with_type_trace",
                "logger_name": logger_name,
                "level_name": "ERROR",
                "line_no": any_int,
            },
            "errorType": "UNEXPECTED_ERROR",
            "originId": "Scraper-py-0.0.0",
            "logLevel": 3,
            "version": 2,
            "a": 1,
            "b": "2",
            "type": "trace",
            "message": "Test log message",
        }
    ]


def test_log_with_dangerous_url(get_all_logs):
    logger_name = "test_log_info"
    log = logging.getLogger(logger_name)

    dangerous_url = "GET https://example.com/?names=Alice%2CBob"  # ?names=Alice,Bob
    log.info("HTTP Request: %s", dangerous_url)

    assert (
        get_all_logs()[0]["message"]
        == "HTTP Request: GET https://example.com/?names=Alice%2CBob"
    )
