from app.util.command_with_args_to_args_dict_converter import (
    CommandWithArgsToArgsDictConverter,
)


def test_conversion_of_arg_pairs():
    args_ = [
        "command",
        "--arg1=test",
        '--arg2={"cS": "InstrumentationKey=https://sample"}',
        "--arg3=False",
    ]
    converted = CommandWithArgsToArgsDictConverter(args_).get()
    assert converted["--arg1"] == "test"
    assert converted["--arg2"] == '{"cS": "InstrumentationKey=https://sample"}'
    assert converted["--arg3"] == "False"


def test_conversion_of_arg_options():
    args_ = [
        "command",
        "--arg1",
        "test",
        "--arg2",
        '{"cS": "InstrumentationKey=https://sample"}',
        "--arg3",
        "False",
    ]
    converted = CommandWithArgsToArgsDictConverter(args_).get()
    assert converted["--arg1"] == "test"
    assert converted["--arg2"] == '{"cS": "InstrumentationKey=https://sample"}'
    assert converted["--arg3"] == "False"


def test_no_args():
    args_ = ["command"]
    converted = CommandWithArgsToArgsDictConverter(args_).get()
    assert not converted
