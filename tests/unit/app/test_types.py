from app.core.types import FeatureFlags


def test_feature_flags_short_syntax():
    feature_flags = FeatureFlags(root=["flag1", "flag2"])
    assert "flag1" in feature_flags
    assert "flag2" in feature_flags

    assert "flag_not_set" not in feature_flags


def test_feature_flags_check_by_root():
    feature_flags = FeatureFlags(root=["flag1"])
    assert "flag1" in feature_flags.root
    assert "flag_not_set" not in feature_flags.root
