from app.arguments import (
    DEFAULT_CORE_ARGUMENTS,
    CoreArguments,
    SentryArguments,
    version,
)

_CORE_ARGUMENTS_PARSED = (None, "scrape")


def test_sequence_to_arguments_conversion_with_arguments_missing():
    sample_arguments = ["scrape", "--test", "something"]
    assert (
        CoreArguments.extract_from_raw_sequence(sample_arguments)
        == _CORE_ARGUMENTS_PARSED
    )


def test_pairs_sequence_to_arguments_conversion_with_arguments_missing():
    sample_arguments = ["scrape", "--test=something"]
    assert (
        CoreArguments.extract_from_raw_sequence(sample_arguments)
        == _CORE_ARGUMENTS_PARSED
    )


def test_sequence_to_arguments_conversion_with_version():
    sample_arguments = ["--version"]
    assert (
        CoreArguments.extract_from_raw_sequence(sample_arguments)
        is DEFAULT_CORE_ARGUMENTS
    )


def test_sequence_to_arguments_conversion_without_arguments():
    sample_arguments = []
    assert (
        CoreArguments.extract_from_raw_sequence(sample_arguments)
        is DEFAULT_CORE_ARGUMENTS
    )


def test_correct_sequence_to_arguments_conversion():
    sample_arguments = [
        "login",
        "--telemetryParams",
        '{"userEmail": "<EMAIL>", \
          "developerMode": "true", \
          "authenticatedUserId": "sample-auth-id"}',
        "True",
        "--source",
        "something",
    ]
    (source, command) = CoreArguments.extract_from_raw_sequence(sample_arguments)
    assert source == "something"
    assert command == "login"


def test_sentry_param_conversion_without_params_passed():
    sample_arguments = [
        "command",
        "--additionalParmNotSentry=somethingelse",
        "--source=something",
    ]

    sentry_params = SentryArguments.extract_from_raw_sequence(sample_arguments)
    assert sentry_params is not None
    assert sentry_params is not None
    assert sentry_params.enabled == False
    assert sentry_params.dsn is None
    assert sentry_params.environment == "development"


def test_sentry_param_conversion_with_only_disabled():
    sample_arguments = [
        "command",
        "--additionalParmNotSentry=somethingelse",
        '--sentryParams={"enabled": false, \
          "dsn": "believe_me_this_is_a_real_sentry_dsn", \
          "environment": "test"}',
        "--source=something",
    ]

    sentry_params = SentryArguments.extract_from_raw_sequence(sample_arguments)
    assert sentry_params is not None
    assert sentry_params is not None
    assert sentry_params.enabled == False
    assert sentry_params.dsn == "believe_me_this_is_a_real_sentry_dsn"
    assert sentry_params.environment == "test"


def test_sentry_param_conversion_with_all_arguments_passed():
    sample_arguments = [
        "command",
        "--additionalParmNotSentry=somethingelse",
        '--sentryParams={"enabled": false, \
          "dsn": "believe_me_this_is_a_real_sentry_dsn", \
          "environment": "test"}',
        "--source=something",
    ]

    sentry_params = SentryArguments.extract_from_raw_sequence(sample_arguments)
    assert sentry_params is not None
    assert sentry_params is not None
    assert sentry_params.enabled == False
    assert sentry_params.dsn == "believe_me_this_is_a_real_sentry_dsn"
    assert sentry_params.environment == "test"


def test_sentry_param_conversion_with_invalid_arguments_returns_default_cfg():
    sample_arguments = [
        "command",
        "@@@this_is_unparsable_arg",
    ]

    sentry_params = SentryArguments.extract_from_raw_sequence(sample_arguments)
    assert sentry_params is not None
    assert sentry_params is not None
    assert sentry_params.enabled == False
    assert sentry_params.dsn is None
    assert sentry_params.environment == "development"


def test_sentry_param_conversion_without_arguments_returns_default_cfg():
    sample_arguments = ["command"]

    sentry_params = SentryArguments.extract_from_raw_sequence(sample_arguments)
    assert sentry_params is not None
    assert sentry_params is not None
    assert sentry_params.enabled == False
    assert sentry_params.dsn is None
    assert sentry_params.environment == "development"


def test_sentry_param_conversion_default_is_enabled_when_version_is_set(monkeypatch):
    monkeypatch.setattr(version, "__version__", "1.0.0")

    sample_arguments = ["command"]

    sentry_params = SentryArguments.extract_from_raw_sequence(sample_arguments)
    assert sentry_params is not None
    assert sentry_params is not None
    assert sentry_params.enabled == True
    assert sentry_params.dsn is not None
    assert sentry_params.dsn.startswith("https://")
    assert sentry_params.environment == "production"
