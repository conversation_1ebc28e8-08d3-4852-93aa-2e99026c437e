import re

from bs4 import BeautifulSoup
from httpx import get
from requests import Response, Session


def partner_button():
    # GET https://partners.gog.com/login returns:
    return """
    <script>
        var galaxyAccounts = new GalaxyAccounts('https://auth.gog.com/auth?client_id=*****************&response_type=code&layout=default&redirect_uri=https%3A%2F%2Fpartners.gog.com%2Flogin%2Fon_success', 'https://login.gog.com');
        $(document).ready(function () {
            $("#login").click(function () {
                galaxyAccounts.open();
            });
        });
    </script>
    """


def login_form():
    # GET x
    return """<form name="login" method="post" action="/login_check" data-form-type="loginForm" class="form form--login js-login-form">
    <h2 class="form__title"><svg class="gog-logo">
            <use xlink:href="#gog-logo"></use>
        </svg>
        <div class="form__title--text">Logowanie</div>
    </h2>
    <ol class="form__fieldset form__fieldset--login-fields">
        <li class="form__field field  "><label for="login_username" class="required">Adres e-mail</label>
            <input
                type="email" id="login_username" name="login[username]" required="required" placeholder="Adres e-mail"
                class="field__input" autocomplete="on" data-error="error_login_username" data-input-type="user"
                data-gamepad-selectable="true" data-gamepad-id="0"><span id="error_login_username"
                class="js-error-msg field__msg is-hidden">Adres e-mail jest niepoprawny</span></li>
        <li class="form__field field  "><label for="login_password" class="required">Hasło</label>
            <input type="password"
                id="login_password" name="login[password]" required="required" placeholder="Hasło" class="field__input"
                autocomplete="on" maxlength="4096" data-error="error_login_password" data-input-type="user"
                data-gamepad-selectable="true" data-gamepad-id="1"><span id="error_login_password"
                class="js-error-msg field__msg is-hidden">Pole wymagane</span></li>
    </ol>
    <ol class="form__fieldset form__fieldset--agreements"></ol>
    <ol class="form__fieldset form__fieldset--login-button">
        <li class="form__field field btn-slot"><button type="submit" id="login_login" name="login[login]"
                class="btn btn--main" data-gamepad-selectable="true" data-gamepad-id="2">Zaloguj się</button></li>
    </ol>
    <div class="form__separator"><span class="form__separator-text">or continue with</span></div>

    <input type="hidden" id="login_login_flow" name="login[login_flow]" value="default">
    <input type="hidden" id="login__token" name="login[_token]" value="MbeYyVtktODh-K9IexkEGeDxNRQoEl4wqATdfDxt_nc">
</form>
"""


def test_x(get_credentials):
    session = Session()
    login_button_page: Response = session.get("https://partners.gog.com/login")
    pattern = r"GalaxyAccounts\s*\(\s*['\"](?P<login_form_link>.*?)['\"]"
    match = re.search(pattern=pattern, string=login_button_page.text, flags=re.DOTALL)
    assert match is not None
    login_form_link = match.group("login_form_link")

    login_form = session.get(login_form_link)
    login_form_soup = BeautifulSoup(login_form.text, "html.parser")

    login_flow = login_form_soup.find("input", {"id": "login_login_flow"})["value"]
    login_token = login_form_soup.find("input", {"id": "login__token"})["value"]

    credentials = get_credentials("gog")
    login_payload = {
        "login[username]": credentials["user"],
        "login[password]": credentials["password"],
        "login[login_flow]": login_flow,
        "login[_token]": login_token,
    }

    login_response = session.post(
        "https://login.gog.com/login_check",
        data=login_payload,
    )
    assert False
