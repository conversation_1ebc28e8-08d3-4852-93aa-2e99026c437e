from datetime import date, datetime
from unittest.mock import ANY, MagicMock, call, patch

import httpx
import pytest

from app.core.exceptions import ScraperException, SessionExpiredException
from app.core.mfa import DisabledMFA
from app.core.sessions import <PERSON><PERSON>, HTTPSession
from app.core.types import <PERSON><PERSON><PERSON>
from app.nintendo.discounts.types import DiscountGroupId, NsUid, SubmissionId
from app.nintendo.nintendo_discounts import NintendoDiscountsScraper
from app.util.csv import to_raw_csv


@pytest.fixture
def session() -> HTTPSession:
    return HTTPSession(
        cookies=[
            Cookie(
                name="SESSION",
                value="asd-this-is-session-value",
                domain="ndid.mng.nintendo.net",
                path="/ndid",
                expires=-1,
            )
        ]
    )


@pytest.fixture
def http() -> MagicMock:
    return MagicMock()


@pytest.fixture
def scraper(session: HTTPSession, http: MagicMock) -> NintendoDiscountsScraper:
    return NintendoDiscountsScraper(
        session=session, credentials=None, mfa=DisabledMFA, http=http
    )


@patch("app.nintendo.nintendo_discounts.DiscountSearchPage")
@patch("app.nintendo.nintendo_discounts.DiscountInfoPage")
@patch("app.nintendo.nintendo_discounts.API")
def test_nintendo_scraper__check_session(
    api: MagicMock,
    discount_info_page: MagicMock,
    discount_search_page: MagicMock,
    discount_groups,
    scraper: NintendoDiscountsScraper,
    submission_id_to_discount_group_ids,
    discount_groups_per_id,
):
    submission_ids = discount_groups.keys()

    discount_search_page.return_value.get_session_identifier.return_value = "123"
    discount_search_page.return_value.get_csrf_token.return_value = "456"
    discount_search_page.return_value.get_all_submission_ids.return_value = [
        submission_ids
    ]

    discount_info_page.side_effect = [
        MagicMock(
            get_discount_group_ids=MagicMock(
                return_value=submission_id_to_discount_group_ids[id_]
            ),
        )
        for id_ in submission_ids
    ]
    api.side_effect = [
        MagicMock(
            get_group_discounts=lambda _access_token, group_id: discount_groups_per_id[
                group_id
            ],
        )
    ]

    result = scraper.check_session()
    assert result.id == "123"


@patch("app.nintendo.nintendo_discounts.DiscountSearchPage")
@patch("app.nintendo.nintendo_discounts.DiscountInfoPage")
@patch("app.nintendo.nintendo_discounts.API")
def test_nintendo_scraper__check_session_raises_session_expired_when_trying_random_discount(
    api: MagicMock,
    discount_info_page: MagicMock,
    discount_search_page: MagicMock,
    scraper: NintendoDiscountsScraper,
    submission_id_to_discount_group_ids,
    discount_groups,
):
    submission_ids = discount_groups.keys()

    discount_search_page.return_value.get_session_identifier.return_value = "123"
    discount_search_page.return_value.get_csrf_token.return_value = "456"
    discount_search_page.return_value.get_all_submission_ids.return_value = [
        submission_ids
    ]

    discount_info_page.side_effect = [
        MagicMock(
            get_discount_group_ids=MagicMock(
                return_value=submission_id_to_discount_group_ids[id_]
            ),
        )
        for id_ in submission_ids
    ]

    api.return_value.get_group_discounts.side_effect = SessionExpiredException

    with pytest.raises(SessionExpiredException):
        scraper.check_session()


def test_nintendo_scraper__get_organizations(scraper: NintendoDiscountsScraper):
    assert not scraper.get_organizations()


@patch("app.nintendo.nintendo_discounts.DiscountSearchPage")
def test_nintendo_scraper__scrape_produces_empty_json_and_csv_files_in_case_of_no_discounts(
    discount_search_page: MagicMock,
    scraper: NintendoDiscountsScraper,
    expected_empty_csv,
):
    discount_search_page.return_value.get_all_submission_ids.return_value = []

    report_zip = MagicMock()
    from_date = date(2021, 1, 1)
    to_date = date(2022, 1, 1)

    result = scraper.scrape(from_date, to_date, report_zip, excluded_skus=[])

    assert report_zip.add_json_file.call_args_list == [
        call([], "prices.json", from_date, to_date),
        call([], "content_lists.json", from_date, to_date),
        call([], "discount_groups.json", from_date, to_date),
        call([], "country_currency_mapping.json", from_date, to_date),
    ]

    assert report_zip.add_csv_file.call_args_list == [
        call(expected_empty_csv, "discounts.csv", from_date, to_date, raw_data=False)
    ]

    assert result.no_data is True


@patch("app.nintendo.nintendo_discounts.DiscountSearchPage")
def test_nintendo_scraper__scrape_produces_only_json_files_in_case_of_failed_csv_conversion(
    discount_search_page: MagicMock,
    scraper: NintendoDiscountsScraper,
):
    discount_search_page.return_value.get_all_submission_ids.return_value = []

    report_zip = MagicMock()
    from_date = date(2021, 1, 1)
    to_date = date(2022, 1, 1)

    with patch(
        "app.nintendo.nintendo_discounts.to_csv", side_effect=Exception("Some error")
    ):
        scraper.scrape(from_date, to_date, report_zip, excluded_skus=[])

    assert report_zip.add_json_file.call_args_list == [
        call([], "prices.json", from_date, to_date),
        call([], "content_lists.json", from_date, to_date),
        call([], "discount_groups.json", from_date, to_date),
        call([], "country_currency_mapping.json", from_date, to_date),
    ]

    assert report_zip.add_csv_file.call_args_list == []


@pytest.fixture
def ns_uids() -> dict[SubmissionId, list[NsUid]]:
    return {
        SubmissionId(190374): [
            NsUid("70010000020724"),
        ],
        SubmissionId(190372): [
            NsUid("70010000020724"),
        ],
        SubmissionId(500000): [
            NsUid("60005000000001"),
        ],
    }


@pytest.fixture
def discount_groups_per_id(
    discount_group_243107, discount_group_243105, discount_group_1, discount_group_2
) -> dict[DiscountGroupId, JSON]:
    return {
        DiscountGroupId("243107"): discount_group_243107,
        DiscountGroupId("243105"): discount_group_243105,
        DiscountGroupId("1"): discount_group_1,
        DiscountGroupId("2"): discount_group_2,
    }


@pytest.fixture
def submission_id_to_discount_group_ids(
    discount_groups,
) -> dict[SubmissionId, list[DiscountGroupId]]:
    return {
        submission_id: sorted({
            discount_group["discountGroupId"]
            for discount_group_response in discount_groups[submission_id]
            for discount_group in discount_group_response["discountGroups"]
        })
        for submission_id in discount_groups
    }


def setup_discount_info_page(
    discount_info_page,
    submission_ids,
    submission_id_to_discount_group_ids,
    country_currency_mapping,
    sale_names,
    content_lists,
    ns_uids,
):
    discount_info_page.side_effect = [
        MagicMock(
            get_discount_group_ids=MagicMock(
                return_value=submission_id_to_discount_group_ids[id_]
            ),
            get_access_token=MagicMock(return_value=ANY),
            get_country_currency_mapping=MagicMock(
                return_value=country_currency_mapping
            ),
            get_sale_name=MagicMock(return_value=sale_names[id_]),
            get_discount_content_list=MagicMock(return_value=content_lists[id_]),
            get_ns_uids=MagicMock(return_value=ns_uids[id_]),
        )
        for id_ in submission_ids
    ]


@patch("app.nintendo.nintendo_discounts.DiscountSearchPage")
@patch("app.nintendo.nintendo_discounts.DiscountInfoPage")
@patch("app.nintendo.nintendo_discounts.API")
def test_nintendo_scraper__scrape_saves_scraped_data_into_csv_file(
    api: MagicMock,
    discount_info_page: MagicMock,
    discount_search_page: MagicMock,
    scraper: NintendoDiscountsScraper,
    discount_groups,
    submission_id_to_discount_group_ids,
    discount_groups_per_id,
    prices,
    target_titles,
    sale_names,
    content_lists,
    country_currency_mapping,
    ns_uids,
    expected_csv,
):
    submission_ids = discount_groups.keys()
    discount_search_page.return_value.get_all_discount_records.return_value = [
        MagicMock(
            submission_id=id_,
            start_datetime_to=datetime(2021, 1, 1),
        )
        for id_ in submission_ids
    ]
    setup_discount_info_page(
        discount_info_page,
        submission_ids,
        submission_id_to_discount_group_ids,
        country_currency_mapping,
        sale_names,
        content_lists,
        ns_uids,
    )

    api.side_effect = [
        MagicMock(
            get_group_discounts=lambda _access_token, group_id: discount_groups_per_id[
                group_id
            ],
            get_prices=lambda _access_token, ns_uid, _product_content_type: prices[
                ns_uid
            ],
            get_target_titles=lambda _csrf_token,
            _submission_id,
            ns_uid,
            _content_type,
            _start_datetime_to,
            _region: target_titles[ns_uid],
        )
    ]

    report_zip = MagicMock()
    from_date = date(2021, 1, 1)
    to_date = date(2022, 1, 1)

    scraper.scrape(from_date, to_date, report_zip, excluded_skus=[])

    assert report_zip.add_csv_file.call_args == call(
        ANY, "discounts.csv", from_date, to_date, raw_data=False
    )

    result_csv = report_zip.add_csv_file.call_args[0][0]
    assert to_raw_csv(expected_csv) == to_raw_csv(
        result_csv
    )  # better diff in case of failure


@patch("app.nintendo.nintendo_discounts.DiscountSearchPage")
@patch("app.nintendo.nintendo_discounts.DiscountInfoPage")
@patch("app.nintendo.nintendo_discounts.API")
def test_nintendo_scraper__scrape_ignores_problematic_discounts(
    api: MagicMock,
    discount_info_page: MagicMock,
    discount_search_page: MagicMock,
    scraper: NintendoDiscountsScraper,
    discount_groups,
    submission_id_to_discount_group_ids,
    discount_groups_per_id,
    prices,
    target_titles,
    sale_names,
    content_lists,
    country_currency_mapping,
    ns_uids,
    expected_csv_without_problematic_submission,
):
    submission_ids = discount_groups.keys()
    discount_search_page.return_value.get_all_discount_records.return_value = [
        MagicMock(
            submission_id=id_,
            start_datetime_to=datetime(2021, 1, 1),
        )
        for id_ in submission_ids
    ]
    setup_discount_info_page(
        discount_info_page,
        submission_ids,
        submission_id_to_discount_group_ids,
        country_currency_mapping,
        sale_names,
        content_lists,
        ns_uids,
    )

    def get_group_discount_or_raise_exception(_access_token, group_id):
        if group_id == "1":
            raise httpx.HTTPStatusError(
                "Forbidden", request=MagicMock(), response=MagicMock(status_code=403)
            )
        return discount_groups_per_id[group_id]

    api.side_effect = [
        MagicMock(
            get_group_discounts=get_group_discount_or_raise_exception,
            get_prices=lambda _access_token, ns_uid, _product_content_type: prices[
                ns_uid
            ],
            get_target_titles=lambda _csrf_token,
            _submission_id,
            ns_uid,
            _content_type,
            _start_datetime_to,
            _region: target_titles[ns_uid],
        )
    ]

    report_zip = MagicMock()
    from_date = date(2021, 1, 1)
    to_date = date(2022, 1, 1)

    scraper.scrape(from_date, to_date, report_zip, excluded_skus=[])

    assert report_zip.add_csv_file.call_args == call(
        ANY, "discounts.csv", from_date, to_date, raw_data=False
    )

    result_csv = report_zip.add_csv_file.call_args[0][0]
    assert to_raw_csv(expected_csv_without_problematic_submission) == to_raw_csv(
        result_csv
    )


@patch("app.nintendo.nintendo_discounts.DiscountSearchPage")
@patch("app.nintendo.nintendo_discounts.DiscountInfoPage")
@patch("app.nintendo.nintendo_discounts.API")
def test_nintendo_scraper__scrape_fails_on_http_error_not_related_to_problematic_discount(
    api: MagicMock,
    discount_info_page: MagicMock,
    discount_search_page: MagicMock,
    scraper: NintendoDiscountsScraper,
    discount_groups,
    submission_id_to_discount_group_ids,
    sale_names,
    content_lists,
    country_currency_mapping,
    ns_uids,
):
    submission_ids = discount_groups.keys()
    discount_search_page.return_value.get_all_submission_ids.return_value = (
        submission_ids
    )
    setup_discount_info_page(
        discount_info_page,
        submission_ids,
        submission_id_to_discount_group_ids,
        country_currency_mapping,
        sale_names,
        content_lists,
        ns_uids,
    )

    api.side_effect = [
        httpx.HTTPStatusError(
            "Internal Server Error",
            request=MagicMock(),
            response=MagicMock(status_code=500),
        )
    ]

    with pytest.raises(httpx.HTTPStatusError):
        scraper.scrape(
            date(2021, 1, 1), date(2022, 1, 1), MagicMock(), excluded_skus=[]
        )


@patch("app.nintendo.nintendo_discounts.DiscountSearchPage")
@patch("app.nintendo.nintendo_discounts.DiscountInfoPage")
@patch("app.nintendo.nintendo_discounts.API")
def test_nintendo_scraper__scrape_fails_on_unexpected_exception(
    api: MagicMock,
    discount_info_page: MagicMock,
    discount_search_page: MagicMock,
    scraper: NintendoDiscountsScraper,
    discount_groups,
    submission_id_to_discount_group_ids,
    sale_names,
    content_lists,
    country_currency_mapping,
    ns_uids,
):
    submission_ids = discount_groups.keys()
    discount_search_page.return_value.get_all_submission_ids.return_value = (
        submission_ids
    )
    setup_discount_info_page(
        discount_info_page,
        submission_ids,
        submission_id_to_discount_group_ids,
        country_currency_mapping,
        sale_names,
        content_lists,
        ns_uids,
    )

    api.side_effect = [ScraperException("Some error")]

    with pytest.raises(ScraperException):
        scraper.scrape(
            date(2021, 1, 1), date(2022, 1, 1), MagicMock(), excluded_skus=[]
        )
