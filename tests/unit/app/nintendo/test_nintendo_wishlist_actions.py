import base64
import json
import zipfile
from datetime import date
from pathlib import Path

import pytest
import requests
import responses
from responses import matchers

from app.core.exceptions import MissingPermissionsException, SessionExpiredException
from app.core.mfa import DisabledMFA
from app.core.scraper import ReportZip, Source
from app.core.sessions import HTTPSession
from app.nintendo.nintendo_sst import (
    NintendoCredentials,
    NintendoWishlistsScraper,
)


@pytest.fixture
def credentials():
    return NintendoCredentials(user="user", password="pass", totp_secret="totpsecret")


@pytest.fixture
def empty_session():
    return HTTPSession()


@pytest.fixture
def http_session():
    return requests.Session()


@pytest.fixture
def scraper(credentials, empty_session, http_session):
    return NintendoWishlistsScraper(
        session=empty_session,
        credentials=credentials,
        mfa=DisabledMFA(),
        http=http_session,
    )


@pytest.fixture
def example_csv_report():
    return b""""NsUid","Code","Name","Region","Platform","Publisher","Sales Total","Sales Conversion Rate","Total","Period Total","05/01/25"
"70010000020724","HACPAURNA","SUPERHOT","Australia/Europe","Switch","SUPERHOT","7148","9.140%","78207","5","5"
"70010000020726","HACPAURNA","SUPERHOT","Americas","Switch","SUPERHOT","13885","9.354%","148448","11","11"
"","","","","","All Total","21033","9.28%","226655","16.0","16.0"
"""


@pytest.fixture
def refresh_authorization(responses_mock: responses.RequestsMock):
    responses_mock.add(
        method=responses.GET,
        url="https://sst.mng.nintendo.net/shoptools/oauth2/authorization/ndid",
        status=200,
    )


@pytest.fixture
def mock_report_get_via_api(
    responses_mock: responses.RequestsMock, refresh_authorization
):
    def _mock_report_get_via_api(report_content: bytes):
        responses_mock.add(
            responses.GET,
            "https://sst.mng.nintendo.net/shoptools/api/switchLicenseeProductCode",
            json=["CODE1", "CODE2", "CODE3"],
            status=200,
        )

        responses_mock.add(
            responses.GET,
            "https://sst.mng.nintendo.net/shoptools/licenseereports/wishlistReport/search",
            json={"data": base64.b64encode(report_content).decode()},
            match=[
                matchers.query_param_matcher(
                    {
                        "period": "DAILY",
                        "beginYear": "2024",
                        "beginMonth": "5",
                        "endYear": "2024",
                        "endMonth": "5",
                        "begin": "2024/05/01",
                        "end": "2024/05/10",
                        "registeredOnly": "false",
                        "searchUnit": "5",
                        "searchCodes": "CODE1\r\nCODE2\r\nCODE3",
                        "downloadCsv": "downloadCsv",
                        "regions": [
                            "JPN",
                            "USA",
                            "EUR",
                            "AUS",
                            "KOR",
                            "CHN",
                            "TWN",
                            "Other",
                        ],
                        "devices": ["HAC", "BEE"],
                        "types": ["TITLE", "AOC", "BUNDLE"],
                    },
                ),
            ],
            status=200,
        )

    return _mock_report_get_via_api


def test_scrape_saves_valid_report(
    tmp_path: Path, scraper, mock_report_get_via_api, example_csv_report
):
    mock_report_get_via_api(example_csv_report)

    from_ = date(2024, 5, 1)
    to = date(2024, 5, 10)
    report_zip = ReportZip(
        report_path=tmp_path,
        source=Source.NINTENDO_WISHLISTS,
        date_from=from_,
        date_to=to,
    )
    with report_zip:
        info = scraper.scrape(
            from_=from_,
            to=to,
            report_zip=report_zip,
            excluded_skus=[],
        )

    zip_path = tmp_path / "nintendo_wishlists-2024-05-01_2024-05-10.zip"
    assert zip_path.exists()

    with zipfile.ZipFile(zip_path) as zf:
        files = zf.namelist()
        assert files == [
            "nintendo_wishlists-2024-05-01-2024-05-10-0.csv",
            "manifest.json",
        ]

        with zf.open("nintendo_wishlists-2024-05-01-2024-05-10-0.csv") as f:
            csv_content = f.read()
            assert csv_content == example_csv_report

        with zf.open("manifest.json") as f:
            manifest = json.load(f)
            assert manifest == {
                "dateFrom": "2024-05-01",
                "dateTo": "2024-05-10",
                "manifestVersion": 1,
                "fileMetaData": {
                    "nintendo_wishlists-2024-05-01-2024-05-10-0.csv": {
                        "dateFrom": "2024-05-01",
                        "dateTo": "2024-05-10",
                        "rawData": True,
                    }
                },
                "scraperVersion": "0.0.0",
            }

    assert info.no_data is False


def test_scraper_with_invalid_report_response_raises_session_expired(
    tmp_path: Path, scraper, mock_report_get_via_api, example_csv_report
):
    mock_report_get_via_api(b'{"error": "INVALID CONTENT"}')

    from_ = date(2024, 5, 1)
    to = date(2024, 5, 10)
    report_zip = ReportZip(
        report_path=tmp_path,
        source=Source.NINTENDO_WISHLISTS,
        date_from=from_,
        date_to=to,
    )
    with report_zip, pytest.raises(SessionExpiredException):
        scraper.scrape(from_=from_, to=to, report_zip=report_zip, excluded_skus=[])


def test_scrape_no_products_raises_missing_permission(
    tmp_path: Path,
    scraper: NintendoWishlistsScraper,
    refresh_authorization,
    responses_mock: responses.RequestsMock,
):
    responses_mock.add(
        responses.GET,
        "https://sst.mng.nintendo.net/shoptools/api/switchLicenseeProductCode",
        json=[],
        status=200,
    )
    from_ = date(2024, 5, 1)
    to = date(2024, 5, 10)
    report_zip = ReportZip(
        report_path=tmp_path,
        source=Source.NINTENDO_WISHLISTS,
        date_from=from_,
        date_to=to,
    )
    with report_zip, pytest.raises(MissingPermissionsException):
        scraper.scrape(from_=from_, to=to, report_zip=report_zip, excluded_skus=[])
