import pytest
import httpx
from unittest.mock import MagicMock, patch
from bs4 import Beautiful<PERSON><PERSON>p

from app.core.exceptions import (
    InvalidCredentialsException,
    MFAInvalid,
    TemporaryApiIssueException,
)
from app.core.mfa import DisabledMFA
from app.nintendo.login import (
    NintendoCredentials,
    login,
    get_ticket_value,
    get_csrf_token_value,
)


class TestNintendoCredentials:
    """Test the NintendoCredentials model."""
    
    def test_credentials_creation(self):
        """Test creating NintendoCredentials."""
        creds = NintendoCredentials(
            user="test_user",
            password="test_password",
            totp_secret="test_secret"
        )
        assert creds.user == "test_user"
        assert creds.password == "test_password"
        assert creds.totp_secret == "test_secret"
    
    def test_credentials_defaults(self):
        """Test NintendoCredentials with default values."""
        creds = NintendoCredentials()
        assert creds.user is None
        assert creds.password is None
        assert creds.totp_secret is None


class TestLogin:
    """Test the login function with httpx."""
    
    @pytest.fixture
    def mock_credentials(self):
        return NintendoCredentials(
            user="test_user",
            password="test_password",
            totp_secret="test_secret"
        )
    
    @pytest.fixture
    def mock_mfa(self):
        mfa = MagicMock()
        mfa.request.return_value = "123456"
        return mfa
    
    @pytest.fixture
    def mock_http_client(self):
        client = MagicMock(spec=httpx.Client)
        client.cookies = MagicMock()
        return client
    
    def test_login_no_credentials_raises_exception(self, mock_http_client, mock_mfa):
        """Test login with no credentials raises InvalidCredentialsException."""
        creds = NintendoCredentials()
        
        with pytest.raises(InvalidCredentialsException, match="No credentials provided"):
            login(creds, mock_http_client, mock_mfa)
    
    def test_login_missing_user_raises_exception(self, mock_http_client, mock_mfa):
        """Test login with missing user raises InvalidCredentialsException."""
        creds = NintendoCredentials(password="test_password")
        
        with pytest.raises(InvalidCredentialsException, match="No credentials provided"):
            login(creds, mock_http_client, mock_mfa)
    
    def test_login_missing_password_raises_exception(self, mock_http_client, mock_mfa):
        """Test login with missing password raises InvalidCredentialsException."""
        creds = NintendoCredentials(user="test_user")
        
        with pytest.raises(InvalidCredentialsException, match="No credentials provided"):
            login(creds, mock_http_client, mock_mfa)
    
    def test_login_clears_cookies(self, mock_credentials, mock_http_client, mock_mfa):
        """Test that login clears existing cookies."""
        # Mock the initial dashboard response
        dashboard_response = MagicMock()
        dashboard_response.url = "https://ndid.mng.nintendo.net/ndid/oauth/Authorized?test=1"
        dashboard_response.text = '''
        <html>
            <form action="/ndid/oauth/Authorized/issue">
                <input name="loginid" />
                <input name="ticket" value="test_ticket" />
                <input name="_csrf" value="test_csrf" />
            </form>
        </html>
        '''
        
        # Mock the login POST response
        login_response = MagicMock()
        login_response.status_code = 200
        login_response.text = "success"
        
        # Mock the MFA POST response
        mfa_response = MagicMock()
        mfa_response.status_code = 200
        mfa_response.text = "<html>Success</html>"
        
        mock_http_client.get.return_value = dashboard_response
        mock_http_client.post.side_effect = [login_response, mfa_response]
        
        login(mock_credentials, mock_http_client, mock_mfa)
        
        # Verify cookies were cleared
        mock_http_client.cookies.clear.assert_called_once()


class TestHelperFunctions:
    """Test helper functions for parsing HTML."""
    
    def test_get_ticket_value_success(self):
        """Test getting ticket value from HTML."""
        html = '<input name="ticket" value="test_ticket_value" />'
        soup = BeautifulSoup(html, "html.parser")
        
        result = get_ticket_value(soup)
        assert result == "test_ticket_value"
    
    def test_get_ticket_value_missing_raises_exception(self):
        """Test getting ticket value when input is missing."""
        html = '<input name="other" value="test_value" />'
        soup = BeautifulSoup(html, "html.parser")
        
        with pytest.raises(TemporaryApiIssueException, match="Could not find ticket input"):
            get_ticket_value(soup)
    
    def test_get_csrf_token_value_success(self):
        """Test getting CSRF token value from HTML."""
        html = '<input name="_csrf" value="test_csrf_value" />'
        soup = BeautifulSoup(html, "html.parser")
        
        result = get_csrf_token_value(soup)
        assert result == "test_csrf_value"
    
    def test_get_csrf_token_value_missing_raises_exception(self):
        """Test getting CSRF token value when input is missing."""
        html = '<input name="other" value="test_value" />'
        soup = BeautifulSoup(html, "html.parser")
        
        with pytest.raises(TemporaryApiIssueException, match="Could not find csrf token input"):
            get_csrf_token_value(soup)
