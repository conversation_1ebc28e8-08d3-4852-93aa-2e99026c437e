import re
from datetime import datetime

import pytest
from dateutil import tz

from app.nintendo.discounts.types import DiscountSearchResult


@pytest.mark.parametrize(
    ("date_str", "datetime_format", "expected"),
    [
        (
            "26/10/2023 15:00:00 (UTC)",
            "DD/MM/YYYY HH:mm:ss",
            datetime(2023, 10, 26, 15, 0, 0, tzinfo=tz.UTC),
        ),
        (
            "11/24/2023 23:59:59 (UTC)",
            "MM/DD/YYYY HH:mm:ss",
            datetime(2023, 11, 24, 23, 59, 59, tzinfo=tz.UTC),
        ),
        (
            "2025/03/24 17:00:00 (UTC)",
            "YYYY/MM/DD HH:mm:ss",
            datetime(2025, 3, 24, 17, 0, 0, tzinfo=tz.UTC),
        ),
        (
            "2025/24/03 17:00:00 (UTC)",
            "YYYY/DD/MM HH:mm:ss",
            datetime(2025, 3, 24, 17, 0, 0, tzinfo=tz.UTC),
        ),
        (
            "2025-03-24 17:00:00 (UTC)",
            "YYYY-MM-DD HH:mm:ss",
            datetime(2025, 3, 24, 17, 0, 0, tzinfo=tz.UTC),
        ),
        (
            "----/--/-- --:--:-- (----)",  # Special case - empty date
            "DD/MM/YYYY HH:mm:ss",
            None,
        ),
    ],
)
def test_parse_nintendo_datetime_valid_formats(date_str, datetime_format, expected):
    result = DiscountSearchResult.parse_datetime(date_str, datetime_format)
    assert result == expected


@pytest.mark.parametrize(
    ("date_str", "datetime_format", "expected_error"),
    [
        (
            "",
            "DD/MM/YYYY HH:mm:ss",
            "Empty date string",
        ),
        (
            "26/10/2023 15:00:00",  # Missing (UTC)
            "DD/MM/YYYY HH:mm:ss",
            "Invalid datetime format. Expected 'DATE TIME (UTC)', got: '26/10/2023 15:00:00'. Format: DD/MM/YYYY HH:mm:ss",
        ),
        (
            "26/10/2023 15:00:00 (CET)",  # Wrong timezone
            "DD/MM/YYYY HH:mm:ss",
            "Invalid datetime format. Expected 'DATE TIME (UTC)', got: '26/10/2023 15:00:00 (CET)'. Format: DD/MM/YYYY HH:mm:ss",
        ),
        (
            "26/10/2023 15:00:00 (UTC)",
            "INVALID_FORMAT",  # Invalid format string
            "Unsupported datetime format: 'INVALID_FORMAT'. Format: INVALID_FORMAT",
        ),
        (
            "26/13/2023 15:00:00 (UTC)",  # Invalid month
            "DD/MM/YYYY HH:mm:ss",
            "Failed to parse '26/13/2023 15:00:00' using format DD/MM/YYYY HH:mm:ss. Make sure the date matches the expected format.",
        ),
        (
            "2025-03-24 17:00:00 (UTC)",
            "YYYY-DD-MM HH:mm:ss",  # Invalid format
            "Unsupported datetime format: 'YYYY-DD-MM HH:mm:ss'. Format: YYYY-DD-MM HH:mm:ss",
        ),
    ],
)
def test_parse_nintendo_datetime_invalid_formats(
    date_str, datetime_format, expected_error
):
    with pytest.raises(ValueError, match=re.escape(expected_error)):
        DiscountSearchResult.parse_datetime(date_str, datetime_format)
