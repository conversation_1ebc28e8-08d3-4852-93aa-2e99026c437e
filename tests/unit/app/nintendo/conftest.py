import json
from pathlib import Path
from unittest.mock import MagicMock

import pytest
import requests

from app.core.types import JSON, DictJSON, ListJSON
from app.nintendo.discounts.types import NsUid, SubmissionId


@pytest.fixture
def load_file():
    def _load_file(file_path: Path):
        file_path = Path(__file__).parent / file_path
        with file_path.open(encoding="utf-8") as f:
            return f.read()

    return _load_file


@pytest.fixture
def files_based_mock_response(load_file):
    def _file_based_mock_response(file_names: list[str]):
        response = MagicMock(spec=requests.Response)
        response.status_code = 200
        response.text = "\n".join(
            load_file(f"pages/mocks/{file_name}") for file_name in file_names
        )
        response.json = lambda: json.loads(response.text)

        return response

    return _file_based_mock_response


@pytest.fixture
def discount_group_243107(load_file):
    return json.loads(load_file("pages/mocks/discountGroup-243107.json"))


@pytest.fixture
def discount_group_243105(load_file):
    return json.loads(load_file("pages/mocks/discountGroup-243105.json"))


@pytest.fixture
def discount_group_1(load_file):
    return json.loads(load_file("pages/mocks/discountGroup-1.json"))


@pytest.fixture
def discount_group_2(load_file):
    return json.loads(load_file("pages/mocks/discountGroup-2.json"))


@pytest.fixture
def discount_groups(
    discount_group_243107,
    discount_group_243105,
    discount_group_1,
    discount_group_2,
) -> dict[SubmissionId, list[JSON]]:
    return {
        SubmissionId(190374): [discount_group_243107],
        SubmissionId(190372): [discount_group_243105],
        SubmissionId(500000): [discount_group_1, discount_group_2],
    }


@pytest.fixture
def prices(load_file) -> dict[NsUid, JSON]:
    return {
        NsUid("70010000020724"): json.loads(
            load_file("pages/mocks/prices-70010000020724.json")
        ),
        NsUid("60005000000001"): json.loads(
            load_file("pages/mocks/prices-60005000000001.json")
        ),
    }


@pytest.fixture
def target_titles(load_file) -> dict[NsUid, JSON]:
    return {
        NsUid("70010000020724"): json.loads(
            load_file("pages/mocks/target-titles-70010000020724.json")
        ),
        NsUid("60005000000001"): json.loads(
            load_file("pages/mocks/target-titles-60005000000001.json")
        ),
    }


@pytest.fixture
def sale_names() -> dict[SubmissionId, str]:
    return {
        SubmissionId(190374): "SUPERHOT Sale",
        SubmissionId(190372): "SUPERHOT Sale",
        SubmissionId(500000): "Winter SUPERCOLD Sale",
    }


@pytest.fixture
def content_lists() -> dict[SubmissionId, DictJSON]:
    return {
        SubmissionId(190374): {
            "valid": True,
            "content": [
                {
                    "discountCountries": [],
                    "primaryKey": 33809,
                    "nsUid": "70010000020724",
                    "titleId": None,
                    "contentType": "TITLE",
                    "contentTypeName": "Title",
                    "regionName": "Europe/Australia",
                    "contentName": "SUPERHOT",
                    "productCode": "HAC-P-AURNA",
                    "lastEditedDate": "02/07/2021 15:18:30 (CEST)",
                    "lastEditedDateForSort": "2021-07-02 13:18:30",
                    "status": "Approved",
                    "platFormName": "Nintendo Switch downloadable software",
                    "publisherName": "SUPERHOT(SUPERHOT)",
                    "onlinePublisherId": 11094,
                    "deviceTypeId": 6,
                    "statExclusionFlag": None,
                    "onlinePriceChangesetId": None,
                    "contentTypeId": {"present": True},
                }
            ],
        },
        SubmissionId(190372): {
            "valid": True,
            "content": [
                {
                    "discountCountries": [],
                    "primaryKey": 33809,
                    "nsUid": "70010000020724",
                    "titleId": None,
                    "contentType": "TITLE",
                    "contentTypeName": "Title",
                    "regionName": "Europe/Australia",
                    "contentName": "SUPERHOT",
                    "productCode": "HAC-P-AURNA",
                    "lastEditedDate": "02/07/2021 15:18:30 (CEST)",
                    "lastEditedDateForSort": "2021-07-02 13:18:30",
                    "status": "Approved",
                    "platFormName": "Nintendo Switch downloadable software",
                    "publisherName": "SUPERHOT(SUPERHOT)",
                    "onlinePublisherId": 11094,
                    "deviceTypeId": 6,
                    "statExclusionFlag": None,
                    "onlinePriceChangesetId": None,
                    "contentTypeId": {"present": True},
                }
            ],
        },
        SubmissionId(500000): {
            "valid": True,
            "content": [
                {
                    "discountCountries": [],
                    "primaryKey": 33809,
                    "nsUid": "60005000000001",
                    "titleId": None,
                    "contentType": "TITLE",
                    "contentTypeName": "Title",
                    "regionName": "Europe/Australia",
                    "contentName": "SUPERCOLD",
                    "productCode": "SUPERCOLD",
                    "lastEditedDate": "02/07/2021 15:18:30 (CEST)",
                    "lastEditedDateForSort": "2021-07-02 13:18:30",
                    "status": "Approved",
                    "platFormName": "Nintendo Switch downloadable software",
                    "publisherName": "SUPERHOT(SUPERHOT)",
                    "onlinePublisherId": 11094,
                    "deviceTypeId": 6,
                    "statExclusionFlag": None,
                    "onlinePriceChangesetId": None,
                    "contentTypeId": {"present": True},
                }
            ],
        },
    }


@pytest.fixture
def country_currency_mapping() -> ListJSON:
    return [
        {"countryCode": "AT", "countryName": "Austria", "currencyCode": "EUR"},
        {"countryCode": "AU", "countryName": "Australia", "currencyCode": "AUD"},
    ]


@pytest.fixture
def expected_csv():
    return {
        "headers": [
            "submission_id",
            "sale_name",
            "discount_group_id",
            "product_code",
            "ns_uid",
            "discount_id",
            "country_code",
            "start_datetime",
            "end_datetime",
            "currency",
            "discount_value",
            "regular_price",
            "price_start_datetime",
            "price_end_datetime",
            "platform_name",
        ],
        "rows": [
            {
                "submission_id": "190374",
                "sale_name": "SUPERHOT Sale",
                "discount_group_id": "243107",
                "product_code": "HAC-P-AURNA",
                "ns_uid": "70010000020724",
                "discount_id": "5951830",
                "country_code": "AU",
                "start_datetime": "2023-12-23T14:00:00+0000",
                "end_datetime": "2024-01-21T12:59:59+0000",
                "currency": "AUD",
                "discount_value": "21",
                "regular_price": "34.99",
                "price_start_datetime": "2020-01-24T04:00:00+0000",
                "price_end_datetime": "",
                "platform_name": "Nintendo Switch downloadable software",
            },
            {
                "submission_id": "190374",
                "sale_name": "SUPERHOT Sale",
                "discount_group_id": "243107",
                "product_code": "HAC-P-AURNA",
                "ns_uid": "70010000020724",
                "discount_id": "5951798",
                "country_code": "AT",
                "start_datetime": "2023-12-23T14:00:00+0000",
                "end_datetime": "2024-01-21T22:59:59+0000",
                "currency": "EUR",
                "discount_value": "13.8",
                "regular_price": "22.99",
                "price_start_datetime": "2019-08-19T14:00:00+0000",
                "price_end_datetime": "",
                "platform_name": "Nintendo Switch downloadable software",
            },
            {
                "submission_id": "190372",
                "sale_name": "SUPERHOT Sale",
                "discount_group_id": "243105",
                "product_code": "HAC-P-AURNA",
                "ns_uid": "70010000020724",
                "discount_id": "5951788",
                "country_code": "AU",
                "start_datetime": "2023-10-26T13:00:00+0000",
                "end_datetime": "2023-11-24T12:59:59+0000",
                "currency": "AUD",
                "discount_value": "21",
                "regular_price": "34.99",
                "price_start_datetime": "2020-01-24T04:00:00+0000",
                "price_end_datetime": "",
                "platform_name": "Nintendo Switch downloadable software",
            },
            {
                "submission_id": "190372",
                "sale_name": "SUPERHOT Sale",
                "discount_group_id": "243105",
                "product_code": "HAC-P-AURNA",
                "ns_uid": "70010000020724",
                "discount_id": "5951756",
                "country_code": "AT",
                "start_datetime": "2023-10-26T13:00:00+0000",
                "end_datetime": "2023-11-24T22:59:59+0000",
                "currency": "EUR",
                "discount_value": "13.8",
                "regular_price": "22.99",
                "price_start_datetime": "2019-08-19T14:00:00+0000",
                "price_end_datetime": "",
                "platform_name": "Nintendo Switch downloadable software",
            },
            {
                "country_code": "AU",
                "currency": "AUD",
                "discount_group_id": "1",
                "discount_id": "101",
                "discount_value": "21",
                "end_datetime": "2023-11-24T12:59:59+0000",
                "ns_uid": "60005000000001",
                "platform_name": "Nintendo Switch downloadable software",
                "price_end_datetime": "",
                "price_start_datetime": "2020-01-24T04:00:00+0000",
                "product_code": "SUPERCOLD",
                "regular_price": "34.99",
                "sale_name": "Winter SUPERCOLD Sale",
                "start_datetime": "2023-10-26T13:00:00+0000",
                "submission_id": "500000",
            },
            {
                "country_code": "AT",
                "currency": "EUR",
                "discount_group_id": "1",
                "discount_id": "102",
                "discount_value": "13.8",
                "end_datetime": "2023-11-24T22:59:59+0000",
                "ns_uid": "60005000000001",
                "platform_name": "Nintendo Switch downloadable software",
                "price_end_datetime": "",
                "price_start_datetime": "2019-08-19T14:00:00+0000",
                "product_code": "SUPERCOLD",
                "regular_price": "22.99",
                "sale_name": "Winter SUPERCOLD Sale",
                "start_datetime": "2023-10-26T13:00:00+0000",
                "submission_id": "500000",
            },
            {
                "country_code": "AU",
                "currency": "AUD",
                "discount_group_id": "2",
                "discount_id": "201",
                "discount_value": "21",
                "end_datetime": "2024-01-21T12:59:59+0000",
                "ns_uid": "60005000000001",
                "platform_name": "Nintendo Switch downloadable software",
                "price_end_datetime": "",
                "price_start_datetime": "2020-01-24T04:00:00+0000",
                "product_code": "SUPERCOLD",
                "regular_price": "34.99",
                "sale_name": "Winter SUPERCOLD Sale",
                "start_datetime": "2023-12-23T14:00:00+0000",
                "submission_id": "500000",
            },
            {
                "country_code": "AT",
                "currency": "EUR",
                "discount_group_id": "2",
                "discount_id": "202",
                "discount_value": "13.8",
                "end_datetime": "2024-01-21T22:59:59+0000",
                "ns_uid": "60005000000001",
                "platform_name": "Nintendo Switch downloadable software",
                "price_end_datetime": "",
                "price_start_datetime": "2019-08-19T14:00:00+0000",
                "product_code": "SUPERCOLD",
                "regular_price": "22.99",
                "sale_name": "Winter SUPERCOLD Sale",
                "start_datetime": "2023-12-23T14:00:00+0000",
                "submission_id": "500000",
            },
        ],
    }


@pytest.fixture
def expected_csv_without_problematic_submission():
    return {
        "headers": [
            "submission_id",
            "sale_name",
            "discount_group_id",
            "product_code",
            "ns_uid",
            "discount_id",
            "country_code",
            "start_datetime",
            "end_datetime",
            "currency",
            "discount_value",
            "regular_price",
            "price_start_datetime",
            "price_end_datetime",
            "platform_name",
        ],
        "rows": [
            {
                "submission_id": "190374",
                "sale_name": "SUPERHOT Sale",
                "discount_group_id": "243107",
                "product_code": "HAC-P-AURNA",
                "ns_uid": "70010000020724",
                "discount_id": "5951830",
                "country_code": "AU",
                "start_datetime": "2023-12-23T14:00:00+0000",
                "end_datetime": "2024-01-21T12:59:59+0000",
                "currency": "AUD",
                "discount_value": "21",
                "regular_price": "34.99",
                "price_start_datetime": "2020-01-24T04:00:00+0000",
                "price_end_datetime": "",
                "platform_name": "Nintendo Switch downloadable software",
            },
            {
                "submission_id": "190374",
                "sale_name": "SUPERHOT Sale",
                "discount_group_id": "243107",
                "product_code": "HAC-P-AURNA",
                "ns_uid": "70010000020724",
                "discount_id": "5951798",
                "country_code": "AT",
                "start_datetime": "2023-12-23T14:00:00+0000",
                "end_datetime": "2024-01-21T22:59:59+0000",
                "currency": "EUR",
                "discount_value": "13.8",
                "regular_price": "22.99",
                "price_start_datetime": "2019-08-19T14:00:00+0000",
                "price_end_datetime": "",
                "platform_name": "Nintendo Switch downloadable software",
            },
            {
                "submission_id": "190372",
                "sale_name": "SUPERHOT Sale",
                "discount_group_id": "243105",
                "product_code": "HAC-P-AURNA",
                "ns_uid": "70010000020724",
                "discount_id": "5951788",
                "country_code": "AU",
                "start_datetime": "2023-10-26T13:00:00+0000",
                "end_datetime": "2023-11-24T12:59:59+0000",
                "currency": "AUD",
                "discount_value": "21",
                "regular_price": "34.99",
                "price_start_datetime": "2020-01-24T04:00:00+0000",
                "price_end_datetime": "",
                "platform_name": "Nintendo Switch downloadable software",
            },
            {
                "submission_id": "190372",
                "sale_name": "SUPERHOT Sale",
                "discount_group_id": "243105",
                "product_code": "HAC-P-AURNA",
                "ns_uid": "70010000020724",
                "discount_id": "5951756",
                "country_code": "AT",
                "start_datetime": "2023-10-26T13:00:00+0000",
                "end_datetime": "2023-11-24T22:59:59+0000",
                "currency": "EUR",
                "discount_value": "13.8",
                "regular_price": "22.99",
                "price_start_datetime": "2019-08-19T14:00:00+0000",
                "price_end_datetime": "",
                "platform_name": "Nintendo Switch downloadable software",
            },
        ],
    }


@pytest.fixture
def expected_empty_csv(expected_csv):
    return {
        "headers": expected_csv["headers"],
        "rows": [],
    }
