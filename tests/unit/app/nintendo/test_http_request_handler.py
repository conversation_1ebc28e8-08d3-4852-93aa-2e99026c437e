import json
from unittest.mock import MagicMock

import pytest
import requests

from app.core.exceptions import MissingPermissionsException, SessionExpiredException
from app.nintendo.discounts.pages.http_request_handler import HttpRequestHandler


@pytest.fixture
def mock_response():
    def _mock_response(status_code):
        response = MagicMock(spec=requests.Response)
        response.status_code = status_code
        response.text = str({"a": 1})
        response.url = "fake.url.com"
        response.reason = "fake reason"
        response.json = lambda: json.loads(response.text)
        return response

    return _mock_response


@pytest.fixture
def mock_http():
    def _http(response):
        http = MagicMock(spec=requests.Session)
        http.get.return_value = response
        return http

    return _http


@pytest.mark.parametrize("status_code", [401, 403])
def test_should_raise_session_expired_exception_for_status_code(
    status_code, mock_response, mock_http
):
    http_request_handler = HttpRequestHandler(
        http=mock_http(mock_response(status_code)), url="fake.url.com"
    )

    with pytest.raises(SessionExpiredException):
        http_request_handler.do_request(url="fake.url.com")


@pytest.mark.parametrize("status_code", [401, 403])
def test_should_raise_missing_permissions_exception(
    status_code, mock_response, mock_http
):
    http_request_handler = HttpRequestHandler(
        http=mock_http(mock_response(status_code)), url="fake.url.com"
    )

    with pytest.raises(MissingPermissionsException):
        http_request_handler.do_request(
            url="fake.url.com", treat_authorization_errors_as_session_expired=False
        )
