from app.nintendo.discounts.converters import to_csv


def test_to_csv_generetes_empty_csv_file_with_only_headers_when_getting_no_data(
    expected_empty_csv,
):
    result = to_csv({}, {}, {}, {}, {}, [])
    assert result == expected_empty_csv


def test_to_csv_create_csv_dict_when_received_proper_data(
    discount_groups,
    prices,
    target_titles,
    sale_names,
    content_lists,
    country_currency_mapping,
    expected_csv,
):
    result = to_csv(
        discount_groups,
        prices,
        target_titles,
        sale_names,
        content_lists,
        country_currency_mapping,
    )
    assert result == expected_csv
