<div>
    <script>
        var isNintendoUser = false;

        // 割引種別
        var discountTypeIdTerm = 0;
        var discountTypeIdOwner = 1;
        var discountTypeIdMembership = 2;

        // リンク用パス
        var termDiscountPath = "\/temporaryDiscount\/discountInfo";
        var ownerDiscountPath = "\/ownerDiscount\/discountInfo";
        var membershipDiscountPath = "\/membershipDiscount\/discountInfo";

        $(document).ready(function () {
            var termSortKeySelector = "#termDiscount_" + "SAVED_DATETIMEfalse";
            var ownerSortKeySelector = "#ownerDiscount_" + "SAVED_DATETIMEfalse";
            var membershipSortKeySelector = "#membershipDiscount_" + "SAVED_DATETIMEfalse";
            $(termSortKeySelector).prop("disabled", true);
            $(termSortKeySelector).removeClass('btn-default').addClass('btn-primary');
            $(ownerSortKeySelector).prop("disabled", true);
            $(ownerSortKeySelector).removeClass('btn-default').addClass('btn-primary');
            $(membershipSortKeySelector).prop("disabled", true);
            $(membershipSortKeySelector).removeClass('btn-default').addClass('btn-primary');
        });
    </script>

    <!-- 操作対象判定用 -->
    <input type="hidden" form="mainForm" id="discountTypeId" name="discountTypeId" value="0" />

    <div id="termDiscount">
        <h2 style="margin-top: 20px;">
            <span>Temporary Discount List</span>
        </h2>

        <div id="termDiscountSearchNotFountMessage" style="width: 100%; margin-top: 10px; display:none;"
            class="container">
            <div class="row">
                <span class="alert alert-info col-sm-12 text-center" role="alert">No discounts were found matching the
                    search condition.</span>
            </div>
        </div>


        <div style="margin-left: 1em;">
            <div style="height: 72px;" id="pagerAreaTERM"></div>
            <input type="hidden" form="mainForm" id="pageTermDiscount" name="pageTermDiscount" value="1" />
            <input type="hidden" form="mainForm" id="termDiscountDispCount" name="termDiscountDispCount" value="10" />
            <input type="hidden" form="mainForm" id="maxCountTERM" name="maxCountTERM" value="70" />

            <div style="position: relative;" class="SelectInfoS pull-right">
                <select id="selectTermDiscountDispCount" class="indicator" data-discountTypeTERM="TERM"
                    onchange="changeDispCount(this.getAttribute(&#39;data-discountTypeTERM&#39;));">
                    <option value="10" selected="selected">10</option>
                    <option value="25">25</option>
                    <option value="50">50</option>
                </select> <span class="SelectValue">50件表示</span>
            </div><br />
        </div>

        <div class="panel-body termDiscountDisp" align="center">
            <!-- ソート情報 -->
            <input type="hidden" form="mainForm" id="sortKeyTermDiscount" name="sortKeyTermDiscount"
                value="SAVED_DATETIME" />
            <input type="hidden" form="mainForm" id="sortKeyFlagTermDiscount" name="sortKeyFlagTermDiscount"
                value="false" />

            <table class="table table_searchList table_doubleStriped table-hover" id="termDiscountTable">
                <!-- 期間指定割引リストのヘッダ -->
                <thead>
                    <tr align="center">
                        <td width="5%"></td>
                        <td width="8%">
                            <span>Submission ID</span><br />
                            <button type="button" id="termDiscount_SUBMISSION_IDfalse"
                                class="btn btn-xs stopChannels btn-default" data-sortKey="SUBMISSION_ID"
                                data-desc="false" data-discountTypeTERM="TERM"
                                onclick="changeSort(this.getAttribute(&#39;data-sortKey&#39;), this.getAttribute(&#39;data-desc&#39;), this.getAttribute(&#39;data-discountTypeTERM&#39;));return false;">
                                <span class="glyphicon glyphicon-chevron-down" aria-hidden="true"></span>
                            </button>
                            <button type="button" id="termDiscount_SUBMISSION_IDtrue"
                                class="btn btn-xs stopChannels btn-default" data-sortKey="SUBMISSION_ID" data-asc="true"
                                data-discountTypeTERM="TERM"
                                onclick="changeSort(this.getAttribute(&#39;data-sortKey&#39;), this.getAttribute(&#39;data-asc&#39;), this.getAttribute(&#39;data-discountTypeTERM&#39;));return false;">
                                <span class="glyphicon glyphicon-chevron-up" aria-hidden="true"></span>
                            </button>
                        </td>
                        <td width="10%">
                            <span>Product Code</span><br />
                            <button type="button" id="termDiscount_PRODUCT_CODEfalse"
                                class="btn btn-xs stopChannels btn-default" data-sortKey="PRODUCT_CODE"
                                data-desc="false" data-discountTypeTERM="TERM"
                                onclick="changeSort(this.getAttribute(&#39;data-sortKey&#39;), this.getAttribute(&#39;data-desc&#39;), this.getAttribute(&#39;data-discountTypeTERM&#39;));return false;">
                                <span class="glyphicon glyphicon-chevron-down" aria-hidden="true"></span>
                            </button>
                            <button type="button" id="termDiscount_PRODUCT_CODEtrue"
                                class="btn btn-xs stopChannels btn-default" data-sortKey="PRODUCT_CODE" data-asc="true"
                                data-discountTypeTERM="TERM"
                                onclick="changeSort(this.getAttribute(&#39;data-sortKey&#39;), this.getAttribute(&#39;data-asc&#39;), this.getAttribute(&#39;data-discountTypeTERM&#39;));return false;">
                                <span class="glyphicon glyphicon-chevron-up" aria-hidden="true"></span>
                            </button>
                        </td>
                        <td width="10%">
                            <span>Region</span><br />
                            <button type="button" id="termDiscount_REGIONfalse"
                                class="btn btn-xs stopChannels btn-default" data-sortKey="REGION" data-desc="false"
                                data-discountTypeTERM="TERM"
                                onclick="changeSort(this.getAttribute(&#39;data-sortKey&#39;), this.getAttribute(&#39;data-desc&#39;), this.getAttribute(&#39;data-discountTypeTERM&#39;));return false;">
                                <span class="glyphicon glyphicon-chevron-down" aria-hidden="true"></span>
                            </button>
                            <button type="button" id="termDiscount_REGIONtrue"
                                class="btn btn-xs stopChannels btn-default" data-sortKey="REGION" data-asc="true"
                                data-discountTypeTERM="TERM"
                                onclick="changeSort(this.getAttribute(&#39;data-sortKey&#39;), this.getAttribute(&#39;data-asc&#39;), this.getAttribute(&#39;data-discountTypeTERM&#39;));return false;">
                                <span class="glyphicon glyphicon-chevron-up" aria-hidden="true"></span>
                            </button>
                        </td>
                        <td width="20%">
                            <span>Sale Name</span><br />
                            <button type="button" id="termDiscount_TITLE_NAMEfalse"
                                class="btn btn-xs stopChannels btn-default" data-sortKey="TITLE_NAME" data-desc="false"
                                data-discountTypeTERM="TERM"
                                onclick="changeSort(this.getAttribute(&#39;data-sortKey&#39;), this.getAttribute(&#39;data-desc&#39;), this.getAttribute(&#39;data-discountTypeTERM&#39;));return false;">
                                <span class="glyphicon glyphicon-chevron-down" aria-hidden="true"></span>
                            </button>
                            <button type="button" id="termDiscount_TITLE_NAMEtrue"
                                class="btn btn-xs stopChannels btn-default" data-sortKey="TITLE_NAME" data-asc="true"
                                data-discountTypeTERM="TERM"
                                onclick="changeSort(this.getAttribute(&#39;data-sortKey&#39;), this.getAttribute(&#39;data-asc&#39;), this.getAttribute(&#39;data-discountTypeTERM&#39;));return false;">
                                <span class="glyphicon glyphicon-chevron-up" aria-hidden="true"></span>
                            </button>
                        </td>
                        <td width="10%">
                            <span>Start Date/Time</span><br />
                            <button type="button" id="termDiscount_START_DATETIMEfalse"
                                class="btn btn-xs stopChannels btn-default" data-sortKey="START_DATETIME"
                                data-desc="false" data-discountTypeTERM="TERM"
                                onclick="changeSort(this.getAttribute(&#39;data-sortKey&#39;), this.getAttribute(&#39;data-desc&#39;), this.getAttribute(&#39;data-discountTypeTERM&#39;));return false;">
                                <span class="glyphicon glyphicon-chevron-down" aria-hidden="true"></span>
                            </button>
                            <button type="button" id="termDiscount_START_DATETIMEtrue"
                                class="btn btn-xs stopChannels btn-default" data-sortKey="START_DATETIME"
                                data-asc="true" data-discountTypeTERM="TERM"
                                onclick="changeSort(this.getAttribute(&#39;data-sortKey&#39;), this.getAttribute(&#39;data-asc&#39;), this.getAttribute(&#39;data-discountTypeTERM&#39;));return false;">
                                <span class="glyphicon glyphicon-chevron-up" aria-hidden="true"></span>
                            </button>
                        </td>
                        <td width="10%">
                            <span>End Date/Time</span><br />
                            <button type="button" id="termDiscount_END_DATETIMEfalse"
                                class="btn btn-xs stopChannels btn-default" data-sortKey="END_DATETIME"
                                data-desc="false" data-discountTypeTERM="TERM"
                                onclick="changeSort(this.getAttribute(&#39;data-sortKey&#39;), this.getAttribute(&#39;data-desc&#39;), this.getAttribute(&#39;data-discountTypeTERM&#39;));return false;">
                                <span class="glyphicon glyphicon-chevron-down" aria-hidden="true"></span>
                            </button>
                            <button type="button" id="termDiscount_END_DATETIMEtrue"
                                class="btn btn-xs stopChannels btn-default" data-sortKey="END_DATETIME" data-asc="true"
                                data-discountTypeTERM="TERM"
                                onclick="changeSort(this.getAttribute(&#39;data-sortKey&#39;), this.getAttribute(&#39;data-asc&#39;), this.getAttribute(&#39;data-discountTypeTERM&#39;));return false;">
                                <span class="glyphicon glyphicon-chevron-up" aria-hidden="true"></span>
                            </button>
                        </td>
                        <td width="10%">
                            <span>Status</span><br />
                            <button type="button" id="termDiscount_STATUSfalse"
                                class="btn btn-xs stopChannels btn-default" data-sortKey="STATUS" data-desc="false"
                                data-discountTypeTERM="TERM"
                                onclick="changeSort(this.getAttribute(&#39;data-sortKey&#39;), this.getAttribute(&#39;data-desc&#39;), this.getAttribute(&#39;data-discountTypeTERM&#39;));return false;">
                                <span class="glyphicon glyphicon-chevron-down" aria-hidden="true"></span>
                            </button>
                            <button type="button" id="termDiscount_STATUStrue"
                                class="btn btn-xs stopChannels btn-default" data-sortKey="STATUS" data-asc="true"
                                data-discountTypeTERM="TERM"
                                onclick="changeSort(this.getAttribute(&#39;data-sortKey&#39;), this.getAttribute(&#39;data-asc&#39;), this.getAttribute(&#39;data-discountTypeTERM&#39;));return false;">
                                <span class="glyphicon glyphicon-chevron-up" aria-hidden="true"></span>
                            </button>
                        </td>
                        <td width="7%">
                            <span>Last Update Date/Time</span><br />
                            <button type="button" id="termDiscount_SAVED_DATETIMEfalse"
                                class="btn btn-xs stopChannels btn-default" data-sortKey="SAVED_DATETIME"
                                data-desc="false" data-discountTypeTERM="TERM"
                                onclick="changeSort(this.getAttribute(&#39;data-sortKey&#39;), this.getAttribute(&#39;data-desc&#39;), this.getAttribute(&#39;data-discountTypeTERM&#39;));return false;">
                                <span class="glyphicon glyphicon-chevron-down" aria-hidden="true"></span>
                            </button>
                            <button type="button" id="termDiscount_SAVED_DATETIMEtrue"
                                class="btn btn-xs stopChannels btn-default" data-sortKey="SAVED_DATETIME"
                                data-asc="true" data-discountTypeTERM="TERM"
                                onclick="changeSort(this.getAttribute(&#39;data-sortKey&#39;), this.getAttribute(&#39;data-asc&#39;), this.getAttribute(&#39;data-discountTypeTERM&#39;));return false;">
                                <span class="glyphicon glyphicon-chevron-up" aria-hidden="true"></span>
                            </button>
                        </td>

                    </tr>
                </thead>
                <!-- 期間指定割引リストのデータ部 -->
                <tbody>

                    <tr class="indicator ac table_searchList_odd" align="center" data-content="#termAc0">
                        <td class="ac_open_icon"></td>
                        <td>

                            <a href="../temporaryDiscount/discountInfo?id=190374"
                                onclick="window.open(&#39;../temporaryDiscount/discountInfo?id=190374&#39;);return false;"><span>190374</span></a>


                        </td>
                        <td width="">HAC-P-AURNA</td>
                        <td width="">Europe/Australia</td>
                        <td width="" style="overflow-wrap: break-word; max-width: 150px;">SUPERHOT Sale</td>
                        <td width="">23/12/2023 15:00:00 (UTC)</td>
                        <td width="">21/01/2024 23:59:59 (UTC)</td>
                        <td width="">Process Completed</td>
                        <td width="">11/10/2023 11:06:25 (UTC)</td>

                    </tr>
                    <tr class="ac_close table_bgWhite" id="termAc0">
                        <td class="table_borderLess table_bgWhite"></td>
                        <td class="table_borderLess table_bgWhite" colspan="9">
                            <div>
                                <table class="table table-striped table_searchList_in" style="table-layout: fixed;">
                                    <thead>
                                        <tr align="center">
                                            <td width="10%"><span>Product Code</span></td>
                                            <td width="10%"><span>Content Type</span></td>
                                            <td width="35%"><span>Content Name</span></td>

                                            <td width="15%"><span>Status</span></td>
                                            <td width="15%"><span>Last Update Date/Time</span></td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr align="center">
                                            <td>HAC-P-AURNA</td>
                                            <td>Title</td>
                                            <td style="overflow-wrap: break-word;">SUPERHOT</td>

                                            <td>Approved</td>
                                            <td>02/07/2021 15:18:30 (UTC)</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </td>
                    </tr>

                    <tr class="indicator ac table_searchList_odd" align="center" data-content="#termAc1">
                        <td class="ac_open_icon"></td>
                        <td>

                            <a href="../temporaryDiscount/discountInfo?id=190372"
                                onclick="window.open(&#39;../temporaryDiscount/discountInfo?id=190372&#39;);return false;"><span>190372</span></a>


                        </td>
                        <td width="">HAC-P-AURNA</td>
                        <td width="">Europe/Australia</td>
                        <td width="" style="overflow-wrap: break-word; max-width: 150px;">SUPERHOT Sale</td>
                        <td width="">26/10/2023 15:00:00 (UTC)</td>
                        <td width="">24/11/2023 23:59:59 (UTC)</td>
                        <td width="">Process Completed</td>
                        <td width="">11/10/2023 11:04:14 (UTC)</td>

                    </tr>
                    <tr class="ac_close table_bgWhite" id="termAc1">
                        <td class="table_borderLess table_bgWhite"></td>
                        <td class="table_borderLess table_bgWhite" colspan="9">
                            <div>
                                <table class="table table-striped table_searchList_in" style="table-layout: fixed;">
                                    <thead>
                                        <tr align="center">
                                            <td width="10%"><span>Product Code</span></td>
                                            <td width="10%"><span>Content Type</span></td>
                                            <td width="35%"><span>Content Name</span></td>

                                            <td width="15%"><span>Status</span></td>
                                            <td width="15%"><span>Last Update Date/Time</span></td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr align="center">
                                            <td>HAC-P-AURNA</td>
                                            <td>Title</td>
                                            <td style="overflow-wrap: break-word;">SUPERHOT</td>

                                            <td>Approved</td>
                                            <td>02/07/2021 15:18:30 (UTC)</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </td>
                    </tr>

                    <tr class="indicator ac table_searchList_odd" align="center" data-content="#termAc2">
                        <td class="ac_open_icon"></td>
                        <td>

                            <a href="../temporaryDiscount/discountInfo?id=190370"
                                onclick="window.open(&#39;../temporaryDiscount/discountInfo?id=190370&#39;);return false;"><span>190370</span></a>


                        </td>
                        <td width="">HAC-P-AURNA</td>
                        <td width="">Americas</td>
                        <td width="" style="overflow-wrap: break-word; max-width: 150px;">SUPERHOT Sale</td>
                        <td width="">21/12/2023 18:00:00 (UTC)</td>
                        <td width="">04/01/2024 08:59:59 (UTC)</td>
                        <td width="">Process Completed</td>
                        <td width="">11/10/2023 11:01:18 (UTC)</td>

                    </tr>
                    <tr class="ac_close table_bgWhite" id="termAc2">
                        <td class="table_borderLess table_bgWhite"></td>
                        <td class="table_borderLess table_bgWhite" colspan="9">
                            <div>
                                <table class="table table-striped table_searchList_in" style="table-layout: fixed;">
                                    <thead>
                                        <tr align="center">
                                            <td width="10%"><span>Product Code</span></td>
                                            <td width="10%"><span>Content Type</span></td>
                                            <td width="35%"><span>Content Name</span></td>

                                            <td width="15%"><span>Status</span></td>
                                            <td width="15%"><span>Last Update Date/Time</span></td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr align="center">
                                            <td>HAC-P-AURNA</td>
                                            <td>Title</td>
                                            <td style="overflow-wrap: break-word;">SUPERHOT</td>

                                            <td>Approved</td>
                                            <td>02/07/2021 15:18:29 (UTC)</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </td>
                    </tr>

                    <tr class="indicator ac table_searchList_odd" align="center" data-content="#termAc3">
                        <td class="ac_open_icon"></td>
                        <td>

                            <a href="../temporaryDiscount/discountInfo?id=190366"
                                onclick="window.open(&#39;../temporaryDiscount/discountInfo?id=190366&#39;);return false;"><span>190366</span></a>


                        </td>
                        <td width="">HAC-P-AURNA</td>
                        <td width="">Americas</td>
                        <td width="" style="overflow-wrap: break-word; max-width: 150px;">SUPERHOT Sale</td>
                        <td width="">23/11/2023 18:00:00 (UTC)</td>
                        <td width="">06/12/2023 08:59:59 (UTC)</td>
                        <td width="">Process Completed</td>
                        <td width="">11/10/2023 10:58:35 (UTC)</td>

                    </tr>
                    <tr class="ac_close table_bgWhite" id="termAc3">
                        <td class="table_borderLess table_bgWhite"></td>
                        <td class="table_borderLess table_bgWhite" colspan="9">
                            <div>
                                <table class="table table-striped table_searchList_in" style="table-layout: fixed;">
                                    <thead>
                                        <tr align="center">
                                            <td width="10%"><span>Product Code</span></td>
                                            <td width="10%"><span>Content Type</span></td>
                                            <td width="35%"><span>Content Name</span></td>

                                            <td width="15%"><span>Status</span></td>
                                            <td width="15%"><span>Last Update Date/Time</span></td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr align="center">
                                            <td>HAC-P-AURNA</td>
                                            <td>Title</td>
                                            <td style="overflow-wrap: break-word;">SUPERHOT</td>

                                            <td>Approved</td>
                                            <td>02/07/2021 15:18:29 (UTC)</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </td>
                    </tr>

                    <tr class="indicator ac table_searchList_odd" align="center" data-content="#termAc4">
                        <td class="ac_open_icon"></td>
                        <td>

                            <a href="../temporaryDiscount/discountInfo?id=190363"
                                onclick="window.open(&#39;../temporaryDiscount/discountInfo?id=190363&#39;);return false;"><span>190363</span></a>


                        </td>
                        <td width="">HAC-P-AURNA</td>
                        <td width="">Americas</td>
                        <td width="" style="overflow-wrap: break-word; max-width: 150px;">SUPERHOT Sale</td>
                        <td width="">26/10/2023 18:00:00 (UTC)</td>
                        <td width="">09/11/2023 08:59:59 (UTC)</td>
                        <td width="">Process Completed</td>
                        <td width="">11/10/2023 10:56:34 (UTC)</td>

                    </tr>
                    <tr class="ac_close table_bgWhite" id="termAc4">
                        <td class="table_borderLess table_bgWhite"></td>
                        <td class="table_borderLess table_bgWhite" colspan="9">
                            <div>
                                <table class="table table-striped table_searchList_in" style="table-layout: fixed;">
                                    <thead>
                                        <tr align="center">
                                            <td width="10%"><span>Product Code</span></td>
                                            <td width="10%"><span>Content Type</span></td>
                                            <td width="35%"><span>Content Name</span></td>

                                            <td width="15%"><span>Status</span></td>
                                            <td width="15%"><span>Last Update Date/Time</span></td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr align="center">
                                            <td>HAC-P-AURNA</td>
                                            <td>Title</td>
                                            <td style="overflow-wrap: break-word;">SUPERHOT</td>

                                            <td>Approved</td>
                                            <td>02/07/2021 15:18:29 (UTC)</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </td>
                    </tr>

                    <tr class="indicator ac table_searchList_odd" align="center" data-content="#termAc5">
                        <td class="ac_open_icon"></td>
                        <td>

                            <a href="../temporaryDiscount/discountInfo?id=181850"
                                onclick="window.open(&#39;../temporaryDiscount/discountInfo?id=181850&#39;);return false;"><span>181850</span></a>


                        </td>
                        <td width="">HAC-P-AURNA</td>
                        <td width="">Americas</td>
                        <td width="" style="overflow-wrap: break-word; max-width: 150px;">SUPERHOT Sale</td>
                        <td width="">27/09/2023 18:00:00 (UTC)</td>
                        <td width="">11/10/2023 08:59:59 (UTC)</td>
                        <td width="">Process Completed</td>
                        <td width="">27/08/2023 12:44:02 (UTC)</td>

                    </tr>
                    <tr class="ac_close table_bgWhite" id="termAc5">
                        <td class="table_borderLess table_bgWhite"></td>
                        <td class="table_borderLess table_bgWhite" colspan="9">
                            <div>
                                <table class="table table-striped table_searchList_in" style="table-layout: fixed;">
                                    <thead>
                                        <tr align="center">
                                            <td width="10%"><span>Product Code</span></td>
                                            <td width="10%"><span>Content Type</span></td>
                                            <td width="35%"><span>Content Name</span></td>

                                            <td width="15%"><span>Status</span></td>
                                            <td width="15%"><span>Last Update Date/Time</span></td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr align="center">
                                            <td>HAC-P-AURNA</td>
                                            <td>Title</td>
                                            <td style="overflow-wrap: break-word;">SUPERHOT</td>

                                            <td>Approved</td>
                                            <td>02/07/2021 15:18:29 (UTC)</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </td>
                    </tr>

                    <tr class="indicator ac table_searchList_odd" align="center" data-content="#termAc6">
                        <td class="ac_open_icon"></td>
                        <td>

                            <a href="../temporaryDiscount/discountInfo?id=181849"
                                onclick="window.open(&#39;../temporaryDiscount/discountInfo?id=181849&#39;);return false;"><span>181849</span></a>


                        </td>
                        <td width="">HAC-P-AURNA</td>
                        <td width="">Americas</td>
                        <td width="" style="overflow-wrap: break-word; max-width: 150px;">SUPERHOT Sale</td>
                        <td width="">31/08/2023 18:00:00 (UTC)</td>
                        <td width="">12/09/2023 08:59:59 (UTC)</td>
                        <td width="">Process Completed</td>
                        <td width="">27/08/2023 12:41:25 (UTC)</td>

                    </tr>
                    <tr class="ac_close table_bgWhite" id="termAc6">
                        <td class="table_borderLess table_bgWhite"></td>
                        <td class="table_borderLess table_bgWhite" colspan="9">
                            <div>
                                <table class="table table-striped table_searchList_in" style="table-layout: fixed;">
                                    <thead>
                                        <tr align="center">
                                            <td width="10%"><span>Product Code</span></td>
                                            <td width="10%"><span>Content Type</span></td>
                                            <td width="35%"><span>Content Name</span></td>

                                            <td width="15%"><span>Status</span></td>
                                            <td width="15%"><span>Last Update Date/Time</span></td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr align="center">
                                            <td>HAC-P-AURNA</td>
                                            <td>Title</td>
                                            <td style="overflow-wrap: break-word;">SUPERHOT</td>

                                            <td>Approved</td>
                                            <td>02/07/2021 15:18:29 (UTC)</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </td>
                    </tr>

                    <tr class="indicator ac table_searchList_odd" align="center" data-content="#termAc7">
                        <td class="ac_open_icon"></td>
                        <td>

                            <a href="../temporaryDiscount/discountInfo?id=181848"
                                onclick="window.open(&#39;../temporaryDiscount/discountInfo?id=181848&#39;);return false;"><span>181848</span></a>


                        </td>
                        <td width="">HAC-P-AURNA</td>
                        <td width="">Europe/Australia</td>
                        <td width="" style="overflow-wrap: break-word; max-width: 150px;">SUPERHOT Sale</td>
                        <td width="">02/09/2023 15:00:00 (UTC)</td>
                        <td width="">27/09/2023 23:59:59 (UTC)</td>
                        <td width="">Process Completed</td>
                        <td width="">27/08/2023 12:39:21 (UTC)</td>

                    </tr>
                    <tr class="ac_close table_bgWhite" id="termAc7">
                        <td class="table_borderLess table_bgWhite"></td>
                        <td class="table_borderLess table_bgWhite" colspan="9">
                            <div>
                                <table class="table table-striped table_searchList_in" style="table-layout: fixed;">
                                    <thead>
                                        <tr align="center">
                                            <td width="10%"><span>Product Code</span></td>
                                            <td width="10%"><span>Content Type</span></td>
                                            <td width="35%"><span>Content Name</span></td>

                                            <td width="15%"><span>Status</span></td>
                                            <td width="15%"><span>Last Update Date/Time</span></td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr align="center">
                                            <td>HAC-P-AURNA</td>
                                            <td>Title</td>
                                            <td style="overflow-wrap: break-word;">SUPERHOT</td>

                                            <td>Approved</td>
                                            <td>02/07/2021 15:18:30 (UTC)</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </td>
                    </tr>

                    <tr class="indicator ac table_searchList_odd" align="center" data-content="#termAc8">
                        <td class="ac_open_icon"></td>
                        <td>

                            <a href="../temporaryDiscount/discountInfo?id=172171"
                                onclick="window.open(&#39;../temporaryDiscount/discountInfo?id=172171&#39;);return false;"><span>172171</span></a>


                        </td>
                        <td width="">HAC-P-AURNA</td>
                        <td width="">Americas</td>
                        <td width="" style="overflow-wrap: break-word; max-width: 150px;">SUPERHOT Sale</td>
                        <td width="">04/08/2023 18:00:00 (UTC)</td>
                        <td width="">18/08/2023 08:59:59 (UTC)</td>
                        <td width="">Process Completed</td>
                        <td width="">04/07/2023 19:25:46 (UTC)</td>

                    </tr>
                    <tr class="ac_close table_bgWhite" id="termAc8">
                        <td class="table_borderLess table_bgWhite"></td>
                        <td class="table_borderLess table_bgWhite" colspan="9">
                            <div>
                                <table class="table table-striped table_searchList_in" style="table-layout: fixed;">
                                    <thead>
                                        <tr align="center">
                                            <td width="10%"><span>Product Code</span></td>
                                            <td width="10%"><span>Content Type</span></td>
                                            <td width="35%"><span>Content Name</span></td>

                                            <td width="15%"><span>Status</span></td>
                                            <td width="15%"><span>Last Update Date/Time</span></td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr align="center">
                                            <td>HAC-P-AURNA</td>
                                            <td>Title</td>
                                            <td style="overflow-wrap: break-word;">SUPERHOT</td>

                                            <td>Approved</td>
                                            <td>02/07/2021 15:18:29 (UTC)</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </td>
                    </tr>

                    <tr class="indicator ac table_searchList_odd" align="center" data-content="#termAc9">
                        <td class="ac_open_icon"></td>
                        <td>

                            <a href="../temporaryDiscount/discountInfo?id=172169"
                                onclick="window.open(&#39;../temporaryDiscount/discountInfo?id=172169&#39;);return false;"><span>172169</span></a>


                        </td>
                        <td width="">HAC-P-AURNA</td>
                        <td width="">Americas</td>
                        <td width="" style="overflow-wrap: break-word; max-width: 150px;">SUPERHOT Sale</td>
                        <td width="">06/07/2023 18:00:00 (UTC)</td>
                        <td width="">20/07/2023 08:59:59 (UTC)</td>
                        <td width="">Process Completed</td>
                        <td width="">04/07/2023 19:22:57 (UTC)</td>

                    </tr>
                    <tr class="ac_close table_bgWhite" id="termAc9">
                        <td class="table_borderLess table_bgWhite"></td>
                        <td class="table_borderLess table_bgWhite" colspan="9">
                            <div>
                                <table class="table table-striped table_searchList_in" style="table-layout: fixed;">
                                    <thead>
                                        <tr align="center">
                                            <td width="10%"><span>Product Code</span></td>
                                            <td width="10%"><span>Content Type</span></td>
                                            <td width="35%"><span>Content Name</span></td>

                                            <td width="15%"><span>Status</span></td>
                                            <td width="15%"><span>Last Update Date/Time</span></td>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr align="center">
                                            <td>HAC-P-AURNA</td>
                                            <td>Title</td>
                                            <td style="overflow-wrap: break-word;">SUPERHOT</td>

                                            <td>Approved</td>
                                            <td>02/07/2021 15:18:29 (UTC)</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </td>
                    </tr>

                </tbody>
            </table>
        </div>
    </div>




    <script type="text/javascript" src="/ncms3/js/discount/searchResult-cdc7346e620b00acb34d0ac490883a0f.js"></script>

    <script>
        /*<![CDATA[*/

        /** 製品コード */
        var productCodeText = "Product Code";
        /** コンテンツ種別 */
        var contentTypeText = "Content Type";
        /** コンテンツ名 */
        var contentNameText = "Content Name";
        /** メーカー */
        var publisherText = "Publisher";
        /** ステータス */
        var statusText = "Status";
        /** 最終更新日時 */
        var lastEditedDatetimeText = "Last Update Date\/Time";

        /*]]>*/
    </script>
</div>
