<div>

    <!-- /* セール名称 */ -->
    <h2 class="itemHeader" style="margin-top: 30px" aria-expanded="true" data-toggle="collapse"
        data-target="#discountInfoSaleNameContainer">
        <span>Sale Name</span>
        <span class="glyphicon glyphicon-chevron-up"></span>
    </h2>
    <div id="discountInfoSaleNameContainer" class="collapse in">
        <div class="noteArea editable" style="margin:10px;">
            <i class="glyphicon glyphicon-info-sign" style="margin-right:3px; color:#A4A4A4;"></i>
            <b style="margin-right:3px;">
                <span>Attention:</span>
            </b>
            <br>
            <div style="padding-left:20px;">
                <span>Be sure to include the name of the publisher or series name in the
                    sale name so that it can be differentiated from other
                    sales.<br><br>■ Good examples<br>• Metroid Series 25th anniversary
                    spring sale!<br>• 30% off all Nintendo software autumn
                    Sale!<br><br>■ Bad examples<br>• 2018 spring sale!<br>• Golden Week
                    half-off sale<br><br></span>
            </div>
            <div id="jpnNote" style="padding-left:20px;margin-top:2em;font-weight:bold;display:none;">
                <span>The Sale Name is internal only - not displayed to users
                    on-device.</span>
            </div>
        </div>
        <table class="itemTable" style="table-layout: fixed;">
            <tbody>
                <tr>
                    <td class="itemCaption">
                        <div style="position: relative;">
                            <span class="itemColumn">Sale Name</span>
                        </div>
                        <div>
                            <span class="IndispensableItem">[Required]</span>
                        </div>
                    </td>
                    <td>
                        <div style="position: relative;display: none;" class="editable notFontWidthCheck">
                            <textarea class="notFontWidthCheck" validate="required,line:2" id="saleName"
                                name="saleName">SUPERHOT Sale</textarea>
                        </div>
                        <div style="position: relative;display: none;" class="editable fontWidthCheck">
                            <textarea class="fontWidthCheck" validate="line:2,characherwidth,required" id="saleName"
                                name="saleName">SUPERHOT Sale</textarea>
                            <div class="editable fontWidthCheck">
                                <span class="fontwidth">横幅 0/265pixel</span>
                                <input type="hidden" class="fontWidthResult">
                            </div>
                        </div>

                        <div style="position: relative; display: none;" class="nonEditable">
                            <textarea name="saleName_nonEdit" class="uneditable"
                                readonly="readonly">SUPERHOT Sale</textarea>
                        </div>


                    </td>
                </tr>
            </tbody>
        </table>
        <div id="saleNameHiddenArea">

        </div>
        <input type="hidden" id="liveSaleName" name="liveSaleName" value="SUPERHOT Sale">
    </div>
</div>
