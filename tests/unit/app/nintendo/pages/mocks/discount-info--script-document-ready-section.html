<script>
    $(document).ready(function () {
        var titleSortKeySelector = "TITLE-SAVED_DATETIME-false";
        var aocSortKeySelector = "AOC-SAVED_DATETIME-false";
        var bundleSortKeySelector = "BUNDLE-SAVED_DATETIME-false";
        var consumableItemSortKeySelector = "CONSUMABLEITEM-SAVED_DATETIME-false";
        $("[id^='" + titleSortKeySelector + "']").prop("disabled", true);
        $("[id^='" + titleSortKeySelector + "']").removeClass('btn-default').addClass('btn-primary');
        $("[id^='" + aocSortKeySelector + "']").prop("disabled", true);
        $("[id^='" + aocSortKeySelector + "']").removeClass('btn-default').addClass('btn-primary');
        $("[id^='" + bundleSortKeySelector + "']").prop("disabled", true);
        $("[id^='" + bundleSortKeySelector + "']").removeClass('btn-default').addClass('btn-primary');
        $("[id^='" + consumableItemSortKeySelector + "']").prop("disabled", true);
        $("[id^='" + consumableItemSortKeySelector + "']").removeClass('btn-default').addClass('btn-primary');

        // ログインユーザのリージョンのガイドラインを開く
        var accordionGuidelineCategory;
        if (false) {
            accordionGuidelineCategory = $('#accordion-jpn-guideline-category');
        } else if (false) {
            accordionGuidelineCategory = $('#accordion-usa-guideline-category');
        } else if (true) {
            accordionGuidelineCategory = $('#accordion-eur-guideline-category');
        } else if ((false) || (false)) {
            accordionGuidelineCategory = $('#accordion-asia-guideline-category');
        }

        if (accordionGuidelineCategory != null) {
            accordionGuidelineCategory.attr('aria-expanded', true);
            accordionGuidelineCategory.addClass('in');
            var icon = accordionGuidelineCategory.prev().find("span.glyphicon");
            icon.removeClass('glyphicon-chevron-down');
            icon.addClass('glyphicon-chevron-up');
        }
    });

    /*<![CDATA[*/

    var isNintendoUser = false;
    var isOperating = false;
    /** リージョンコードのマップ */
    function getRegionCodeMap() {
        return { "1": "JPN", "2": "USA", "3": "EUR", "5": "KOR", "6": "CHN", "7": "TWN", "10": "ASIA" };
    }
    /** リージョンごとのタイムゾーン（正式名称）のマップ */
    function getRegionTimezoneNameMap() {
        return { "1": "Asia\/Tokyo", "2": "America\/Los_Angeles", "3": "Europe\/Berlin", "5": "Asia\/Seoul", "6": "Asia\/Shanghai", "7": "Asia\/Hong_Kong", "10": "Asia\/Tokyo" };
    }

    /** リージョンごとの国ISOコードのマップ */
    function getRegionCountryIsoCodeMap() {
        return { "1": ["JP"], "2": ["AI", "AG", "AR", "AW", "BS", "BB", "BZ", "BO", "BR", "VG", "CA", "KY", "CL", "CO", "CR", "DM", "DO", "EC", "SV", "GF", "GD", "GP", "GT", "GY", "HT", "HN", "JM", "MQ", "MX", "MS", "AN", "NI", "PA", "PY", "PE", "KN", "LC", "VC", "SR", "TT", "TC", "US", "UY", "VI", "VE", "SG", "MY", "AE", "SA", "BM"], "3": ["AT", "BE", "BG", "HR", "CY", "CZ", "DK", "EE", "FI", "FR", "DE", "GR", "HU", "IE", "IT", "LV", "LT", "LU", "MT", "NL", "NO", "PL", "PT", "RO", "RU", "SK", "SI", "ZA", "ES", "SE", "CH", "GB", "AU", "NZ", "IL"], "5": ["KR"], "6": ["CN"], "7": ["TW", "HK"], "10": [] };
    }

    /** 国マスタのマップ */
    function getCountryMap() {
        return { "1": { "1": "Japan" }, "2": { "2": "Anguilla", "3": "Antigua and Barbuda", "4": "Argentina", "5": "Aruba", "7": "Barbados", "8": "Belize", "121": "Bermuda", "10": "Brazil", "11": "British Virgin Islands", "12": "Canada", "13": "Cayman Islands", "14": "Chile", "25": "Co-operative Republic of Guyana", "15": "Colombia", "17": "Commonwealth of Dominica", "6": "Commonwealth of The Bahamas", "16": "Costa Rica", "18": "Dominican Republic", "19": "Ecuador", "22": "Grenada", "23": "Guadeloupe", "24": "Guatemala", "21": "Guiana", "27": "Honduras", "28": "Jamaica", "50": "Kingdom of Saudi Arabia", "48": "Malaysia", "29": "Martinique", "30": "Mexico", "31": "Montserrat", "32": "Nederlandse Antillen", "33": "Nicaragua", "34": "Panama", "35": "Paraguay", "36": "Peru", "9": "Republic of Bolivia", "20": "Republic of El Salvador", "26": "Republic of Haiti", "47": "Republic of Singapore", "40": "Republic of Suriname", "41": "Republic of Trinidad and Tobago", "37": "Saint Christopher and Nevis", "38": "Saint Lucia", "39": "Saint Vincent and the Grenadines", "42": "Turks and Caicos Islands", "49": "United Arab Emirates", "43": "United States", "44": "Uruguay", "46": "Venezuela", "45": "Virgin Islands of the United States" }, "3": { "116": "Australia", "52": "Austria", "53": "Belgium", "56": "Bulgaria", "57": "Croatia", "58": "Cyprus", "59": "Czech Republic", "60": "Denmark", "61": "Estonia", "62": "Finland", "63": "France", "64": "Germany", "65": "Greece", "66": "Hungary", "68": "Ireland", "69": "Italy", "70": "Latvia", "73": "Lithuania", "74": "Luxembourg", "76": "Malta", "80": "Netherlands", "117": "New Zealand", "81": "Norway", "82": "Poland", "83": "Portugal", "84": "Romania", "85": "Russia", "87": "Slovak Republic", "88": "Slovenia", "89": "South Africa", "90": "Spain", "123": "State of Israel", "92": "Sweden", "93": "Switzerland", "95": "United Kingdom" }, "5": { "118": "Korea" }, "6": { "122": "China" }, "7": { "120": "Hong Kong", "119": "Taiwan" }, "10": {} };
    }
    /** タイムゾーン名のマップ */
    function getTimezoneNameMap() {
        return { "DE": "CET", "NO": "CET", "BE": "CET", "FI": "EET", "RU": "MSK", "PT": "WET", "BG": "EET", "DK": "CET", "LT": "EET", "LU": "CET", "HR": "CET", "LV": "EET", "FR": "CET", "NZ": "NZST", "HU": "CET", "SE": "CET", "SI": "CET", "SK": "CET", "GB": "GMT", "IE": "GMT", "EE": "EET", "IL": "IST", "CH": "CET", "MT": "CET", "ZA": "SAST", "GR": "EET", "IT": "CET", "ES": "CET", "AT": "CET", "AU": "AEST", "CY": "EET", "CZ": "CET", "PL": "CET", "RO": "EET", "NL": "CET" };
    }

    /** 言語情報のマップ */
    function getLangMap() {
        return { "ja": "Japanese", "en_US": "English", "fr_US": "Canadian French", "es_US": "Latin American Spanish", "pt_US": "Brazilian Portuguese", "en": "English", "fr": "French", "it": "Italian", "de": "German", "es": "Spanish", "nl": "Dutch", "pt": "Portuguese", "ru": "Russian", "en_AU": "Australian English", "ko": "Korean", "zh_CN": "Simplified Chinese", "zh_TW": "Traditional Chinese" };
    }

    /** 国言語のマップ */
    function getCountryLangMap() {
        return { "TT": ["en_US", "es_US"], "DE": ["de"], "TW": ["zh_TW"], "HK": ["zh_TW"], "PT": ["pt"], "HN": ["en_US", "es_US"], "DK": ["en"], "LT": ["en"], "DM": ["en_US", "es_US"], "PY": ["en_US", "es_US"], "LU": ["fr", "de"], "HR": ["en"], "LV": ["en"], "DO": ["en_US", "es_US"], "HT": ["en_US", "es_US"], "HU": ["en"], "IE": ["en"], "EC": ["en_US", "es_US"], "US": ["en_US", "es_US"], "EE": ["en"], "IL": ["en"], "MQ": ["en_US", "es_US"], "UY": ["en_US", "es_US"], "AE": ["en_US", "es_US"], "AG": ["en_US", "es_US"], "MS": ["en_US", "es_US"], "MT": ["en"], "ZA": ["en"], "AI": ["en_US", "es_US"], "MX": ["en_US", "es_US"], "IT": ["it"], "MY": ["en_US", "es_US"], "AN": ["en_US", "es_US"], "VC": ["en_US", "es_US"], "ES": ["es"], "VE": ["en_US", "es_US"], "AR": ["en_US", "es_US"], "VG": ["en_US", "es_US"], "VI": ["en_US", "es_US"], "AT": ["de"], "AU": ["en_AU"], "AW": ["en_US", "es_US"], "NI": ["en_US", "es_US"], "RO": ["en"], "NL": ["nl"], "BB": ["en_US", "es_US"], "NO": ["en"], "JM": ["en_US", "es_US"], "FI": ["en"], "BE": ["nl", "fr"], "RU": ["ru"], "BG": ["en"], "JP": ["ja"], "BM": ["en_US", "es_US"], "FR": ["fr"], "NZ": ["en_AU"], "BO": ["en_US", "es_US"], "SA": ["en_US", "es_US"], "BR": ["en_US", "pt_US"], "BS": ["en_US", "es_US"], "SE": ["en"], "SG": ["en_US", "es_US"], "SI": ["en"], "BZ": ["en_US", "es_US"], "SK": ["en"], "GB": ["en"], "GD": ["en_US", "es_US"], "CA": ["en_US", "fr_US"], "GF": ["en_US", "es_US"], "SR": ["en_US", "es_US"], "SV": ["en_US", "es_US"], "KN": ["en_US", "es_US"], "CH": ["fr", "de", "it"], "KR": ["ko"], "CL": ["en_US", "es_US"], "GP": ["en_US", "es_US"], "GR": ["en"], "CN": ["zh_CN"], "CO": ["en_US", "es_US"], "GT": ["en_US", "es_US"], "KY": ["en_US", "es_US"], "CR": ["en_US", "es_US"], "TC": ["en_US", "es_US"], "PA": ["en_US", "es_US"], "GY": ["en_US", "es_US"], "PE": ["en_US", "es_US"], "CY": ["en"], "LC": ["en_US", "es_US"], "CZ": ["en"], "PL": ["en"] };
    }

    /** 日付フォーマット */
    function getDateFormat() {
        return "DD\/MM\/YYYY";
    }

    /** 日時フォーマット */
    function getDatetimeFormat() {
        return "DD\/MM\/YYYY HH:mm:ss";
    }

    /** 日時未指定の場合のラベル表示用文字列 */
    function getNoDatetimeLabel() {
        return "--\/--\/---- --:--:-- (----)";
    }

    /** サーバーから渡された国別情報JSON文字列を返す */
    function getCountryInfListJson() {
        return "[{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"JP\",\"countryName\":\"Japan\",\"currency\":{\"currencyCode\":\"JPY\",\"currencySign\":\"\\\\\",\"currencySignPosition\":\"1\",\"decimal\":\"\",\"maskMoneyApplied\":false,\"maxLength\":5,\"precision\":0,\"thousands\":\",\"},\"displayOrder\":1,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"Asia\/Tokyo\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"US\",\"countryName\":\"United States\",\"currency\":{\"currencyCode\":\"USD\",\"currencySign\":\"$\",\"currencySignPosition\":\"1\",\"decimal\":\".\",\"maskMoneyApplied\":true,\"maxLength\":11,\"precision\":2,\"thousands\":\",\"},\"displayOrder\":2,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"America\/Los_Angeles\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"CA\",\"countryName\":\"Canada\",\"currency\":{\"currencyCode\":\"CAD\",\"currencySign\":\"$\",\"currencySignPosition\":\"1\",\"decimal\":\".\",\"maskMoneyApplied\":true,\"maxLength\":11,\"precision\":2,\"thousands\":\",\"},\"displayOrder\":3,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"America\/Los_Angeles\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"MX\",\"countryName\":\"Mexico\",\"currency\":{\"currencyCode\":\"MXN\",\"currencySign\":\"MX$\",\"currencySignPosition\":\"1\",\"decimal\":\".\",\"maskMoneyApplied\":true,\"maxLength\":13,\"precision\":2,\"thousands\":\",\"},\"displayOrder\":4,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"America\/Los_Angeles\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"BR\",\"countryName\":\"Brazil\",\"currency\":{\"currencyCode\":\"BRL\",\"currencySign\":\"R$\",\"currencySignPosition\":\"1\",\"decimal\":\",\",\"maskMoneyApplied\":true,\"maxLength\":12,\"precision\":2,\"thousands\":\".\"},\"displayOrder\":5,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"America\/Los_Angeles\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"AI\",\"countryName\":\"Anguilla\",\"currency\":{\"currencyCode\":\"USD\",\"currencySign\":\"$\",\"currencySignPosition\":\"1\",\"decimal\":\".\",\"maskMoneyApplied\":true,\"maxLength\":11,\"precision\":2,\"thousands\":\",\"},\"displayOrder\":6,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"America\/Los_Angeles\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"AG\",\"countryName\":\"Antigua and Barbuda\",\"currency\":{\"currencyCode\":\"USD\",\"currencySign\":\"$\",\"currencySignPosition\":\"1\",\"decimal\":\".\",\"maskMoneyApplied\":true,\"maxLength\":11,\"precision\":2,\"thousands\":\",\"},\"displayOrder\":7,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"America\/Los_Angeles\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"AR\",\"countryName\":\"Argentina\",\"currency\":{\"currencyCode\":\"ARS\",\"currencySign\":\"$\",\"currencySignPosition\":\"1\",\"decimal\":\",\",\"maskMoneyApplied\":true,\"maxLength\":12,\"precision\":2,\"thousands\":\".\"},\"displayOrder\":8,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"America\/Los_Angeles\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"AW\",\"countryName\":\"Aruba\",\"currency\":{\"currencyCode\":\"USD\",\"currencySign\":\"$\",\"currencySignPosition\":\"1\",\"decimal\":\".\",\"maskMoneyApplied\":true,\"maxLength\":11,\"precision\":2,\"thousands\":\",\"},\"displayOrder\":9,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"America\/Los_Angeles\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"BS\",\"countryName\":\"Commonwealth of The Bahamas\",\"currency\":{\"currencyCode\":\"USD\",\"currencySign\":\"$\",\"currencySignPosition\":\"1\",\"decimal\":\".\",\"maskMoneyApplied\":true,\"maxLength\":11,\"precision\":2,\"thousands\":\",\"},\"displayOrder\":10,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"America\/Los_Angeles\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"BB\",\"countryName\":\"Barbados\",\"currency\":{\"currencyCode\":\"USD\",\"currencySign\":\"$\",\"currencySignPosition\":\"1\",\"decimal\":\".\",\"maskMoneyApplied\":true,\"maxLength\":11,\"precision\":2,\"thousands\":\",\"},\"displayOrder\":11,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"America\/Los_Angeles\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"BZ\",\"countryName\":\"Belize\",\"currency\":{\"currencyCode\":\"USD\",\"currencySign\":\"$\",\"currencySignPosition\":\"1\",\"decimal\":\".\",\"maskMoneyApplied\":true,\"maxLength\":11,\"precision\":2,\"thousands\":\",\"},\"displayOrder\":12,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"America\/Los_Angeles\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"BM\",\"countryName\":\"Bermuda\",\"currency\":{\"currencyCode\":\"USD\",\"currencySign\":\"$\",\"currencySignPosition\":\"1\",\"decimal\":\".\",\"maskMoneyApplied\":true,\"maxLength\":11,\"precision\":2,\"thousands\":\",\"},\"displayOrder\":13,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"America\/Los_Angeles\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"BO\",\"countryName\":\"Republic of Bolivia\",\"currency\":{\"currencyCode\":\"USD\",\"currencySign\":\"$\",\"currencySignPosition\":\"1\",\"decimal\":\".\",\"maskMoneyApplied\":true,\"maxLength\":11,\"precision\":2,\"thousands\":\",\"},\"displayOrder\":14,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"America\/Los_Angeles\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"VG\",\"countryName\":\"British Virgin Islands\",\"currency\":{\"currencyCode\":\"USD\",\"currencySign\":\"$\",\"currencySignPosition\":\"1\",\"decimal\":\".\",\"maskMoneyApplied\":true,\"maxLength\":11,\"precision\":2,\"thousands\":\",\"},\"displayOrder\":15,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"America\/Los_Angeles\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"KY\",\"countryName\":\"Cayman Islands\",\"currency\":{\"currencyCode\":\"USD\",\"currencySign\":\"$\",\"currencySignPosition\":\"1\",\"decimal\":\".\",\"maskMoneyApplied\":true,\"maxLength\":11,\"precision\":2,\"thousands\":\",\"},\"displayOrder\":16,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"America\/Los_Angeles\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"CL\",\"countryName\":\"Chile\",\"currency\":{\"currencyCode\":\"CLP\",\"currencySign\":\"$\",\"currencySignPosition\":\"1\",\"decimal\":\",\",\"maskMoneyApplied\":true,\"maxLength\":9,\"precision\":0,\"thousands\":\".\"},\"displayOrder\":17,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"America\/Los_Angeles\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"CO\",\"countryName\":\"Colombia\",\"currency\":{\"currencyCode\":\"COP\",\"currencySign\":\"$\",\"currencySignPosition\":\"1\",\"decimal\":\",\",\"maskMoneyApplied\":true,\"maxLength\":9,\"precision\":0,\"thousands\":\".\"},\"displayOrder\":18,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"America\/Los_Angeles\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"CR\",\"countryName\":\"Costa Rica\",\"currency\":{\"currencyCode\":\"USD\",\"currencySign\":\"$\",\"currencySignPosition\":\"1\",\"decimal\":\".\",\"maskMoneyApplied\":true,\"maxLength\":11,\"precision\":2,\"thousands\":\",\"},\"displayOrder\":19,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"America\/Los_Angeles\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"DM\",\"countryName\":\"Commonwealth of Dominica\",\"currency\":{\"currencyCode\":\"USD\",\"currencySign\":\"$\",\"currencySignPosition\":\"1\",\"decimal\":\".\",\"maskMoneyApplied\":true,\"maxLength\":11,\"precision\":2,\"thousands\":\",\"},\"displayOrder\":20,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"America\/Los_Angeles\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"DO\",\"countryName\":\"Dominican Republic\",\"currency\":{\"currencyCode\":\"USD\",\"currencySign\":\"$\",\"currencySignPosition\":\"1\",\"decimal\":\".\",\"maskMoneyApplied\":true,\"maxLength\":11,\"precision\":2,\"thousands\":\",\"},\"displayOrder\":21,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"America\/Los_Angeles\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"EC\",\"countryName\":\"Ecuador\",\"currency\":{\"currencyCode\":\"USD\",\"currencySign\":\"$\",\"currencySignPosition\":\"1\",\"decimal\":\".\",\"maskMoneyApplied\":true,\"maxLength\":11,\"precision\":2,\"thousands\":\",\"},\"displayOrder\":22,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"America\/Los_Angeles\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"SV\",\"countryName\":\"Republic of El Salvador\",\"currency\":{\"currencyCode\":\"USD\",\"currencySign\":\"$\",\"currencySignPosition\":\"1\",\"decimal\":\".\",\"maskMoneyApplied\":true,\"maxLength\":11,\"precision\":2,\"thousands\":\",\"},\"displayOrder\":23,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"America\/Los_Angeles\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"GF\",\"countryName\":\"Guiana\",\"currency\":{\"currencyCode\":\"USD\",\"currencySign\":\"$\",\"currencySignPosition\":\"1\",\"decimal\":\".\",\"maskMoneyApplied\":true,\"maxLength\":11,\"precision\":2,\"thousands\":\",\"},\"displayOrder\":24,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"America\/Los_Angeles\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"GD\",\"countryName\":\"Grenada\",\"currency\":{\"currencyCode\":\"USD\",\"currencySign\":\"$\",\"currencySignPosition\":\"1\",\"decimal\":\".\",\"maskMoneyApplied\":true,\"maxLength\":11,\"precision\":2,\"thousands\":\",\"},\"displayOrder\":25,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"America\/Los_Angeles\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"GP\",\"countryName\":\"Guadeloupe\",\"currency\":{\"currencyCode\":\"USD\",\"currencySign\":\"$\",\"currencySignPosition\":\"1\",\"decimal\":\".\",\"maskMoneyApplied\":true,\"maxLength\":11,\"precision\":2,\"thousands\":\",\"},\"displayOrder\":26,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"America\/Los_Angeles\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"GT\",\"countryName\":\"Guatemala\",\"currency\":{\"currencyCode\":\"USD\",\"currencySign\":\"$\",\"currencySignPosition\":\"1\",\"decimal\":\".\",\"maskMoneyApplied\":true,\"maxLength\":11,\"precision\":2,\"thousands\":\",\"},\"displayOrder\":27,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"America\/Los_Angeles\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"GY\",\"countryName\":\"Co-operative Republic of Guyana\",\"currency\":{\"currencyCode\":\"USD\",\"currencySign\":\"$\",\"currencySignPosition\":\"1\",\"decimal\":\".\",\"maskMoneyApplied\":true,\"maxLength\":11,\"precision\":2,\"thousands\":\",\"},\"displayOrder\":28,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"America\/Los_Angeles\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"HT\",\"countryName\":\"Republic of Haiti\",\"currency\":{\"currencyCode\":\"USD\",\"currencySign\":\"$\",\"currencySignPosition\":\"1\",\"decimal\":\".\",\"maskMoneyApplied\":true,\"maxLength\":11,\"precision\":2,\"thousands\":\",\"},\"displayOrder\":29,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"America\/Los_Angeles\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"HN\",\"countryName\":\"Honduras\",\"currency\":{\"currencyCode\":\"USD\",\"currencySign\":\"$\",\"currencySignPosition\":\"1\",\"decimal\":\".\",\"maskMoneyApplied\":true,\"maxLength\":11,\"precision\":2,\"thousands\":\",\"},\"displayOrder\":30,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"America\/Los_Angeles\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"JM\",\"countryName\":\"Jamaica\",\"currency\":{\"currencyCode\":\"USD\",\"currencySign\":\"$\",\"currencySignPosition\":\"1\",\"decimal\":\".\",\"maskMoneyApplied\":true,\"maxLength\":11,\"precision\":2,\"thousands\":\",\"},\"displayOrder\":31,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"America\/Los_Angeles\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"MQ\",\"countryName\":\"Martinique\",\"currency\":{\"currencyCode\":\"USD\",\"currencySign\":\"$\",\"currencySignPosition\":\"1\",\"decimal\":\".\",\"maskMoneyApplied\":true,\"maxLength\":11,\"precision\":2,\"thousands\":\",\"},\"displayOrder\":32,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"America\/Los_Angeles\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"MS\",\"countryName\":\"Montserrat\",\"currency\":{\"currencyCode\":\"USD\",\"currencySign\":\"$\",\"currencySignPosition\":\"1\",\"decimal\":\".\",\"maskMoneyApplied\":true,\"maxLength\":11,\"precision\":2,\"thousands\":\",\"},\"displayOrder\":33,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"America\/Los_Angeles\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"AN\",\"countryName\":\"Nederlandse Antillen\",\"currency\":{\"currencyCode\":\"USD\",\"currencySign\":\"$\",\"currencySignPosition\":\"1\",\"decimal\":\".\",\"maskMoneyApplied\":true,\"maxLength\":11,\"precision\":2,\"thousands\":\",\"},\"displayOrder\":34,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"America\/Los_Angeles\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"NI\",\"countryName\":\"Nicaragua\",\"currency\":{\"currencyCode\":\"USD\",\"currencySign\":\"$\",\"currencySignPosition\":\"1\",\"decimal\":\".\",\"maskMoneyApplied\":true,\"maxLength\":11,\"precision\":2,\"thousands\":\",\"},\"displayOrder\":35,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"America\/Los_Angeles\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"PA\",\"countryName\":\"Panama\",\"currency\":{\"currencyCode\":\"USD\",\"currencySign\":\"$\",\"currencySignPosition\":\"1\",\"decimal\":\".\",\"maskMoneyApplied\":true,\"maxLength\":11,\"precision\":2,\"thousands\":\",\"},\"displayOrder\":36,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"America\/Los_Angeles\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"PY\",\"countryName\":\"Paraguay\",\"currency\":{\"currencyCode\":\"USD\",\"currencySign\":\"$\",\"currencySignPosition\":\"1\",\"decimal\":\".\",\"maskMoneyApplied\":true,\"maxLength\":11,\"precision\":2,\"thousands\":\",\"},\"displayOrder\":37,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"America\/Los_Angeles\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"PE\",\"countryName\":\"Peru\",\"currency\":{\"currencyCode\":\"PEN\",\"currencySign\":\"S\/\",\"currencySignPosition\":\"1\",\"decimal\":\".\",\"maskMoneyApplied\":true,\"maxLength\":13,\"precision\":2,\"thousands\":\",\"},\"displayOrder\":38,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"America\/Los_Angeles\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"KN\",\"countryName\":\"Saint Christopher and Nevis\",\"currency\":{\"currencyCode\":\"USD\",\"currencySign\":\"$\",\"currencySignPosition\":\"1\",\"decimal\":\".\",\"maskMoneyApplied\":true,\"maxLength\":11,\"precision\":2,\"thousands\":\",\"},\"displayOrder\":39,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"America\/Los_Angeles\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"LC\",\"countryName\":\"Saint Lucia\",\"currency\":{\"currencyCode\":\"USD\",\"currencySign\":\"$\",\"currencySignPosition\":\"1\",\"decimal\":\".\",\"maskMoneyApplied\":true,\"maxLength\":11,\"precision\":2,\"thousands\":\",\"},\"displayOrder\":40,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"America\/Los_Angeles\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"VC\",\"countryName\":\"Saint Vincent and the Grenadines\",\"currency\":{\"currencyCode\":\"USD\",\"currencySign\":\"$\",\"currencySignPosition\":\"1\",\"decimal\":\".\",\"maskMoneyApplied\":true,\"maxLength\":11,\"precision\":2,\"thousands\":\",\"},\"displayOrder\":41,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"America\/Los_Angeles\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"SR\",\"countryName\":\"Republic of Suriname\",\"currency\":{\"currencyCode\":\"USD\",\"currencySign\":\"$\",\"currencySignPosition\":\"1\",\"decimal\":\".\",\"maskMoneyApplied\":true,\"maxLength\":11,\"precision\":2,\"thousands\":\",\"},\"displayOrder\":42,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"America\/Los_Angeles\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"TT\",\"countryName\":\"Republic of Trinidad and Tobago\",\"currency\":{\"currencyCode\":\"USD\",\"currencySign\":\"$\",\"currencySignPosition\":\"1\",\"decimal\":\".\",\"maskMoneyApplied\":true,\"maxLength\":11,\"precision\":2,\"thousands\":\",\"},\"displayOrder\":43,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"America\/Los_Angeles\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"TC\",\"countryName\":\"Turks and Caicos Islands\",\"currency\":{\"currencyCode\":\"USD\",\"currencySign\":\"$\",\"currencySignPosition\":\"1\",\"decimal\":\".\",\"maskMoneyApplied\":true,\"maxLength\":11,\"precision\":2,\"thousands\":\",\"},\"displayOrder\":44,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"America\/Los_Angeles\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"UY\",\"countryName\":\"Uruguay\",\"currency\":{\"currencyCode\":\"USD\",\"currencySign\":\"$\",\"currencySignPosition\":\"1\",\"decimal\":\".\",\"maskMoneyApplied\":true,\"maxLength\":11,\"precision\":2,\"thousands\":\",\"},\"displayOrder\":45,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"America\/Los_Angeles\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"VI\",\"countryName\":\"Virgin Islands of the United States\",\"currency\":{\"currencyCode\":\"USD\",\"currencySign\":\"$\",\"currencySignPosition\":\"1\",\"decimal\":\".\",\"maskMoneyApplied\":true,\"maxLength\":11,\"precision\":2,\"thousands\":\",\"},\"displayOrder\":46,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"America\/Los_Angeles\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"VE\",\"countryName\":\"Venezuela\",\"currency\":{\"currencyCode\":\"USD\",\"currencySign\":\"$\",\"currencySignPosition\":\"1\",\"decimal\":\".\",\"maskMoneyApplied\":true,\"maxLength\":11,\"precision\":2,\"thousands\":\",\"},\"displayOrder\":47,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"America\/Los_Angeles\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"SG\",\"countryName\":\"Republic of Singapore\",\"currency\":{\"currencyCode\":\"USD\",\"currencySign\":\"$\",\"currencySignPosition\":\"1\",\"decimal\":\".\",\"maskMoneyApplied\":true,\"maxLength\":11,\"precision\":2,\"thousands\":\",\"},\"displayOrder\":48,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"America\/Los_Angeles\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"MY\",\"countryName\":\"Malaysia\",\"currency\":{\"currencyCode\":\"USD\",\"currencySign\":\"$\",\"currencySignPosition\":\"1\",\"decimal\":\".\",\"maskMoneyApplied\":true,\"maxLength\":11,\"precision\":2,\"thousands\":\",\"},\"displayOrder\":49,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"America\/Los_Angeles\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"AE\",\"countryName\":\"United Arab Emirates\",\"currency\":{\"currencyCode\":\"USD\",\"currencySign\":\"$\",\"currencySignPosition\":\"1\",\"decimal\":\".\",\"maskMoneyApplied\":true,\"maxLength\":11,\"precision\":2,\"thousands\":\",\"},\"displayOrder\":50,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"America\/Los_Angeles\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"SA\",\"countryName\":\"Kingdom of Saudi Arabia\",\"currency\":{\"currencyCode\":\"USD\",\"currencySign\":\"$\",\"currencySignPosition\":\"1\",\"decimal\":\".\",\"maskMoneyApplied\":true,\"maxLength\":11,\"precision\":2,\"thousands\":\",\"},\"displayOrder\":51,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"America\/Los_Angeles\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"AL\",\"countryName\":\"Republic of Albania\",\"currency\":{\"currencyCode\":\"DEF\",\"currencySign\":\"\",\"currencySignPosition\":\"0\",\"decimal\":\"\",\"maskMoneyApplied\":true,\"maxLength\":7,\"precision\":0,\"thousands\":\",\"},\"displayOrder\":52,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"UTC\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"AT\",\"countryName\":\"Austria\",\"currency\":{\"currencyCode\":\"EUR\",\"currencySign\":\"\u20AC\",\"currencySignPosition\":\"1\",\"decimal\":\",\",\"maskMoneyApplied\":true,\"maxLength\":11,\"precision\":2,\"thousands\":\".\"},\"displayOrder\":53,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"Europe\/Vienna\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"BE\",\"countryName\":\"Belgium\",\"currency\":{\"currencyCode\":\"EUR\",\"currencySign\":\"\u20AC\",\"currencySignPosition\":\"1\",\"decimal\":\",\",\"maskMoneyApplied\":true,\"maxLength\":11,\"precision\":2,\"thousands\":\" \"},\"displayOrder\":54,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"Europe\/Brussels\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"BA\",\"countryName\":\"Bosnia and Herzegovina\",\"currency\":{\"currencyCode\":\"DEF\",\"currencySign\":\"\",\"currencySignPosition\":\"0\",\"decimal\":\"\",\"maskMoneyApplied\":true,\"maxLength\":7,\"precision\":0,\"thousands\":\",\"},\"displayOrder\":55,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"UTC\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"BW\",\"countryName\":\"Republic of Botswana\",\"currency\":{\"currencyCode\":\"DEF\",\"currencySign\":\"\",\"currencySignPosition\":\"0\",\"decimal\":\"\",\"maskMoneyApplied\":true,\"maxLength\":7,\"precision\":0,\"thousands\":\",\"},\"displayOrder\":56,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"UTC\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"BG\",\"countryName\":\"Bulgaria\",\"currency\":{\"currencyCode\":\"EUR\",\"currencySign\":\"\u20AC\",\"currencySignPosition\":\"1\",\"decimal\":\",\",\"maskMoneyApplied\":true,\"maxLength\":11,\"precision\":2,\"thousands\":\" \"},\"displayOrder\":57,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"Europe\/Sofia\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"HR\",\"countryName\":\"Croatia\",\"currency\":{\"currencyCode\":\"EUR\",\"currencySign\":\"\u20AC\",\"currencySignPosition\":\"1\",\"decimal\":\",\",\"maskMoneyApplied\":true,\"maxLength\":11,\"precision\":2,\"thousands\":\" \"},\"displayOrder\":58,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"Europe\/Zagreb\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"CY\",\"countryName\":\"Cyprus\",\"currency\":{\"currencyCode\":\"EUR\",\"currencySign\":\"\u20AC\",\"currencySignPosition\":\"1\",\"decimal\":\",\",\"maskMoneyApplied\":true,\"maxLength\":11,\"precision\":2,\"thousands\":\" \"},\"displayOrder\":59,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"Asia\/Nicosia\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"CZ\",\"countryName\":\"Czech Republic\",\"currency\":{\"currencyCode\":\"CZK\",\"currencySign\":\"K\u010D\",\"currencySignPosition\":\"2\",\"decimal\":\",\",\"maskMoneyApplied\":true,\"maxLength\":12,\"precision\":2,\"thousands\":\" \"},\"displayOrder\":60,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"Europe\/Prague\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"DK\",\"countryName\":\"Denmark\",\"currency\":{\"currencyCode\":\"DKK\",\"currencySign\":\"kr\",\"currencySignPosition\":\"2\",\"decimal\":\",\",\"maskMoneyApplied\":true,\"maxLength\":12,\"precision\":2,\"thousands\":\".\"},\"displayOrder\":61,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"Europe\/Copenhagen\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"EE\",\"countryName\":\"Estonia\",\"currency\":{\"currencyCode\":\"EUR\",\"currencySign\":\"\u20AC\",\"currencySignPosition\":\"1\",\"decimal\":\",\",\"maskMoneyApplied\":true,\"maxLength\":11,\"precision\":2,\"thousands\":\" \"},\"displayOrder\":62,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"Europe\/Tallinn\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"FI\",\"countryName\":\"Finland\",\"currency\":{\"currencyCode\":\"EUR\",\"currencySign\":\"\u20AC\",\"currencySignPosition\":\"1\",\"decimal\":\",\",\"maskMoneyApplied\":true,\"maxLength\":11,\"precision\":2,\"thousands\":\" \"},\"displayOrder\":63,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"Europe\/Helsinki\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"FR\",\"countryName\":\"France\",\"currency\":{\"currencyCode\":\"EUR\",\"currencySign\":\"\u20AC\",\"currencySignPosition\":\"1\",\"decimal\":\",\",\"maskMoneyApplied\":true,\"maxLength\":11,\"precision\":2,\"thousands\":\" \"},\"displayOrder\":64,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"Europe\/Paris\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"DE\",\"countryName\":\"Germany\",\"currency\":{\"currencyCode\":\"EUR\",\"currencySign\":\"\u20AC\",\"currencySignPosition\":\"1\",\"decimal\":\",\",\"maskMoneyApplied\":true,\"maxLength\":11,\"precision\":2,\"thousands\":\".\"},\"displayOrder\":65,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"Europe\/Berlin\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"GR\",\"countryName\":\"Greece\",\"currency\":{\"currencyCode\":\"EUR\",\"currencySign\":\"\u20AC\",\"currencySignPosition\":\"1\",\"decimal\":\",\",\"maskMoneyApplied\":true,\"maxLength\":11,\"precision\":2,\"thousands\":\".\"},\"displayOrder\":66,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"Europe\/Athens\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"HU\",\"countryName\":\"Hungary\",\"currency\":{\"currencyCode\":\"EUR\",\"currencySign\":\"\u20AC\",\"currencySignPosition\":\"1\",\"decimal\":\",\",\"maskMoneyApplied\":true,\"maxLength\":11,\"precision\":2,\"thousands\":\" \"},\"displayOrder\":67,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"Europe\/Budapest\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"IS\",\"countryName\":\"Republic of Iceland\",\"currency\":{\"currencyCode\":\"DEF\",\"currencySign\":\"\",\"currencySignPosition\":\"0\",\"decimal\":\"\",\"maskMoneyApplied\":true,\"maxLength\":7,\"precision\":0,\"thousands\":\",\"},\"displayOrder\":68,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"UTC\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"IE\",\"countryName\":\"Ireland\",\"currency\":{\"currencyCode\":\"EUR\",\"currencySign\":\"\u20AC\",\"currencySignPosition\":\"1\",\"decimal\":\".\",\"maskMoneyApplied\":true,\"maxLength\":11,\"precision\":2,\"thousands\":\",\"},\"displayOrder\":69,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"Europe\/Dublin\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"IT\",\"countryName\":\"Italy\",\"currency\":{\"currencyCode\":\"EUR\",\"currencySign\":\"\u20AC\",\"currencySignPosition\":\"1\",\"decimal\":\",\",\"maskMoneyApplied\":true,\"maxLength\":11,\"precision\":2,\"thousands\":\".\"},\"displayOrder\":70,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"Europe\/Rome\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"LV\",\"countryName\":\"Latvia\",\"currency\":{\"currencyCode\":\"EUR\",\"currencySign\":\"\u20AC\",\"currencySignPosition\":\"1\",\"decimal\":\",\",\"maskMoneyApplied\":true,\"maxLength\":11,\"precision\":2,\"thousands\":\" \"},\"displayOrder\":71,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"Europe\/Riga\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"LS\",\"countryName\":\"Kingdom of Lesotho\",\"currency\":{\"currencyCode\":\"DEF\",\"currencySign\":\"\",\"currencySignPosition\":\"0\",\"decimal\":\"\",\"maskMoneyApplied\":true,\"maxLength\":7,\"precision\":0,\"thousands\":\",\"},\"displayOrder\":72,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"UTC\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"LI\",\"countryName\":\"Principality of Liechtenstein\",\"currency\":{\"currencyCode\":\"DEF\",\"currencySign\":\"\",\"currencySignPosition\":\"0\",\"decimal\":\"\",\"maskMoneyApplied\":true,\"maxLength\":7,\"precision\":0,\"thousands\":\",\"},\"displayOrder\":73,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"UTC\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"LT\",\"countryName\":\"Lithuania\",\"currency\":{\"currencyCode\":\"EUR\",\"currencySign\":\"\u20AC\",\"currencySignPosition\":\"1\",\"decimal\":\",\",\"maskMoneyApplied\":true,\"maxLength\":11,\"precision\":2,\"thousands\":\" \"},\"displayOrder\":74,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"Europe\/Vilnius\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"LU\",\"countryName\":\"Luxembourg\",\"currency\":{\"currencyCode\":\"EUR\",\"currencySign\":\"\u20AC\",\"currencySignPosition\":\"1\",\"decimal\":\",\",\"maskMoneyApplied\":true,\"maxLength\":11,\"precision\":2,\"thousands\":\" \"},\"displayOrder\":75,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"Europe\/Luxembourg\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"MK\",\"countryName\":\"Former Yugoslav Republic of Macedonia\",\"currency\":{\"currencyCode\":\"DEF\",\"currencySign\":\"\",\"currencySignPosition\":\"0\",\"decimal\":\"\",\"maskMoneyApplied\":true,\"maxLength\":7,\"precision\":0,\"thousands\":\",\"},\"displayOrder\":76,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"UTC\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"MT\",\"countryName\":\"Malta\",\"currency\":{\"currencyCode\":\"EUR\",\"currencySign\":\"\u20AC\",\"currencySignPosition\":\"1\",\"decimal\":\",\",\"maskMoneyApplied\":true,\"maxLength\":11,\"precision\":2,\"thousands\":\" \"},\"displayOrder\":77,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"Europe\/Malta\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"ME\",\"countryName\":\"Republic of Montenegro\",\"currency\":{\"currencyCode\":\"DEF\",\"currencySign\":\"\",\"currencySignPosition\":\"0\",\"decimal\":\"\",\"maskMoneyApplied\":true,\"maxLength\":7,\"precision\":0,\"thousands\":\",\"},\"displayOrder\":78,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"UTC\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"MZ\",\"countryName\":\"Republic of Mozambique\",\"currency\":{\"currencyCode\":\"DEF\",\"currencySign\":\"\",\"currencySignPosition\":\"0\",\"decimal\":\"\",\"maskMoneyApplied\":true,\"maxLength\":7,\"precision\":0,\"thousands\":\",\"},\"displayOrder\":79,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"UTC\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"NA\",\"countryName\":\"Republic of Namibia\",\"currency\":{\"currencyCode\":\"DEF\",\"currencySign\":\"\",\"currencySignPosition\":\"0\",\"decimal\":\"\",\"maskMoneyApplied\":true,\"maxLength\":7,\"precision\":0,\"thousands\":\",\"},\"displayOrder\":80,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"UTC\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"NL\",\"countryName\":\"Netherlands\",\"currency\":{\"currencyCode\":\"EUR\",\"currencySign\":\"\u20AC\",\"currencySignPosition\":\"1\",\"decimal\":\",\",\"maskMoneyApplied\":true,\"maxLength\":11,\"precision\":2,\"thousands\":\".\"},\"displayOrder\":81,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"Europe\/Amsterdam\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"NO\",\"countryName\":\"Norway\",\"currency\":{\"currencyCode\":\"NOK\",\"currencySign\":\"kr\",\"currencySignPosition\":\"2\",\"decimal\":\"\",\"maskMoneyApplied\":true,\"maxLength\":8,\"precision\":0,\"thousands\":\"\"},\"displayOrder\":82,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"Europe\/Oslo\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"PL\",\"countryName\":\"Poland\",\"currency\":{\"currencyCode\":\"PLN\",\"currencySign\":\"z\u0142\",\"currencySignPosition\":\"2\",\"decimal\":\",\",\"maskMoneyApplied\":true,\"maxLength\":12,\"precision\":2,\"thousands\":\" \"},\"displayOrder\":83,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"Europe\/Warsaw\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"PT\",\"countryName\":\"Portugal\",\"currency\":{\"currencyCode\":\"EUR\",\"currencySign\":\"\u20AC\",\"currencySignPosition\":\"1\",\"decimal\":\",\",\"maskMoneyApplied\":true,\"maxLength\":11,\"precision\":2,\"thousands\":\".\"},\"displayOrder\":84,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"Europe\/Lisbon\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"RO\",\"countryName\":\"Romania\",\"currency\":{\"currencyCode\":\"EUR\",\"currencySign\":\"\u20AC\",\"currencySignPosition\":\"1\",\"decimal\":\",\",\"maskMoneyApplied\":true,\"maxLength\":11,\"precision\":2,\"thousands\":\" \"},\"displayOrder\":85,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"Europe\/Bucharest\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"RU\",\"countryName\":\"Russia\",\"currency\":{\"currencyCode\":\"RUB\",\"currencySign\":\"\u0440.\",\"currencySignPosition\":\"2\",\"decimal\":\"\",\"maskMoneyApplied\":true,\"maxLength\":8,\"precision\":0,\"thousands\":\"\"},\"displayOrder\":86,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"Europe\/Moscow\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"RS\",\"countryName\":\"Republic of Serbia \u0026 Republic of Kosovo\",\"currency\":{\"currencyCode\":\"DEF\",\"currencySign\":\"\",\"currencySignPosition\":\"0\",\"decimal\":\"\",\"maskMoneyApplied\":true,\"maxLength\":7,\"precision\":0,\"thousands\":\",\"},\"displayOrder\":87,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"UTC\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"SK\",\"countryName\":\"Slovak Republic\",\"currency\":{\"currencyCode\":\"EUR\",\"currencySign\":\"\u20AC\",\"currencySignPosition\":\"1\",\"decimal\":\",\",\"maskMoneyApplied\":true,\"maxLength\":11,\"precision\":2,\"thousands\":\" \"},\"displayOrder\":88,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"Europe\/Bratislava\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"SI\",\"countryName\":\"Slovenia\",\"currency\":{\"currencyCode\":\"EUR\",\"currencySign\":\"\u20AC\",\"currencySignPosition\":\"1\",\"decimal\":\",\",\"maskMoneyApplied\":true,\"maxLength\":11,\"precision\":2,\"thousands\":\" \"},\"displayOrder\":89,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"Europe\/Ljubljana\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"ZA\",\"countryName\":\"South Africa\",\"currency\":{\"currencyCode\":\"ZAR\",\"currencySign\":\"R$\",\"currencySignPosition\":\"1\",\"decimal\":\".\",\"maskMoneyApplied\":true,\"maxLength\":12,\"precision\":2,\"thousands\":\",\"},\"displayOrder\":90,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"Africa\/Johannesburg\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"ES\",\"countryName\":\"Spain\",\"currency\":{\"currencyCode\":\"EUR\",\"currencySign\":\"\u20AC\",\"currencySignPosition\":\"1\",\"decimal\":\",\",\"maskMoneyApplied\":true,\"maxLength\":10,\"precision\":2,\"thousands\":\"\"},\"displayOrder\":91,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"Europe\/Madrid\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"SZ\",\"countryName\":\"Kingdom of Swaziland\",\"currency\":{\"currencyCode\":\"DEF\",\"currencySign\":\"\",\"currencySignPosition\":\"0\",\"decimal\":\"\",\"maskMoneyApplied\":true,\"maxLength\":7,\"precision\":0,\"thousands\":\",\"},\"displayOrder\":92,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"UTC\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"SE\",\"countryName\":\"Sweden\",\"currency\":{\"currencyCode\":\"SEK\",\"currencySign\":\"kr\",\"currencySignPosition\":\"2\",\"decimal\":\"\",\"maskMoneyApplied\":true,\"maxLength\":8,\"precision\":0,\"thousands\":\"\"},\"displayOrder\":93,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"Europe\/Stockholm\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"CH\",\"countryName\":\"Switzerland\",\"currency\":{\"currencyCode\":\"CHF\",\"currencySign\":\"S\u20A3\",\"currencySignPosition\":\"2\",\"decimal\":\".\",\"maskMoneyApplied\":true,\"maxLength\":12,\"precision\":2,\"thousands\":\" \"},\"displayOrder\":94,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"Europe\/Zurich\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"GB\",\"countryName\":\"United Kingdom\",\"currency\":{\"currencyCode\":\"GBP\",\"currencySign\":\"\u00A3\",\"currencySignPosition\":\"1\",\"decimal\":\".\",\"maskMoneyApplied\":true,\"maxLength\":11,\"precision\":2,\"thousands\":\",\"},\"displayOrder\":95,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"Europe\/London\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"AU\",\"countryName\":\"Australia\",\"currency\":{\"currencyCode\":\"AUD\",\"currencySign\":\"$\",\"currencySignPosition\":\"1\",\"decimal\":\".\",\"maskMoneyApplied\":true,\"maxLength\":11,\"precision\":2,\"thousands\":\",\"},\"displayOrder\":96,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"Australia\/Melbourne\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"NZ\",\"countryName\":\"New Zealand\",\"currency\":{\"currencyCode\":\"NZD\",\"currencySign\":\"$\",\"currencySignPosition\":\"1\",\"decimal\":\".\",\"maskMoneyApplied\":true,\"maxLength\":11,\"precision\":2,\"thousands\":\",\"},\"displayOrder\":97,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"Pacific\/Auckland\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"TR\",\"countryName\":\"Turkey\",\"currency\":{\"currencyCode\":\"DEF\",\"currencySign\":\"\",\"currencySignPosition\":\"0\",\"decimal\":\",\",\"maskMoneyApplied\":true,\"maxLength\":10,\"precision\":2,\"thousands\":\" \"},\"displayOrder\":98,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"Europe\/Istanbul\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"ZM\",\"countryName\":\"Republic of Zambia\",\"currency\":{\"currencyCode\":\"DEF\",\"currencySign\":\"\",\"currencySignPosition\":\"0\",\"decimal\":\"\",\"maskMoneyApplied\":true,\"maxLength\":7,\"precision\":0,\"thousands\":\",\"},\"displayOrder\":99,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"UTC\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"ZW\",\"countryName\":\"Republic of Zimbabwe\",\"currency\":{\"currencyCode\":\"DEF\",\"currencySign\":\"\",\"currencySignPosition\":\"0\",\"decimal\":\"\",\"maskMoneyApplied\":true,\"maxLength\":7,\"precision\":0,\"thousands\":\",\"},\"displayOrder\":100,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"UTC\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"AZ\",\"countryName\":\"Azerbaijan\",\"currency\":{\"currencyCode\":\"DEF\",\"currencySign\":\"\",\"currencySignPosition\":\"0\",\"decimal\":\"\",\"maskMoneyApplied\":true,\"maxLength\":7,\"precision\":0,\"thousands\":\",\"},\"displayOrder\":101,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"UTC\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"MR\",\"countryName\":\"Mauritania\",\"currency\":{\"currencyCode\":\"DEF\",\"currencySign\":\"\",\"currencySignPosition\":\"0\",\"decimal\":\"\",\"maskMoneyApplied\":true,\"maxLength\":7,\"precision\":0,\"thousands\":\",\"},\"displayOrder\":102,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"UTC\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"ML\",\"countryName\":\"Mali\",\"currency\":{\"currencyCode\":\"DEF\",\"currencySign\":\"\",\"currencySignPosition\":\"0\",\"decimal\":\"\",\"maskMoneyApplied\":true,\"maxLength\":7,\"precision\":0,\"thousands\":\",\"},\"displayOrder\":103,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"UTC\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"NE\",\"countryName\":\"Niger\",\"currency\":{\"currencyCode\":\"DEF\",\"currencySign\":\"\",\"currencySignPosition\":\"0\",\"decimal\":\"\",\"maskMoneyApplied\":true,\"maxLength\":7,\"precision\":0,\"thousands\":\",\"},\"displayOrder\":104,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"UTC\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"TD\",\"countryName\":\"Chad\",\"currency\":{\"currencyCode\":\"DEF\",\"currencySign\":\"\",\"currencySignPosition\":\"0\",\"decimal\":\"\",\"maskMoneyApplied\":true,\"maxLength\":7,\"precision\":0,\"thousands\":\",\"},\"displayOrder\":105,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"UTC\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"SD\",\"countryName\":\"Sudan\",\"currency\":{\"currencyCode\":\"DEF\",\"currencySign\":\"\",\"currencySignPosition\":\"0\",\"decimal\":\"\",\"maskMoneyApplied\":true,\"maxLength\":7,\"precision\":0,\"thousands\":\",\"},\"displayOrder\":106,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"UTC\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"ER\",\"countryName\":\"Eritrea\",\"currency\":{\"currencyCode\":\"DEF\",\"currencySign\":\"\",\"currencySignPosition\":\"0\",\"decimal\":\"\",\"maskMoneyApplied\":true,\"maxLength\":7,\"precision\":0,\"thousands\":\",\"},\"displayOrder\":107,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"UTC\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"DJ\",\"countryName\":\"Djibouti\",\"currency\":{\"currencyCode\":\"DEF\",\"currencySign\":\"\",\"currencySignPosition\":\"0\",\"decimal\":\"\",\"maskMoneyApplied\":true,\"maxLength\":7,\"precision\":0,\"thousands\":\",\"},\"displayOrder\":108,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"UTC\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"SO\",\"countryName\":\"Somalia\",\"currency\":{\"currencyCode\":\"DEF\",\"currencySign\":\"\",\"currencySignPosition\":\"0\",\"decimal\":\"\",\"maskMoneyApplied\":true,\"maxLength\":7,\"precision\":0,\"thousands\":\",\"},\"displayOrder\":109,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"UTC\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"AD\",\"countryName\":\"Andorra\",\"currency\":{\"currencyCode\":\"DEF\",\"currencySign\":\"\",\"currencySignPosition\":\"0\",\"decimal\":\"\",\"maskMoneyApplied\":true,\"maxLength\":7,\"precision\":0,\"thousands\":\",\"},\"displayOrder\":110,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"UTC\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"GI\",\"countryName\":\"Gibraltar\",\"currency\":{\"currencyCode\":\"DEF\",\"currencySign\":\"\",\"currencySignPosition\":\"0\",\"decimal\":\"\",\"maskMoneyApplied\":true,\"maxLength\":7,\"precision\":0,\"thousands\":\",\"},\"displayOrder\":111,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"UTC\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"GG\",\"countryName\":\"Guernsey\",\"currency\":{\"currencyCode\":\"DEF\",\"currencySign\":\"\",\"currencySignPosition\":\"0\",\"decimal\":\"\",\"maskMoneyApplied\":true,\"maxLength\":7,\"precision\":0,\"thousands\":\",\"},\"displayOrder\":112,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"UTC\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"IM\",\"countryName\":\"Isle of Man\",\"currency\":{\"currencyCode\":\"DEF\",\"currencySign\":\"\",\"currencySignPosition\":\"0\",\"decimal\":\"\",\"maskMoneyApplied\":true,\"maxLength\":7,\"precision\":0,\"thousands\":\",\"},\"displayOrder\":113,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"UTC\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"JE\",\"countryName\":\"Jersey\",\"currency\":{\"currencyCode\":\"DEF\",\"currencySign\":\"\",\"currencySignPosition\":\"0\",\"decimal\":\"\",\"maskMoneyApplied\":true,\"maxLength\":7,\"precision\":0,\"thousands\":\",\"},\"displayOrder\":114,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"UTC\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"MC\",\"countryName\":\"Monaco\",\"currency\":{\"currencyCode\":\"DEF\",\"currencySign\":\"\",\"currencySignPosition\":\"0\",\"decimal\":\"\",\"maskMoneyApplied\":true,\"maxLength\":7,\"precision\":0,\"thousands\":\",\"},\"displayOrder\":115,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"UTC\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"IN\",\"countryName\":\"India\",\"currency\":{\"currencyCode\":\"DEF\",\"currencySign\":\"\",\"currencySignPosition\":\"0\",\"decimal\":\"\",\"maskMoneyApplied\":true,\"maxLength\":7,\"precision\":0,\"thousands\":\",\"},\"displayOrder\":116,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"UTC\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"SM\",\"countryName\":\"San Marino\",\"currency\":{\"currencyCode\":\"DEF\",\"currencySign\":\"\",\"currencySignPosition\":\"0\",\"decimal\":\"\",\"maskMoneyApplied\":true,\"maxLength\":7,\"precision\":0,\"thousands\":\",\"},\"displayOrder\":117,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"UTC\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"VA\",\"countryName\":\"Vatican City\",\"currency\":{\"currencyCode\":\"DEF\",\"currencySign\":\"\",\"currencySignPosition\":\"0\",\"decimal\":\"\",\"maskMoneyApplied\":true,\"maxLength\":7,\"precision\":0,\"thousands\":\",\"},\"displayOrder\":118,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"UTC\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"KR\",\"countryName\":\"Korea\",\"currency\":{\"currencyCode\":\"KRW\",\"currencySign\":\"\u20A9\",\"currencySignPosition\":\"1\",\"decimal\":\"\",\"maskMoneyApplied\":true,\"maxLength\":9,\"precision\":0,\"thousands\":\",\"},\"displayOrder\":119,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"Asia\/Seoul\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"TW\",\"countryName\":\"Taiwan\",\"currency\":{\"currencyCode\":\"TWD\",\"currencySign\":\"NT$\",\"currencySignPosition\":\"1\",\"decimal\":\"\",\"maskMoneyApplied\":true,\"maxLength\":10,\"precision\":0,\"thousands\":\",\"},\"displayOrder\":120,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"Asia\/Taipei\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"HK\",\"countryName\":\"Hong Kong\",\"currency\":{\"currencyCode\":\"HKD\",\"currencySign\":\"HK$\",\"currencySignPosition\":\"1\",\"decimal\":\"\",\"maskMoneyApplied\":true,\"maxLength\":10,\"precision\":0,\"thousands\":\",\"},\"displayOrder\":121,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"Asia\/Hong_Kong\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"CN\",\"countryName\":\"China\",\"currency\":{\"currencyCode\":\"CNY\",\"currencySign\":\"\u5143\",\"currencySignPosition\":\"2\",\"decimal\":\".\",\"maskMoneyApplied\":true,\"maxLength\":10,\"precision\":2,\"thousands\":\"\"},\"displayOrder\":122,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"Asia\/Shanghai\"},{\"closedCountryAndHasPublicPrice\":false,\"countryCode\":\"IL\",\"countryName\":\"State of Israel\",\"currency\":{\"currencyCode\":\"EUR\",\"currencySign\":\"\u20AC\",\"currencySignPosition\":\"1\",\"decimal\":\",\",\"maskMoneyApplied\":true,\"maxLength\":11,\"precision\":2,\"thousands\":\",\"},\"displayOrder\":123,\"ecardOnlySalesCountry\":false,\"releaseTargetCountry\":false,\"taxIncluded\":false,\"timezoneName\":\"Israel\"}]";
    }

    function getCurrencyInfoListJson() {
        return null;
    }

    function getDeriveDescriptionJson() {
        return "[{\"content\":\"Save on this title through MM\/dd\/yyyy, HH:59 PT\",\"deviceTypeId\":5,\"langCode\":\"en_US\"},{\"content\":\"\u00C9conomisez sur ce titre jusqu'au dd-MM-yyyy, HH:59 (HP)\",\"deviceTypeId\":5,\"langCode\":\"fr_US\"},{\"content\":\"Ahorra en este t\u00EDtulo hasta dd\/MM\/yyyy, HH:59 (horario del Pac\u00EDfico)\",\"deviceTypeId\":5,\"langCode\":\"es_US\"},{\"content\":\"yyyy\/MM\/dd HH:59:59\u307E\u3067\u3001\u304A\u6C42\u3081\u3084\u3059\u3044\u4FA1\u683C\u3067\u8CFC\u5165\u3044\u305F\u3060\u3051\u307E\u3059\u3002\",\"deviceTypeId\":4,\"langCode\":\"ja\"},{\"content\":\"yyyy\/MM\/dd HH:59:59\u307E\u3067\u3001\u304A\u6C42\u3081\u3084\u3059\u3044\u4FA1\u683C\u3067\u8CFC\u5165\u3044\u305F\u3060\u3051\u307E\u3059\u3002\",\"deviceTypeId\":5,\"langCode\":\"ja\"}]";
    }

    /** AjaxでChangesetIdを更新する */
    var urlAjaxUpdateChangesetId = "\/ncms3\/temporaryDiscount\/discountInfo\/ajaxUpdateChangesetId";

    /** Ajaxで割引を削除する */
    var urlAjaxDelete = "\/ncms3\/temporaryDiscount\/discountInfo\/ajaxDelete";

    /** ディカウントグループID存在チェック */
    var urlAjaxExistsDiscountGroup = "\/ncms3\/temporaryDiscount\/setup\/ajaxExistsDiscountGroup";

    /** サーバーから渡されたデフォルトの開始時刻を返す*/
    function getDefaultStartHour() {
        return "15";
    }

    /** 登録したディスカウント情報を渡す*/
    function getDiscountContentList() {
        return [{ "discountCountries": [{ "countryId": null, "countryCode": "AT", "countryName": null, "discountId": null, "priceId": null, "startDatetime": null, "endDatetime": null, "regularValue": null, "discountValue": null, "discountedValue": null }, { "countryId": null, "countryCode": "BE", "countryName": null, "discountId": null, "priceId": null, "startDatetime": null, "endDatetime": null, "regularValue": null, "discountValue": null, "discountedValue": null }], "primaryKey": 33809, "nsUid": "70010000020724", "titleId": null, "contentType": "TITLE", "contentTypeName": "Title", "regionName": "Europe\/Australia", "contentName": "SUPERHOT", "productCode": "HAC-P-AURNA", "lastEditedDate": "02\/07\/2021 15:18:30 (CEST)", "lastEditedDateForSort": "2021-07-02 13:18:30", "status": "Approved", "platFormName": "Nintendo Switch downloadable software", "publisherName": "SUPERHOT(SUPERHOT)", "onlinePublisherId": 11094, "deviceTypeId": 6, "statExclusionFlag": null, "onlinePriceChangesetId": null, "contentTypeId": { "present": true } }, { "discountCountries": [{ "countryId": null, "countryCode": "AT", "countryName": null, "discountId": null, "priceId": null, "startDatetime": null, "endDatetime": null, "regularValue": null, "discountValue": null, "discountedValue": null }, { "countryId": null, "countryCode": "BE", "countryName": null, "discountId": null, "priceId": null, "startDatetime": null, "endDatetime": null, "regularValue": null, "discountValue": null, "discountedValue": null }], "primaryKey": 33810, "nsUid": "70010000020725", "titleId": null, "contentType": "TITLE", "contentTypeName": "Title", "regionName": "Europe\/Australia", "contentName": "SUPERHOT 2", "productCode": "HAC-P-TRINA", "lastEditedDate": "02\/07\/2021 15:18:30 (CEST)", "lastEditedDateForSort": "2021-07-02 13:18:30", "status": "Approved", "platFormName": "Nintendo Switch downloadable software", "publisherName": "SUPERHOT(SUPERHOT)", "onlinePublisherId": 11094, "deviceTypeId": 6, "statExclusionFlag": null, "onlinePriceChangesetId": null, "contentTypeId": { "present": true } }];
    }

    /** 新規登録かどうかの値を返す*/
    function isSetupMode() {
        return false;
    }

    /** 表示用タイムゾーンのマップ（適用期間用）*/
    function getTimeZoneDisplayNameMap() {
        return { "1": "JST", "2": "PST\/PDT", "3": "CET\/CEST", "5": "KST", "6": "CST", "7": "HKT", "10": "JST" };
    }

    /** 開始日時のマップ（適用期間用）*/
    function getDefaultHourOnStartDatetimeMap() {
        return { "1": "00", "2": "09", "3": "15", "5": "00", "6": "00", "7": "00", "10": "00" };
    }

    /** 終了日時のマップ（適用期間用）*/
    function getDefaultHourOnEndDatetimeMap() {
        return { "1": "23", "2": "23", "3": "23", "5": "23", "6": null, "7": "23", "10": null };
    }

    /** PMS確認用URL*/
    function getPmsConfirmUrl() {
        return "https:\/\/pms.wc.eshop.nintendo.net\/pms";
    }

    var addButtonText = "Add";
    var removeButtonText = "Remove";

    /** ガイドラインPDFファイル作成用URL */
    var urlGuidelineDownloadLink = "\/ncms3\/discount\/guidelinePdfDownload";

    /** NOE の最低割引率のデフォルト値 */
    var defaultMinimumDiscountEur = 10;

    /** 割引設定値 */
    function getDiscountSettingProperty() {
        return { "targetSize": 100, "condSizeWUPandCTR": 5, "condSizeHAC": 50, "validation": { "countPerPageFromSmt": 20 }, "regionalSettingList": [{ "region": "JPN", "maxApplicationPeriodDays": 27, "coolingOffPeriodDays": 28, "discountStopHoursFromReleaseDate": 672, "deadlineSetting": { "termDiscount": { "request": { "hours": 48, "isCompareByDays": false, "isValidateBeforeStartDatetime": true }, "editStartDatetime": { "hours": 48, "isCompareByDays": false, "isValidateBeforeStartDatetime": true }, "editEndDatetime": { "hours": 48, "isCompareByDays": false, "isValidateBeforeStartDatetime": true }, "delete": { "hours": 48, "isCompareByDays": false, "isValidateBeforeStartDatetime": true } }, "ownerDiscount": { "request": { "hours": null, "isCompareByDays": null, "isValidateBeforeStartDatetime": null }, "editStartDatetime": { "hours": null, "isCompareByDays": null, "isValidateBeforeStartDatetime": null }, "editEndDatetime": { "hours": null, "isCompareByDays": null, "isValidateBeforeStartDatetime": null }, "delete": { "hours": null, "isCompareByDays": null, "isValidateBeforeStartDatetime": null } }, "membershipDiscount": { "request": { "hours": null, "isCompareByDays": null, "isValidateBeforeStartDatetime": null }, "editStartDatetime": { "hours": null, "isCompareByDays": null, "isValidateBeforeStartDatetime": null }, "editEndDatetime": { "hours": null, "isCompareByDays": null, "isValidateBeforeStartDatetime": null }, "delete": { "hours": null, "isCompareByDays": null, "isValidateBeforeStartDatetime": null } } } }, { "region": "USA", "maxApplicationPeriodDays": 21, "coolingOffPeriodDays": null, "discountStopHoursFromReleaseDate": 672, "deadlineSetting": { "termDiscount": { "request": { "hours": null, "isCompareByDays": null, "isValidateBeforeStartDatetime": null }, "editStartDatetime": { "hours": 0, "isCompareByDays": false, "isValidateBeforeStartDatetime": true }, "editEndDatetime": { "hours": 0, "isCompareByDays": false, "isValidateBeforeStartDatetime": false }, "delete": { "hours": 48, "isCompareByDays": false, "isValidateBeforeStartDatetime": true } }, "ownerDiscount": { "request": { "hours": null, "isCompareByDays": null, "isValidateBeforeStartDatetime": null }, "editStartDatetime": { "hours": null, "isCompareByDays": null, "isValidateBeforeStartDatetime": null }, "editEndDatetime": { "hours": null, "isCompareByDays": null, "isValidateBeforeStartDatetime": null }, "delete": { "hours": null, "isCompareByDays": null, "isValidateBeforeStartDatetime": null } }, "membershipDiscount": { "request": { "hours": null, "isCompareByDays": null, "isValidateBeforeStartDatetime": null }, "editStartDatetime": { "hours": null, "isCompareByDays": null, "isValidateBeforeStartDatetime": null }, "editEndDatetime": { "hours": null, "isCompareByDays": null, "isValidateBeforeStartDatetime": null }, "delete": { "hours": null, "isCompareByDays": null, "isValidateBeforeStartDatetime": null } } } }, { "region": "EUR", "maxApplicationPeriodDays": 31, "coolingOffPeriodDays": 27, "discountStopHoursFromReleaseDate": 672, "deadlineSetting": { "termDiscount": { "request": { "hours": null, "isCompareByDays": null, "isValidateBeforeStartDatetime": null }, "editStartDatetime": { "hours": 48, "isCompareByDays": false, "isValidateBeforeStartDatetime": true }, "editEndDatetime": { "hours": 48, "isCompareByDays": false, "isValidateBeforeStartDatetime": true }, "delete": { "hours": 48, "isCompareByDays": false, "isValidateBeforeStartDatetime": true } }, "ownerDiscount": { "request": { "hours": null, "isCompareByDays": null, "isValidateBeforeStartDatetime": null }, "editStartDatetime": { "hours": null, "isCompareByDays": null, "isValidateBeforeStartDatetime": null }, "editEndDatetime": { "hours": null, "isCompareByDays": null, "isValidateBeforeStartDatetime": null }, "delete": { "hours": null, "isCompareByDays": null, "isValidateBeforeStartDatetime": null } }, "membershipDiscount": { "request": { "hours": null, "isCompareByDays": null, "isValidateBeforeStartDatetime": null }, "editStartDatetime": { "hours": null, "isCompareByDays": null, "isValidateBeforeStartDatetime": null }, "editEndDatetime": { "hours": null, "isCompareByDays": null, "isValidateBeforeStartDatetime": null }, "delete": { "hours": null, "isCompareByDays": null, "isValidateBeforeStartDatetime": null } } } }, { "region": "KOR", "maxApplicationPeriodDays": 27, "coolingOffPeriodDays": 28, "discountStopHoursFromReleaseDate": 672, "deadlineSetting": { "termDiscount": { "request": { "hours": 48, "isCompareByDays": false, "isValidateBeforeStartDatetime": true }, "editStartDatetime": { "hours": 48, "isCompareByDays": false, "isValidateBeforeStartDatetime": true }, "editEndDatetime": { "hours": 48, "isCompareByDays": false, "isValidateBeforeStartDatetime": true }, "delete": { "hours": 48, "isCompareByDays": false, "isValidateBeforeStartDatetime": true } }, "ownerDiscount": { "request": { "hours": null, "isCompareByDays": null, "isValidateBeforeStartDatetime": null }, "editStartDatetime": { "hours": null, "isCompareByDays": null, "isValidateBeforeStartDatetime": null }, "editEndDatetime": { "hours": null, "isCompareByDays": null, "isValidateBeforeStartDatetime": null }, "delete": { "hours": null, "isCompareByDays": null, "isValidateBeforeStartDatetime": null } }, "membershipDiscount": { "request": { "hours": null, "isCompareByDays": null, "isValidateBeforeStartDatetime": null }, "editStartDatetime": { "hours": null, "isCompareByDays": null, "isValidateBeforeStartDatetime": null }, "editEndDatetime": { "hours": null, "isCompareByDays": null, "isValidateBeforeStartDatetime": null }, "delete": { "hours": null, "isCompareByDays": null, "isValidateBeforeStartDatetime": null } } } }, { "region": "TWN", "maxApplicationPeriodDays": 27, "coolingOffPeriodDays": 28, "discountStopHoursFromReleaseDate": 672, "deadlineSetting": { "termDiscount": { "request": { "hours": 48, "isCompareByDays": false, "isValidateBeforeStartDatetime": true }, "editStartDatetime": { "hours": 48, "isCompareByDays": false, "isValidateBeforeStartDatetime": true }, "editEndDatetime": { "hours": 48, "isCompareByDays": false, "isValidateBeforeStartDatetime": true }, "delete": { "hours": 48, "isCompareByDays": false, "isValidateBeforeStartDatetime": true } }, "ownerDiscount": { "request": { "hours": null, "isCompareByDays": null, "isValidateBeforeStartDatetime": null }, "editStartDatetime": { "hours": null, "isCompareByDays": null, "isValidateBeforeStartDatetime": null }, "editEndDatetime": { "hours": null, "isCompareByDays": null, "isValidateBeforeStartDatetime": null }, "delete": { "hours": null, "isCompareByDays": null, "isValidateBeforeStartDatetime": null } }, "membershipDiscount": { "request": { "hours": null, "isCompareByDays": null, "isValidateBeforeStartDatetime": null }, "editStartDatetime": { "hours": null, "isCompareByDays": null, "isValidateBeforeStartDatetime": null }, "editEndDatetime": { "hours": null, "isCompareByDays": null, "isValidateBeforeStartDatetime": null }, "delete": { "hours": null, "isCompareByDays": null, "isValidateBeforeStartDatetime": null } } } }, { "region": "CHN", "maxApplicationPeriodDays": null, "coolingOffPeriodDays": null, "discountStopHoursFromReleaseDate": 672, "deadlineSetting": { "termDiscount": { "request": { "hours": null, "isCompareByDays": null, "isValidateBeforeStartDatetime": null }, "editStartDatetime": { "hours": null, "isCompareByDays": null, "isValidateBeforeStartDatetime": null }, "editEndDatetime": { "hours": null, "isCompareByDays": null, "isValidateBeforeStartDatetime": null }, "delete": { "hours": 48, "isCompareByDays": false, "isValidateBeforeStartDatetime": true } }, "ownerDiscount": { "request": { "hours": null, "isCompareByDays": null, "isValidateBeforeStartDatetime": null }, "editStartDatetime": { "hours": null, "isCompareByDays": null, "isValidateBeforeStartDatetime": null }, "editEndDatetime": { "hours": null, "isCompareByDays": null, "isValidateBeforeStartDatetime": null }, "delete": { "hours": null, "isCompareByDays": null, "isValidateBeforeStartDatetime": null } }, "membershipDiscount": { "request": { "hours": null, "isCompareByDays": null, "isValidateBeforeStartDatetime": null }, "editStartDatetime": { "hours": null, "isCompareByDays": null, "isValidateBeforeStartDatetime": null }, "editEndDatetime": { "hours": null, "isCompareByDays": null, "isValidateBeforeStartDatetime": null }, "delete": { "hours": null, "isCompareByDays": null, "isValidateBeforeStartDatetime": null } } } }] };
    }
    /** 割引変更期限判定設定値 */
    function getDeadlineValidationExecuteSettingProperty() {
        return { "termDiscount": { "request": { "regionalSettingList": [{ "region": "JPN", "in1stFlow": { "doCheckOnSave": false, "doCheckOnSubmit": true, "doCheckOnSelfApprove": true }, "in3rdFlow": { "doCheckOnSave": false, "doCheckOnSubmit": true, "doCheckOnSelfApprove": true } }, { "region": "KOR", "in1stFlow": { "doCheckOnSave": false, "doCheckOnSubmit": true, "doCheckOnSelfApprove": true }, "in3rdFlow": { "doCheckOnSave": false, "doCheckOnSubmit": true, "doCheckOnSelfApprove": true } }, { "region": "TWN", "in1stFlow": { "doCheckOnSave": false, "doCheckOnSubmit": true, "doCheckOnSelfApprove": true }, "in3rdFlow": { "doCheckOnSave": false, "doCheckOnSubmit": true, "doCheckOnSelfApprove": true } }, { "region": "USA", "in1stFlow": { "doCheckOnSave": false, "doCheckOnSubmit": false, "doCheckOnSelfApprove": false }, "in3rdFlow": { "doCheckOnSave": false, "doCheckOnSubmit": false, "doCheckOnSelfApprove": false } }, { "region": "EUR", "in1stFlow": { "doCheckOnSave": false, "doCheckOnSubmit": false, "doCheckOnSelfApprove": false }, "in3rdFlow": { "doCheckOnSave": false, "doCheckOnSubmit": false, "doCheckOnSelfApprove": false } }, { "region": "CHN", "in1stFlow": { "doCheckOnSave": false, "doCheckOnSubmit": false, "doCheckOnSelfApprove": false }, "in3rdFlow": { "doCheckOnSave": false, "doCheckOnSubmit": false, "doCheckOnSelfApprove": false } }] }, "editStartDatetime": { "regionalSettingList": [{ "region": "JPN", "in1stFlow": { "doCheckOnSave": false, "doCheckOnSubmit": true, "doCheckOnSelfApprove": true }, "in3rdFlow": { "doCheckOnSave": false, "doCheckOnSubmit": true, "doCheckOnSelfApprove": true } }, { "region": "KOR", "in1stFlow": { "doCheckOnSave": false, "doCheckOnSubmit": true, "doCheckOnSelfApprove": true }, "in3rdFlow": { "doCheckOnSave": false, "doCheckOnSubmit": true, "doCheckOnSelfApprove": true } }, { "region": "TWN", "in1stFlow": { "doCheckOnSave": false, "doCheckOnSubmit": true, "doCheckOnSelfApprove": true }, "in3rdFlow": { "doCheckOnSave": false, "doCheckOnSubmit": true, "doCheckOnSelfApprove": true } }, { "region": "USA", "in1stFlow": { "doCheckOnSave": false, "doCheckOnSubmit": true, "doCheckOnSelfApprove": true }, "in3rdFlow": { "doCheckOnSave": false, "doCheckOnSubmit": true, "doCheckOnSelfApprove": true } }, { "region": "EUR", "in1stFlow": { "doCheckOnSave": false, "doCheckOnSubmit": true, "doCheckOnSelfApprove": true }, "in3rdFlow": { "doCheckOnSave": false, "doCheckOnSubmit": true, "doCheckOnSelfApprove": true } }, { "region": "CHN", "in1stFlow": { "doCheckOnSave": false, "doCheckOnSubmit": false, "doCheckOnSelfApprove": false }, "in3rdFlow": { "doCheckOnSave": false, "doCheckOnSubmit": false, "doCheckOnSelfApprove": false } }] }, "editEndDatetime": { "regionalSettingList": [{ "region": "JPN", "in1stFlow": { "doCheckOnSave": false, "doCheckOnSubmit": true, "doCheckOnSelfApprove": true }, "in3rdFlow": { "doCheckOnSave": false, "doCheckOnSubmit": true, "doCheckOnSelfApprove": true } }, { "region": "KOR", "in1stFlow": { "doCheckOnSave": false, "doCheckOnSubmit": true, "doCheckOnSelfApprove": true }, "in3rdFlow": { "doCheckOnSave": false, "doCheckOnSubmit": true, "doCheckOnSelfApprove": true } }, { "region": "TWN", "in1stFlow": { "doCheckOnSave": false, "doCheckOnSubmit": true, "doCheckOnSelfApprove": true }, "in3rdFlow": { "doCheckOnSave": false, "doCheckOnSubmit": true, "doCheckOnSelfApprove": true } }, { "region": "USA", "in1stFlow": { "doCheckOnSave": false, "doCheckOnSubmit": true, "doCheckOnSelfApprove": true }, "in3rdFlow": { "doCheckOnSave": false, "doCheckOnSubmit": true, "doCheckOnSelfApprove": true } }, { "region": "EUR", "in1stFlow": { "doCheckOnSave": false, "doCheckOnSubmit": true, "doCheckOnSelfApprove": true }, "in3rdFlow": { "doCheckOnSave": false, "doCheckOnSubmit": true, "doCheckOnSelfApprove": true } }, { "region": "CHN", "in1stFlow": { "doCheckOnSave": false, "doCheckOnSubmit": false, "doCheckOnSelfApprove": false }, "in3rdFlow": { "doCheckOnSave": false, "doCheckOnSubmit": false, "doCheckOnSelfApprove": false } }] }, "delete": { "regionalSettingList": [{ "region": "JPN", "doExecuteBy1stUser": false, "doExecuteBy3rdUser": true }, { "region": "KOR", "doExecuteBy1stUser": false, "doExecuteBy3rdUser": true }, { "region": "TWN", "doExecuteBy1stUser": false, "doExecuteBy3rdUser": true }, { "region": "USA", "doExecuteBy1stUser": false, "doExecuteBy3rdUser": true }, { "region": "EUR", "doExecuteBy1stUser": false, "doExecuteBy3rdUser": true }, { "region": "CHN", "doExecuteBy1stUser": false, "doExecuteBy3rdUser": true }] } }, "ownerDiscount": { "request": { "regionalSettingList": [{ "region": "JPN", "in1stFlow": { "doCheckOnSave": false, "doCheckOnSubmit": false, "doCheckOnSelfApprove": false }, "in3rdFlow": { "doCheckOnSave": false, "doCheckOnSubmit": false, "doCheckOnSelfApprove": false } }, { "region": "KOR", "in1stFlow": { "doCheckOnSave": false, "doCheckOnSubmit": false, "doCheckOnSelfApprove": false }, "in3rdFlow": { "doCheckOnSave": false, "doCheckOnSubmit": false, "doCheckOnSelfApprove": false } }, { "region": "TWN", "in1stFlow": { "doCheckOnSave": false, "doCheckOnSubmit": false, "doCheckOnSelfApprove": false }, "in3rdFlow": { "doCheckOnSave": false, "doCheckOnSubmit": false, "doCheckOnSelfApprove": false } }, { "region": "USA", "in1stFlow": { "doCheckOnSave": false, "doCheckOnSubmit": false, "doCheckOnSelfApprove": false }, "in3rdFlow": { "doCheckOnSave": false, "doCheckOnSubmit": false, "doCheckOnSelfApprove": false } }, { "region": "EUR", "in1stFlow": { "doCheckOnSave": false, "doCheckOnSubmit": false, "doCheckOnSelfApprove": false }, "in3rdFlow": { "doCheckOnSave": false, "doCheckOnSubmit": false, "doCheckOnSelfApprove": false } }, { "region": "CHN", "in1stFlow": { "doCheckOnSave": false, "doCheckOnSubmit": false, "doCheckOnSelfApprove": false }, "in3rdFlow": { "doCheckOnSave": false, "doCheckOnSubmit": false, "doCheckOnSelfApprove": false } }] }, "editStartDatetime": { "regionalSettingList": [{ "region": "JPN", "in1stFlow": { "doCheckOnSave": false, "doCheckOnSubmit": false, "doCheckOnSelfApprove": false }, "in3rdFlow": { "doCheckOnSave": false, "doCheckOnSubmit": false, "doCheckOnSelfApprove": false } }, { "region": "KOR", "in1stFlow": { "doCheckOnSave": false, "doCheckOnSubmit": false, "doCheckOnSelfApprove": false }, "in3rdFlow": { "doCheckOnSave": false, "doCheckOnSubmit": false, "doCheckOnSelfApprove": false } }, { "region": "TWN", "in1stFlow": { "doCheckOnSave": false, "doCheckOnSubmit": false, "doCheckOnSelfApprove": false }, "in3rdFlow": { "doCheckOnSave": false, "doCheckOnSubmit": false, "doCheckOnSelfApprove": false } }, { "region": "USA", "in1stFlow": { "doCheckOnSave": false, "doCheckOnSubmit": false, "doCheckOnSelfApprove": false }, "in3rdFlow": { "doCheckOnSave": false, "doCheckOnSubmit": false, "doCheckOnSelfApprove": false } }, { "region": "EUR", "in1stFlow": { "doCheckOnSave": false, "doCheckOnSubmit": false, "doCheckOnSelfApprove": false }, "in3rdFlow": { "doCheckOnSave": false, "doCheckOnSubmit": false, "doCheckOnSelfApprove": false } }, { "region": "CHN", "in1stFlow": { "doCheckOnSave": false, "doCheckOnSubmit": false, "doCheckOnSelfApprove": false }, "in3rdFlow": { "doCheckOnSave": false, "doCheckOnSubmit": false, "doCheckOnSelfApprove": false } }] }, "editEndDatetime": { "regionalSettingList": [{ "region": "JPN", "in1stFlow": { "doCheckOnSave": false, "doCheckOnSubmit": false, "doCheckOnSelfApprove": false }, "in3rdFlow": { "doCheckOnSave": false, "doCheckOnSubmit": false, "doCheckOnSelfApprove": false } }, { "region": "KOR", "in1stFlow": { "doCheckOnSave": false, "doCheckOnSubmit": false, "doCheckOnSelfApprove": false }, "in3rdFlow": { "doCheckOnSave": false, "doCheckOnSubmit": false, "doCheckOnSelfApprove": false } }, { "region": "TWN", "in1stFlow": { "doCheckOnSave": false, "doCheckOnSubmit": false, "doCheckOnSelfApprove": false }, "in3rdFlow": { "doCheckOnSave": false, "doCheckOnSubmit": false, "doCheckOnSelfApprove": false } }, { "region": "USA", "in1stFlow": { "doCheckOnSave": false, "doCheckOnSubmit": false, "doCheckOnSelfApprove": false }, "in3rdFlow": { "doCheckOnSave": false, "doCheckOnSubmit": false, "doCheckOnSelfApprove": false } }, { "region": "EUR", "in1stFlow": { "doCheckOnSave": false, "doCheckOnSubmit": false, "doCheckOnSelfApprove": false }, "in3rdFlow": { "doCheckOnSave": false, "doCheckOnSubmit": false, "doCheckOnSelfApprove": false } }, { "region": "CHN", "in1stFlow": { "doCheckOnSave": false, "doCheckOnSubmit": false, "doCheckOnSelfApprove": false }, "in3rdFlow": { "doCheckOnSave": false, "doCheckOnSubmit": false, "doCheckOnSelfApprove": false } }] }, "delete": { "regionalSettingList": [{ "region": "JPN", "doExecuteBy1stUser": false, "doExecuteBy3rdUser": false }, { "region": "KOR", "doExecuteBy1stUser": false, "doExecuteBy3rdUser": false }, { "region": "TWN", "doExecuteBy1stUser": false, "doExecuteBy3rdUser": false }, { "region": "USA", "doExecuteBy1stUser": false, "doExecuteBy3rdUser": false }, { "region": "EUR", "doExecuteBy1stUser": false, "doExecuteBy3rdUser": false }, { "region": "CHN", "doExecuteBy1stUser": false, "doExecuteBy3rdUser": false }] } }, "membershipDiscount": { "request": { "regionalSettingList": [{ "region": "JPN", "in1stFlow": { "doCheckOnSave": false, "doCheckOnSubmit": false, "doCheckOnSelfApprove": false }, "in3rdFlow": { "doCheckOnSave": false, "doCheckOnSubmit": false, "doCheckOnSelfApprove": false } }, { "region": "KOR", "in1stFlow": { "doCheckOnSave": false, "doCheckOnSubmit": false, "doCheckOnSelfApprove": false }, "in3rdFlow": { "doCheckOnSave": false, "doCheckOnSubmit": false, "doCheckOnSelfApprove": false } }, { "region": "TWN", "in1stFlow": { "doCheckOnSave": false, "doCheckOnSubmit": false, "doCheckOnSelfApprove": false }, "in3rdFlow": { "doCheckOnSave": false, "doCheckOnSubmit": false, "doCheckOnSelfApprove": false } }, { "region": "USA", "in1stFlow": { "doCheckOnSave": false, "doCheckOnSubmit": false, "doCheckOnSelfApprove": false }, "in3rdFlow": { "doCheckOnSave": false, "doCheckOnSubmit": false, "doCheckOnSelfApprove": false } }, { "region": "EUR", "in1stFlow": { "doCheckOnSave": false, "doCheckOnSubmit": false, "doCheckOnSelfApprove": false }, "in3rdFlow": { "doCheckOnSave": false, "doCheckOnSubmit": false, "doCheckOnSelfApprove": false } }, { "region": "CHN", "in1stFlow": { "doCheckOnSave": false, "doCheckOnSubmit": false, "doCheckOnSelfApprove": false }, "in3rdFlow": { "doCheckOnSave": false, "doCheckOnSubmit": false, "doCheckOnSelfApprove": false } }] }, "editStartDatetime": { "regionalSettingList": [{ "region": "JPN", "in1stFlow": { "doCheckOnSave": false, "doCheckOnSubmit": false, "doCheckOnSelfApprove": false }, "in3rdFlow": { "doCheckOnSave": false, "doCheckOnSubmit": false, "doCheckOnSelfApprove": false } }, { "region": "KOR", "in1stFlow": { "doCheckOnSave": false, "doCheckOnSubmit": false, "doCheckOnSelfApprove": false }, "in3rdFlow": { "doCheckOnSave": false, "doCheckOnSubmit": false, "doCheckOnSelfApprove": false } }, { "region": "TWN", "in1stFlow": { "doCheckOnSave": false, "doCheckOnSubmit": false, "doCheckOnSelfApprove": false }, "in3rdFlow": { "doCheckOnSave": false, "doCheckOnSubmit": false, "doCheckOnSelfApprove": false } }, { "region": "USA", "in1stFlow": { "doCheckOnSave": false, "doCheckOnSubmit": false, "doCheckOnSelfApprove": false }, "in3rdFlow": { "doCheckOnSave": false, "doCheckOnSubmit": false, "doCheckOnSelfApprove": false } }, { "region": "EUR", "in1stFlow": { "doCheckOnSave": false, "doCheckOnSubmit": false, "doCheckOnSelfApprove": false }, "in3rdFlow": { "doCheckOnSave": false, "doCheckOnSubmit": false, "doCheckOnSelfApprove": false } }, { "region": "CHN", "in1stFlow": { "doCheckOnSave": false, "doCheckOnSubmit": false, "doCheckOnSelfApprove": false }, "in3rdFlow": { "doCheckOnSave": false, "doCheckOnSubmit": false, "doCheckOnSelfApprove": false } }] }, "editEndDatetime": { "regionalSettingList": [{ "region": "JPN", "in1stFlow": { "doCheckOnSave": false, "doCheckOnSubmit": false, "doCheckOnSelfApprove": false }, "in3rdFlow": { "doCheckOnSave": false, "doCheckOnSubmit": false, "doCheckOnSelfApprove": false } }, { "region": "KOR", "in1stFlow": { "doCheckOnSave": false, "doCheckOnSubmit": false, "doCheckOnSelfApprove": false }, "in3rdFlow": { "doCheckOnSave": false, "doCheckOnSubmit": false, "doCheckOnSelfApprove": false } }, { "region": "TWN", "in1stFlow": { "doCheckOnSave": false, "doCheckOnSubmit": false, "doCheckOnSelfApprove": false }, "in3rdFlow": { "doCheckOnSave": false, "doCheckOnSubmit": false, "doCheckOnSelfApprove": false } }, { "region": "USA", "in1stFlow": { "doCheckOnSave": false, "doCheckOnSubmit": false, "doCheckOnSelfApprove": false }, "in3rdFlow": { "doCheckOnSave": false, "doCheckOnSubmit": false, "doCheckOnSelfApprove": false } }, { "region": "EUR", "in1stFlow": { "doCheckOnSave": false, "doCheckOnSubmit": false, "doCheckOnSelfApprove": false }, "in3rdFlow": { "doCheckOnSave": false, "doCheckOnSubmit": false, "doCheckOnSelfApprove": false } }, { "region": "CHN", "in1stFlow": { "doCheckOnSave": false, "doCheckOnSubmit": false, "doCheckOnSelfApprove": false }, "in3rdFlow": { "doCheckOnSave": false, "doCheckOnSubmit": false, "doCheckOnSelfApprove": false } }] }, "delete": { "regionalSettingList": [{ "region": "JPN", "doExecuteBy1stUser": false, "doExecuteBy3rdUser": false }, { "region": "KOR", "doExecuteBy1stUser": false, "doExecuteBy3rdUser": false }, { "region": "TWN", "doExecuteBy1stUser": false, "doExecuteBy3rdUser": false }, { "region": "USA", "doExecuteBy1stUser": false, "doExecuteBy3rdUser": false }, { "region": "EUR", "doExecuteBy1stUser": false, "doExecuteBy3rdUser": false }, { "region": "CHN", "doExecuteBy1stUser": false, "doExecuteBy3rdUser": false }] } } };
    }

    /** 入力された文字列チェック */
    var urlAjaxCheckCharacter = "\/ncms3\/temporaryDiscount\/setup\/ajaxCheckCharacter";

    /** リージョンの値 */
    DiscountCommon.Region = {};
    DiscountCommon.Region.JPN = "1";
    DiscountCommon.Region.USA = "2";
    DiscountCommon.Region.EUR = "3";
    DiscountCommon.Region.ASIA = "10";
    DiscountCommon.Region.KOR = "5";
    DiscountCommon.Region.TWN = "7";
    DiscountCommon.Region.CHN = "6";

    /** デバイスタイプIDの値 */
    DiscountCommon.DeviceType = {};
    DiscountCommon.DeviceType.CTR = "4";
    DiscountCommon.DeviceType.WUP = "5";
    DiscountCommon.DeviceType.HAC = "6";

    /** HACのNSUID種別 */
    DiscountCommon.HacNsUidTypes = [7001, 7003, 7005, 7007, 7008, 7009];

    /*]]>*/
</script>
