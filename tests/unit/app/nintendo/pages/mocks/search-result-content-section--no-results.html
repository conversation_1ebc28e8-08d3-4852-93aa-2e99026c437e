<div id="resultContent">
    <div>
        <script>
            var isNintendoUser = false;

            // 割引種別
            var discountTypeIdTerm = 0;
            var discountTypeIdOwner = 1;
            var discountTypeIdMembership = 2;

            // リンク用パス
            var termDiscountPath = "\/temporaryDiscount\/discountInfo";
            var ownerDiscountPath = "\/ownerDiscount\/discountInfo";
            var membershipDiscountPath = "\/membershipDiscount\/discountInfo";

            $(document).ready(function () {
                var termSortKeySelector = "#termDiscount_" + "SAVED_DATETIMEfalse";
                var ownerSortKeySelector = "#ownerDiscount_" + "SAVED_DATETIMEfalse";
                var membershipSortKeySelector = "#membershipDiscount_" + "SAVED_DATETIMEfalse";
                $(termSortKeySelector).prop("disabled", true);
                $(termSortKeySelector).removeClass('btn-default').addClass('btn-primary');
                $(ownerSortKeySelector).prop("disabled", true);
                $(ownerSortKeySelector).removeClass('btn-default').addClass('btn-primary');
                $(membershipSortKeySelector).prop("disabled", true);
                $(membershipSortKeySelector).removeClass('btn-default').addClass('btn-primary');
            });
        </script>

        <!-- 操作対象判定用 -->
        <input type="hidden" form="mainForm" id="discountTypeId" name="discountTypeId" value="0">

        <div id="termDiscount">
            <h2 style="margin-top: 20px;">
                <span>Temporary Discount List</span>
            </h2>

            <div id="termDiscountSearchNotFountMessage" style="width: 100%; margin-top: 10px;" class="container">
                <div class="row">
                    <span class="alert alert-info col-sm-12 text-center" role="alert">No discounts were found matching
                        the search condition.</span>
                </div>
            </div>


            <div style="margin-left: 1em;">
                <div style="height: 72px; display: none;" id="pagerAreaTERM"></div>
                <input type="hidden" form="mainForm" id="pageTermDiscount" name="pageTermDiscount" value="1">
                <input type="hidden" form="mainForm" id="termDiscountDispCount" name="termDiscountDispCount" value="25">
                <input type="hidden" form="mainForm" id="maxCountTERM" name="maxCountTERM" value="0">

                <div style="position: relative; display: none;" class="SelectInfoS pull-right">
                    <select id="selectTermDiscountDispCount" class="indicator" data-discounttypeterm="TERM"
                        onchange="changeDispCount(this.getAttribute('data-discountTypeTERM'));">
                        <option value="10">10</option>
                        <option value="25" selected="selected">25</option>
                        <option value="50">50</option>
                    </select> <span class="SelectValue">25</span>
                </div><br>
            </div>

            <div class="panel-body termDiscountDisp" align="center" style="display: none;">
                <!-- ソート情報 -->
                <input type="hidden" form="mainForm" id="sortKeyTermDiscount" name="sortKeyTermDiscount"
                    value="SAVED_DATETIME">
                <input type="hidden" form="mainForm" id="sortKeyFlagTermDiscount" name="sortKeyFlagTermDiscount"
                    value="false">

                <table class="table table_searchList table_doubleStriped table-hover" id="termDiscountTable">
                    <!-- 期間指定割引リストのヘッダ -->
                    <thead>
                        <tr align="center">
                            <td width="5%"></td>
                            <td width="8%">
                                <span>Submission ID</span><br>
                                <button type="button" id="termDiscount_SUBMISSION_IDfalse"
                                    class="btn btn-xs stopChannels btn-default" data-sortkey="SUBMISSION_ID"
                                    data-desc="false" data-discounttypeterm="TERM"
                                    onclick="changeSort(this.getAttribute('data-sortKey'), this.getAttribute('data-desc'), this.getAttribute('data-discountTypeTERM'));return false;">
                                    <span class="glyphicon glyphicon-chevron-down" aria-hidden="true"></span>
                                </button>
                                <button type="button" id="termDiscount_SUBMISSION_IDtrue"
                                    class="btn btn-xs stopChannels btn-default" data-sortkey="SUBMISSION_ID"
                                    data-asc="true" data-discounttypeterm="TERM"
                                    onclick="changeSort(this.getAttribute('data-sortKey'), this.getAttribute('data-asc'), this.getAttribute('data-discountTypeTERM'));return false;">
                                    <span class="glyphicon glyphicon-chevron-up" aria-hidden="true"></span>
                                </button>
                            </td>
                            <td width="10%">
                                <span>Product Code</span><br>
                                <button type="button" id="termDiscount_PRODUCT_CODEfalse"
                                    class="btn btn-xs stopChannels btn-default" data-sortkey="PRODUCT_CODE"
                                    data-desc="false" data-discounttypeterm="TERM"
                                    onclick="changeSort(this.getAttribute('data-sortKey'), this.getAttribute('data-desc'), this.getAttribute('data-discountTypeTERM'));return false;">
                                    <span class="glyphicon glyphicon-chevron-down" aria-hidden="true"></span>
                                </button>
                                <button type="button" id="termDiscount_PRODUCT_CODEtrue"
                                    class="btn btn-xs stopChannels btn-default" data-sortkey="PRODUCT_CODE"
                                    data-asc="true" data-discounttypeterm="TERM"
                                    onclick="changeSort(this.getAttribute('data-sortKey'), this.getAttribute('data-asc'), this.getAttribute('data-discountTypeTERM'));return false;">
                                    <span class="glyphicon glyphicon-chevron-up" aria-hidden="true"></span>
                                </button>
                            </td>
                            <td width="10%">
                                <span>Region</span><br>
                                <button type="button" id="termDiscount_REGIONfalse"
                                    class="btn btn-xs stopChannels btn-default" data-sortkey="REGION" data-desc="false"
                                    data-discounttypeterm="TERM"
                                    onclick="changeSort(this.getAttribute('data-sortKey'), this.getAttribute('data-desc'), this.getAttribute('data-discountTypeTERM'));return false;">
                                    <span class="glyphicon glyphicon-chevron-down" aria-hidden="true"></span>
                                </button>
                                <button type="button" id="termDiscount_REGIONtrue"
                                    class="btn btn-xs stopChannels btn-default" data-sortkey="REGION" data-asc="true"
                                    data-discounttypeterm="TERM"
                                    onclick="changeSort(this.getAttribute('data-sortKey'), this.getAttribute('data-asc'), this.getAttribute('data-discountTypeTERM'));return false;">
                                    <span class="glyphicon glyphicon-chevron-up" aria-hidden="true"></span>
                                </button>
                            </td>
                            <td width="20%">
                                <span>Sale Name</span><br>
                                <button type="button" id="termDiscount_TITLE_NAMEfalse"
                                    class="btn btn-xs stopChannels btn-default" data-sortkey="TITLE_NAME"
                                    data-desc="false" data-discounttypeterm="TERM"
                                    onclick="changeSort(this.getAttribute('data-sortKey'), this.getAttribute('data-desc'), this.getAttribute('data-discountTypeTERM'));return false;">
                                    <span class="glyphicon glyphicon-chevron-down" aria-hidden="true"></span>
                                </button>
                                <button type="button" id="termDiscount_TITLE_NAMEtrue"
                                    class="btn btn-xs stopChannels btn-default" data-sortkey="TITLE_NAME"
                                    data-asc="true" data-discounttypeterm="TERM"
                                    onclick="changeSort(this.getAttribute('data-sortKey'), this.getAttribute('data-asc'), this.getAttribute('data-discountTypeTERM'));return false;">
                                    <span class="glyphicon glyphicon-chevron-up" aria-hidden="true"></span>
                                </button>
                            </td>
                            <td width="10%">
                                <span>Start Date/Time</span><br>
                                <button type="button" id="termDiscount_START_DATETIMEfalse"
                                    class="btn btn-xs stopChannels btn-default" data-sortkey="START_DATETIME"
                                    data-desc="false" data-discounttypeterm="TERM"
                                    onclick="changeSort(this.getAttribute('data-sortKey'), this.getAttribute('data-desc'), this.getAttribute('data-discountTypeTERM'));return false;">
                                    <span class="glyphicon glyphicon-chevron-down" aria-hidden="true"></span>
                                </button>
                                <button type="button" id="termDiscount_START_DATETIMEtrue"
                                    class="btn btn-xs stopChannels btn-default" data-sortkey="START_DATETIME"
                                    data-asc="true" data-discounttypeterm="TERM"
                                    onclick="changeSort(this.getAttribute('data-sortKey'), this.getAttribute('data-asc'), this.getAttribute('data-discountTypeTERM'));return false;">
                                    <span class="glyphicon glyphicon-chevron-up" aria-hidden="true"></span>
                                </button>
                            </td>
                            <td width="10%">
                                <span>End Date/Time</span><br>
                                <button type="button" id="termDiscount_END_DATETIMEfalse"
                                    class="btn btn-xs stopChannels btn-default" data-sortkey="END_DATETIME"
                                    data-desc="false" data-discounttypeterm="TERM"
                                    onclick="changeSort(this.getAttribute('data-sortKey'), this.getAttribute('data-desc'), this.getAttribute('data-discountTypeTERM'));return false;">
                                    <span class="glyphicon glyphicon-chevron-down" aria-hidden="true"></span>
                                </button>
                                <button type="button" id="termDiscount_END_DATETIMEtrue"
                                    class="btn btn-xs stopChannels btn-default" data-sortkey="END_DATETIME"
                                    data-asc="true" data-discounttypeterm="TERM"
                                    onclick="changeSort(this.getAttribute('data-sortKey'), this.getAttribute('data-asc'), this.getAttribute('data-discountTypeTERM'));return false;">
                                    <span class="glyphicon glyphicon-chevron-up" aria-hidden="true"></span>
                                </button>
                            </td>
                            <td width="10%">
                                <span>Status</span><br>
                                <button type="button" id="termDiscount_STATUSfalse"
                                    class="btn btn-xs stopChannels btn-default" data-sortkey="STATUS" data-desc="false"
                                    data-discounttypeterm="TERM"
                                    onclick="changeSort(this.getAttribute('data-sortKey'), this.getAttribute('data-desc'), this.getAttribute('data-discountTypeTERM'));return false;">
                                    <span class="glyphicon glyphicon-chevron-down" aria-hidden="true"></span>
                                </button>
                                <button type="button" id="termDiscount_STATUStrue"
                                    class="btn btn-xs stopChannels btn-default" data-sortkey="STATUS" data-asc="true"
                                    data-discounttypeterm="TERM"
                                    onclick="changeSort(this.getAttribute('data-sortKey'), this.getAttribute('data-asc'), this.getAttribute('data-discountTypeTERM'));return false;">
                                    <span class="glyphicon glyphicon-chevron-up" aria-hidden="true"></span>
                                </button>
                            </td>
                            <td width="7%">
                                <span>Last Update Date/Time</span><br>
                                <button type="button" id="termDiscount_SAVED_DATETIMEfalse"
                                    class="btn btn-xs stopChannels btn-primary" data-sortkey="SAVED_DATETIME"
                                    data-desc="false" data-discounttypeterm="TERM"
                                    onclick="changeSort(this.getAttribute('data-sortKey'), this.getAttribute('data-desc'), this.getAttribute('data-discountTypeTERM'));return false;"
                                    disabled="">
                                    <span class="glyphicon glyphicon-chevron-down" aria-hidden="true"></span>
                                </button>
                                <button type="button" id="termDiscount_SAVED_DATETIMEtrue"
                                    class="btn btn-xs stopChannels btn-default" data-sortkey="SAVED_DATETIME"
                                    data-asc="true" data-discounttypeterm="TERM"
                                    onclick="changeSort(this.getAttribute('data-sortKey'), this.getAttribute('data-asc'), this.getAttribute('data-discountTypeTERM'));return false;">
                                    <span class="glyphicon glyphicon-chevron-up" aria-hidden="true"></span>
                                </button>
                            </td>

                        </tr>
                    </thead>
                    <!-- 期間指定割引リストのデータ部 -->
                    <tbody>

                    </tbody>
                </table>
            </div>
        </div>




        <script type="text/javascript"
            src="/ncms3/js/discount/searchResult-cdc7346e620b00acb34d0ac490883a0f.js"></script>

        <script>
            /*<![CDATA[*/

            /** 製品コード */
            var productCodeText = "Product Code";
            /** コンテンツ種別 */
            var contentTypeText = "Content Type";
            /** コンテンツ名 */
            var contentNameText = "Content Name";
            /** メーカー */
            var publisherText = "Publisher";
            /** ステータス */
            var statusText = "Status";
            /** 最終更新日時 */
            var lastEditedDatetimeText = "Last Update Date\/Time";

            /*]]>*/
        </script>
    </div>
</div>
