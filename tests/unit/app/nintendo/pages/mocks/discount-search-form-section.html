<input
  type="text"
  format="dd/mm/yy"
  id="rangeDateTimeTo.dateField"
  name="rangeDateTimeTo.dateField"
  value=""
/>
<div>
  <input type="hidden" class="datetimeFormat" value="MM/dd/yyyy HH:mm:ss" />
  <input
    type="hidden"
    class="clientDatetimeFormat"
    value="MM/DD/YYYY HH:mm:ss"
  />
  <input type="hidden" class="minDate" value="" />
  <input type="hidden" class="hideClearButton" />

  <input type="hidden" value="23" class="hourFixedForm" />
  <input type="hidden" value="59" class="minuteFixedForm" />
  <input type="hidden" value="59" class="secondFixedForm" />

  <input type="hidden" class="isEndDatetime" value="true" />
</div>
