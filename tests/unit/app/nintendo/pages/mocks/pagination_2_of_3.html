<div style="margin-left: 1em;">
    <input type="hidden" form="mainForm" id="pageTermDiscount" name="pageTermDiscount" value="2">
    <input type="hidden" form="mainForm" id="termDiscountDispCount" name="termDiscountDispCount" value="25">
    <input type="hidden" form="mainForm" id="maxCountTERM" name="maxCountTERM" value="74">

    <div style="position: relative;" class="SelectInfoS pull-right">
        <select id="selectTermDiscountDispCount" class="indicator" data-discounttypeterm="TERM"
            onchange="changeDispCount(this.getAttribute('data-discountTypeTERM'));">
            <option value="10">10</option>
            <option value="25" selected="selected">25</option>
            <option value="50">50</option>
        </select> <span class="SelectValue">25</span>
    </div><br>
</div>
