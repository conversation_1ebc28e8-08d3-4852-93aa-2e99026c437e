{"discountGroups": [{"dataSource": "QA", "discountType": "TERM", "changeSetId": "8c4ad4fc-5b9b-45e8-adba-32185773e51f", "discountGroupId": "1", "approveStatus": "3", "approveDate": "2023-10-11T09:04:13+0000", "reasonCode": null, "contents": [{"contentsType": "TITLE", "nsUid": "60005000000001", "titleId": null, "discounts": [{"discountId": "101", "priceId": "5000000101", "countryCode": "AU", "discountValue": "21", "startDatetime": "2023-10-26T13:00:00+0000", "endDatetime": "2023-11-24T12:59:59+0000", "statExclusionFlag": "0"}, {"discountId": "102", "priceId": "5000000102", "countryCode": "AT", "discountValue": "13.8", "startDatetime": "2023-10-26T13:00:00+0000", "endDatetime": "2023-11-24T22:59:59+0000", "statExclusionFlag": "0"}]}]}, {"dataSource": "LIVE", "discountType": "TERM", "changeSetId": null, "discountGroupId": "1", "approveStatus": "3", "approveDate": "2023-10-11T09:04:13+0000", "reasonCode": null, "contents": [{"contentsType": "TITLE", "nsUid": "60005000000001", "titleId": null, "discounts": [{"discountId": "101", "priceId": "5000000101", "countryCode": "AU", "discountValue": "21", "startDatetime": "2023-10-26T13:00:00+0000", "endDatetime": "2023-11-24T12:59:59+0000", "statExclusionFlag": "0"}, {"discountId": "102", "priceId": "5000000102", "countryCode": "AT", "discountValue": "13.8", "startDatetime": "2023-10-26T13:00:00+0000", "endDatetime": "2023-11-24T22:59:59+0000", "statExclusionFlag": "0"}]}]}]}