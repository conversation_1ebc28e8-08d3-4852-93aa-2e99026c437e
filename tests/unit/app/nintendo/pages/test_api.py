from unittest.mock import ANY, MagicMock

import pytest
import requests

from app.nintendo.discounts.api import API


@pytest.fixture
def mocked_api():
    def _post(response):
        cookies = MagicMock()
        cookies.set = MagicMock()

        http = MagicMock(spec=requests.Session)
        http.headers = {}
        http.cookies = cookies
        http.post.return_value = response
        return API(http), http.post

    return _post


def test_get_prices_fetches_production_data(api_response_prices, mocked_api):
    api, mocked_post = mocked_api(api_response_prices)
    response = api.get_prices(ANY, "ANY", "BUNDLE")
    fields = dict(mocked_post.call_args[1]["files"])

    assert mocked_post.call_count == 1
    assert response == api_response_prices.json()
    assert fields["dataSource"][1] in ["BOTH", "LIVE"]


def test_get_group_discount_fetches_production_data(
    api_response_discount_group, mocked_api
):
    api, mocked_post = mocked_api(api_response_discount_group)
    response = api.get_group_discounts(ANY, "ANY")
    fields = dict(mocked_post.call_args[1]["files"])

    assert mocked_post.call_count == 1
    assert response == api_response_discount_group.json()
    assert fields["dataSource"][1] in ["BOTH", "LIVE"]


# TODO: add tests for error handling
