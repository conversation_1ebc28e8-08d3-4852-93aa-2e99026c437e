from unittest.mock import ANY, MagicMock, patch

import pytest
import requests

from app.core.exceptions import MissingPermissionsException, SessionExpiredException
from app.nintendo.discounts.pages.discount_search_page import DiscountSearchPage


@pytest.fixture
def search_page_with_mocked_get_search_result_page(
    search_result_valid_page, get_mocked_result_pages
):
    mocked_result_pages = get_mocked_result_pages(3)

    http = MagicMock(spec=requests.Session)
    http.get.return_value = search_result_valid_page
    http.headers = {}
    http.cookies = MagicMock()

    page = DiscountSearchPage(http)
    page.get_search_result_page = lambda page_number: mocked_result_pages[page_number]

    return page, mocked_result_pages


def test_get_all_submission_ids(search_page_with_mocked_get_search_result_page):
    page, _ = search_page_with_mocked_get_search_result_page
    assert page.get_all_submission_ids() == list(range(1, 10))


def test_get_all_pages(search_page_with_mocked_get_search_result_page):
    page, result_pages = search_page_with_mocked_get_search_result_page
    assert page.get_all_pages() == [result_pages[1], result_pages[2], result_pages[3]]


def test_get_search_result_page(
    search_result_valid_page, search_result_content_section
):
    http = MagicMock(spec=requests.Session)
    http.get.return_value = search_result_valid_page
    http.post.return_value = search_result_content_section
    http.headers = {}
    http.cookies = MagicMock()

    page = DiscountSearchPage(http)
    page_number = 1
    result = page.get_search_result_page(page_number)

    expected_data = [
        ("submissionId", (None, "")),
        ("discountTypes", (None, "0")),
        ("deviceTypeId", (None, "")),
        ("subjectContentName", (None, "")),
        ("initialCode", (None, "")),
        ("region", (None, "")),
        ("country", (None, "")),
        ("statuses", (None, "11")),
        ("startDateTimeFrom.standardTimeZone", (None, "UTC")),
        ("startDateTimeTo.standardTimeZone", (None, "UTC")),
        ("endDateTimeFrom.standardTimeZone", (None, "UTC")),
        ("endDateTimeTo.standardTimeZone", (None, "UTC")),
        ("rangeDateTimeFrom.standardTimeZone", (None, "UTC")),
        ("rangeDateTimeTo.standardTimeZone", (None, "UTC")),
        ("saleName", (None, "")),
        ("discountTypeId", (None, "0")),
        ("sortKeyTermDiscount", (None, "SAVED_DATETIME")),
        ("sortKeyFlagTermDiscount", (None, "false")),
        ("ownerDiscountDispCount", (None, "0")),
        ("sortKeyOwnerDiscount", (None, "SAVED_DATETIME")),
        ("sortKeyFlagOwnerDiscount", (None, "false")),
        ("_csrf", (None, "28a8d7f9-7b27-4313-9b37-360911eb25b7")),
        ("termDiscountDispCount", (None, "50")),
        ("pageTermDiscount", (None, str(page_number))),
        ("contentTypes", (None, "1")),
        ("contentTypes", (None, "7")),
        ("contentTypes", (None, "9")),
        ("contentTypes", (None, "31")),
    ]

    http.post.assert_called_once_with(
        "https://ncms3.mng.nintendo.net/ncms3/discount/search",
        files=ANY,
        headers=ANY,
        timeout=ANY,
    )
    kwargs = http.post.call_args.kwargs
    assert kwargs["files"] == expected_data
    assert kwargs["headers"]["x-csrf-token"] == "28a8d7f9-7b27-4313-9b37-360911eb25b7"

    assert result.get_page_number() == page_number


@pytest.fixture
def result_page():
    def _result_page(response):
        http = MagicMock(spec=requests.Session)
        http.get.return_value = response
        http.headers = {}
        http.cookies = MagicMock()
        return DiscountSearchPage(http)

    return _result_page


def test_validate_permissions__missing_permissions(
    result_page,
    discount_search__missing_permissions,
):
    with pytest.raises(MissingPermissionsException):
        result_page(discount_search__missing_permissions)


def test_get_session_identifier(result_page, search_result_valid_page):
    page = result_page(search_result_valid_page)
    assert page.get_session_identifier() == "supersuperdata"


def test_get_session_identifier_raises_session_expired_exception_if_loginid_cannot_be_found(
    result_page,
    search_result_content_no_span_tag_for_loginid,
):
    page = result_page(search_result_content_no_span_tag_for_loginid)
    with pytest.raises(SessionExpiredException):
        page.get_session_identifier()


def test_get_session_identifier_raises_session_expired_exception_if_loginid_is_empty(
    result_page,
    search_result_content_no_content_for_loginid,
):
    page = result_page(search_result_content_no_content_for_loginid)
    with pytest.raises(SessionExpiredException):
        page.get_session_identifier()


@pytest.mark.usefixtures("mock_sleep")
def test_search_page_should_return_session_expired_when_getting_login_page(
    result_page, discount_search__login_form
):
    with pytest.raises(SessionExpiredException):
        result_page(discount_search__login_form)


@pytest.mark.usefixtures("mock_sleep")
def test_search_page_should_retry_request_in_case_of_session_expired_exception(
    search_result_valid_page,
):
    with patch.object(
        DiscountSearchPage,
        "do_request",
        side_effect=[SessionExpiredException, search_result_valid_page],
    ):
        http = MagicMock(spec=requests.Session)
        http.cookies = MagicMock()
        DiscountSearchPage(http)


@pytest.mark.usefixtures("mock_sleep")
def test_search_page_should_retry_getting_page_up_to_5_times_and_rethrow_if_failed():
    with patch.object(
        DiscountSearchPage, "do_request", side_effect=SessionExpiredException
    ):
        http = MagicMock(spec=requests.Session)
        http.cookies = MagicMock()
        with pytest.raises(SessionExpiredException):
            DiscountSearchPage(http)

        assert DiscountSearchPage.do_request.call_count == 5
