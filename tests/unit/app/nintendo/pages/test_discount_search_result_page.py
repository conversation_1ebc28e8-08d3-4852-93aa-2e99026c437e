from unittest.mock import MagicMock

import pytest
import requests

from app.nintendo.discounts.pages.discount_search_result_page import (
    DiscountSearchResultPage,
)
from app.nintendo.discounts.types import CSRFToken


@pytest.fixture
def result_page():
    def _result_page(response):
        http = MagicMock(spec=requests.Session)
        http.post.return_value = response
        http.headers = {}
        http.cookies = MagicMock()
        return DiscountSearchResultPage(
            http, CSRFToken("fake"), 1, "DD/MM/YYYY HH:mm:ss"
        )

    return _result_page


def test_get_results_number(result_page, search_result_content_section):
    page = result_page(search_result_content_section)
    assert page.get_results_number() == 70


def test_submission_ids(result_page, search_result_content_section):
    page = result_page(search_result_content_section)
    ids = page.get_submission_ids()

    expected_ids = [
        190374,
        190372,
        190370,
        190366,
        190363,
        181850,
        181849,
        181848,
        172171,
        172169,
    ]

    assert set(ids) == set(expected_ids)
    assert len(ids) == 10


def test_is_last_page_when_not_on_last_page(result_page, search_result_content_section):
    page = result_page(search_result_content_section)
    assert page.is_last_page() is False


@pytest.mark.parametrize(
    ("html_file_name", "expected_page_number"),
    [
        ("search-result-content-section.html", 1),
        ("pagination_1_of_1.html", 1),
        ("pagination_1_of_3.html", 1),
        ("pagination_2_of_3.html", 2),
        ("pagination_3_of_3.html", 3),
    ],
)
def test_get_page_number(
    result_page, files_based_mock_response, html_file_name, expected_page_number
):
    page = result_page(files_based_mock_response([html_file_name]))
    assert page.get_page_number() == expected_page_number


@pytest.mark.parametrize(
    ("html_file_name", "is_last_page"),
    [
        ("search-result-content-section.html", False),
        ("pagination_1_of_1.html", True),
        ("pagination_1_of_3.html", False),
        ("pagination_2_of_3.html", False),
        ("pagination_3_of_3.html", True),
    ],
)
def test_is_last_page(
    result_page, files_based_mock_response, html_file_name, is_last_page
):
    page = result_page(files_based_mock_response([html_file_name]))
    assert page.is_last_page() == is_last_page


@pytest.mark.parametrize(
    ("html_file_name", "results_per_page"),
    [
        ("search-result-content-section.html", 10),
        ("pagination_1_of_1.html", 50),
        ("pagination_1_of_3.html", 25),
        ("pagination_2_of_3.html", 25),
        ("pagination_3_of_3.html", 25),
    ],
)
def test_get_results_per_page(
    result_page, files_based_mock_response, html_file_name, results_per_page
):
    page = result_page(files_based_mock_response([html_file_name]))
    assert page.get_results_per_page() == results_per_page
