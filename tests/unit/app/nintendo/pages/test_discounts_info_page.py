from unittest.mock import ANY, MagicMock

import pytest
import requests

from app.nintendo.discounts.pages.discounts_info_page import DiscountInfoPage


@pytest.fixture
def discount_info_page():
    def _discount_info_page(response):
        http = MagicMock(spec=requests.Session)
        http.get.return_value = response
        http.cookies = MagicMock()
        return DiscountInfoPage(http, ANY)

    return _discount_info_page


def test_get_access_token(discount_info_page, discount_info_response):
    page = discount_info_page(discount_info_response)
    assert page.get_access_token() == "12345"


def test_get_discount_group_ids__single_id(discount_info_page, discount_info_response):
    page = discount_info_page(discount_info_response)
    assert page.get_discount_group_ids() == ["67890"]


def test_get_discount_group_ids__multiple_ids(
    discount_info_page, discount_info_response__multiple_discount_group_ids
):
    page = discount_info_page(discount_info_response__multiple_discount_group_ids)
    assert page.get_discount_group_ids() == ["67890", "67891"]


def test_get_ns_uid(discount_info_page, discount_info__script_document_ready_section):
    page = discount_info_page(discount_info__script_document_ready_section)
    assert page.get_ns_uids() == ["70010000020724", "70010000020725"]


def test_get_sale_name(discount_info_page, discount_info__sale_name_section):
    page = discount_info_page(discount_info__sale_name_section)
    assert page.get_sale_name() == "SUPERHOT Sale"


def test_get_discount_content_list(
    discount_info_page, discount_info__script_document_ready_section
):
    page = discount_info_page(discount_info__script_document_ready_section)

    assert page.get_discount_content_list() == {
        "valid": True,
        "content": [
            {
                "discountCountries": [
                    {
                        "countryId": None,
                        "countryCode": "AT",
                        "countryName": None,
                        "discountId": None,
                        "priceId": None,
                        "startDatetime": None,
                        "endDatetime": None,
                        "regularValue": None,
                        "discountValue": None,
                        "discountedValue": None,
                    },
                    {
                        "countryId": None,
                        "countryCode": "BE",
                        "countryName": None,
                        "discountId": None,
                        "priceId": None,
                        "startDatetime": None,
                        "endDatetime": None,
                        "regularValue": None,
                        "discountValue": None,
                        "discountedValue": None,
                    },
                ],
                "primaryKey": 33809,
                "nsUid": "70010000020724",
                "titleId": None,
                "contentType": "TITLE",
                "contentTypeName": "Title",
                "regionName": "Europe/Australia",
                "contentName": "SUPERHOT",
                "productCode": "HAC-P-AURNA",
                "lastEditedDate": "02/07/2021 15:18:30 (CEST)",
                "lastEditedDateForSort": "2021-07-02 13:18:30",
                "status": "Approved",
                "platFormName": "Nintendo Switch downloadable software",
                "publisherName": "SUPERHOT(SUPERHOT)",
                "onlinePublisherId": 11094,
                "deviceTypeId": 6,
                "statExclusionFlag": None,
                "onlinePriceChangesetId": None,
                "contentTypeId": {"present": True},
            },
            {
                "discountCountries": [
                    {
                        "countryId": None,
                        "countryCode": "AT",
                        "countryName": None,
                        "discountId": None,
                        "priceId": None,
                        "startDatetime": None,
                        "endDatetime": None,
                        "regularValue": None,
                        "discountValue": None,
                        "discountedValue": None,
                    },
                    {
                        "countryId": None,
                        "countryCode": "BE",
                        "countryName": None,
                        "discountId": None,
                        "priceId": None,
                        "startDatetime": None,
                        "endDatetime": None,
                        "regularValue": None,
                        "discountValue": None,
                        "discountedValue": None,
                    },
                ],
                "primaryKey": 33810,
                "nsUid": "70010000020725",
                "titleId": None,
                "contentType": "TITLE",
                "contentTypeName": "Title",
                "regionName": "Europe/Australia",
                "contentName": "SUPERHOT 2",
                "productCode": "HAC-P-TRINA",
                "lastEditedDate": "02/07/2021 15:18:30 (CEST)",
                "lastEditedDateForSort": "2021-07-02 13:18:30",
                "status": "Approved",
                "platFormName": "Nintendo Switch downloadable software",
                "publisherName": "SUPERHOT(SUPERHOT)",
                "onlinePublisherId": 11094,
                "deviceTypeId": 6,
                "statExclusionFlag": None,
                "onlinePriceChangesetId": None,
                "contentTypeId": {"present": True},
            },
        ],
    }


def test_get_country_currency_mapping(
    discount_info_page,
    discount_info__script_document_ready_section,
):
    page = discount_info_page(discount_info__script_document_ready_section)

    # fmt: off
    # pylint: disable=line-too-long
    expected_mapping = [
        {"countryCode": "JP", "countryName": "Japan", "currencyCode": "JPY"},
        {"countryCode": "US", "countryName": "United States", "currencyCode": "USD"},
        {"countryCode": "CA", "countryName": "Canada", "currencyCode": "CAD"},
        {"countryCode": "MX", "countryName": "Mexico", "currencyCode": "MXN"},
        {"countryCode": "BR", "countryName": "Brazil", "currencyCode": "BRL"},
        {"countryCode": "AI", "countryName": "Anguilla", "currencyCode": "USD"},
        {"countryCode": "AG", "countryName": "Antigua and Barbuda", "currencyCode": "USD"},
        {"countryCode": "AR", "countryName": "Argentina", "currencyCode": "ARS"},
        {"countryCode": "AW", "countryName": "Aruba", "currencyCode": "USD"},
        {"countryCode": "BS", "countryName": "Commonwealth of The Bahamas", "currencyCode": "USD"},
        {"countryCode": "BB", "countryName": "Barbados", "currencyCode": "USD"},
        {"countryCode": "BZ", "countryName": "Belize", "currencyCode": "USD"},
        {"countryCode": "BM", "countryName": "Bermuda", "currencyCode": "USD"},
        {"countryCode": "BO", "countryName": "Republic of Bolivia", "currencyCode": "USD"},
        {"countryCode": "VG", "countryName": "British Virgin Islands", "currencyCode": "USD"},
        {"countryCode": "KY", "countryName": "Cayman Islands", "currencyCode": "USD"},
        {"countryCode": "CL", "countryName": "Chile", "currencyCode": "CLP"},
        {"countryCode": "CO", "countryName": "Colombia", "currencyCode": "COP"},
        {"countryCode": "CR", "countryName": "Costa Rica", "currencyCode": "USD"},
        {"countryCode": "DM", "countryName": "Commonwealth of Dominica", "currencyCode": "USD"},
        {"countryCode": "DO", "countryName": "Dominican Republic", "currencyCode": "USD"},
        {"countryCode": "EC", "countryName": "Ecuador", "currencyCode": "USD"},
        {"countryCode": "SV", "countryName": "Republic of El Salvador", "currencyCode": "USD"},
        {"countryCode": "GF", "countryName": "Guiana", "currencyCode": "USD"},
        {"countryCode": "GD", "countryName": "Grenada", "currencyCode": "USD"},
        {"countryCode": "GP", "countryName": "Guadeloupe", "currencyCode": "USD"},
        {"countryCode": "GT", "countryName": "Guatemala", "currencyCode": "USD"},
        {"countryCode": "HT", "countryName": "Republic of Haiti", "currencyCode": "USD"},
        {"countryCode": "HN", "countryName": "Honduras", "currencyCode": "USD"},
        {"countryCode": "JM", "countryName": "Jamaica", "currencyCode": "USD"},
        {"countryCode": "MQ", "countryName": "Martinique", "currencyCode": "USD"},
        {"countryCode": "MS", "countryName": "Montserrat", "currencyCode": "USD"},
        {"countryCode": "AN", "countryName": "Nederlandse Antillen", "currencyCode": "USD"},
        {"countryCode": "NI", "countryName": "Nicaragua", "currencyCode": "USD"},
        {"countryCode": "PA", "countryName": "Panama", "currencyCode": "USD"},
        {"countryCode": "PY", "countryName": "Paraguay", "currencyCode": "USD"},
        {"countryCode": "PE", "countryName": "Peru", "currencyCode": "PEN"},
        {"countryCode": "KN", "countryName": "Saint Christopher and Nevis", "currencyCode": "USD"},
        {"countryCode": "LC", "countryName": "Saint Lucia", "currencyCode": "USD"},
        {"countryCode": "VC", "countryName": "Saint Vincent and the Grenadines", "currencyCode": "USD"},
        {"countryCode": "SR", "countryName": "Republic of Suriname", "currencyCode": "USD"},
        {"countryCode": "TT", "countryName": "Republic of Trinidad and Tobago", "currencyCode": "USD"},
        {"countryCode": "TC", "countryName": "Turks and Caicos Islands", "currencyCode": "USD"},
        {"countryCode": "UY", "countryName": "Uruguay", "currencyCode": "USD"},
        {"countryCode": "VI", "countryName": "Virgin Islands of the United States", "currencyCode": "USD"},
        {"countryCode": "VE", "countryName": "Venezuela", "currencyCode": "USD"},
        {"countryCode": "SG", "countryName": "Republic of Singapore", "currencyCode": "USD"},
        {"countryCode": "MY", "countryName": "Malaysia", "currencyCode": "USD"},
        {"countryCode": "AE", "countryName": "United Arab Emirates", "currencyCode": "USD"},
        {"countryCode": "SA", "countryName": "Kingdom of Saudi Arabia", "currencyCode": "USD"},
        {"countryCode": "AL", "countryName": "Republic of Albania", "currencyCode": "DEF"},
        {"countryCode": "AT", "countryName": "Austria", "currencyCode": "EUR"},
        {"countryCode": "BE", "countryName": "Belgium", "currencyCode": "EUR"},
        {"countryCode": "BA", "countryName": "Bosnia and Herzegovina", "currencyCode": "DEF"},
        {"countryCode": "BW", "countryName": "Republic of Botswana", "currencyCode": "DEF"},
        {"countryCode": "BG", "countryName": "Bulgaria", "currencyCode": "EUR"},
        {"countryCode": "HR", "countryName": "Croatia", "currencyCode": "EUR"},
        {"countryCode": "CY", "countryName": "Cyprus", "currencyCode": "EUR"},
        {"countryCode": "CZ", "countryName": "Czech Republic", "currencyCode": "CZK"},
        {"countryCode": "DK", "countryName": "Denmark", "currencyCode": "DKK"},
        {"countryCode": "EE", "countryName": "Estonia", "currencyCode": "EUR"},
        {"countryCode": "FI", "countryName": "Finland", "currencyCode": "EUR"},
        {"countryCode": "FR", "countryName": "France", "currencyCode": "EUR"},
        {"countryCode": "DE", "countryName": "Germany", "currencyCode": "EUR"},
        {"countryCode": "GR", "countryName": "Greece", "currencyCode": "EUR"},
        {"countryCode": "HU", "countryName": "Hungary", "currencyCode": "EUR"},
        {"countryCode": "IS", "countryName": "Republic of Iceland", "currencyCode": "DEF"},
        {"countryCode": "IE", "countryName": "Ireland", "currencyCode": "EUR"},
        {"countryCode": "IT", "countryName": "Italy", "currencyCode": "EUR"},
        {"countryCode": "LV", "countryName": "Latvia", "currencyCode": "EUR"},
        {"countryCode": "LS", "countryName": "Kingdom of Lesotho", "currencyCode": "DEF"},
        {"countryCode": "LI", "countryName": "Principality of Liechtenstein", "currencyCode": "DEF"},
        {"countryCode": "LT", "countryName": "Lithuania", "currencyCode": "EUR"},
        {"countryCode": "LU", "countryName": "Luxembourg", "currencyCode": "EUR"},
        {"countryCode": "MK", "countryName": "Former Yugoslav Republic of Macedonia", "currencyCode": "DEF"},
        {"countryCode": "MT", "countryName": "Malta", "currencyCode": "EUR"},
        {"countryCode": "ME", "countryName": "Republic of Montenegro", "currencyCode": "DEF"},
        {"countryCode": "MZ", "countryName": "Republic of Mozambique", "currencyCode": "DEF"},
        {"countryCode": "NA", "countryName": "Republic of Namibia", "currencyCode": "DEF"},
        {"countryCode": "NL", "countryName": "Netherlands", "currencyCode": "EUR"},
        {"countryCode": "NO", "countryName": "Norway", "currencyCode": "NOK"},
        {"countryCode": "PL", "countryName": "Poland", "currencyCode": "PLN"},
        {"countryCode": "PT", "countryName": "Portugal", "currencyCode": "EUR"},
        {"countryCode": "RO", "countryName": "Romania", "currencyCode": "EUR"},
        {"countryCode": "RU", "countryName": "Russia", "currencyCode": "RUB"},
        {"countryCode": "RS", "countryName": "Republic of Serbia u0026 Republic of Kosovo", "currencyCode": "DEF"},
        {"countryCode": "SK", "countryName": "Slovak Republic", "currencyCode": "EUR"},
        {"countryCode": "SI", "countryName": "Slovenia", "currencyCode": "EUR"},
        {"countryCode": "ZA", "countryName": "South Africa", "currencyCode": "ZAR"},
        {"countryCode": "ES", "countryName": "Spain", "currencyCode": "EUR"},
        {"countryCode": "SZ", "countryName": "Kingdom of Swaziland", "currencyCode": "DEF"},
        {"countryCode": "SE", "countryName": "Sweden", "currencyCode": "SEK"},
        {"countryCode": "CH", "countryName": "Switzerland", "currencyCode": "CHF"},
        {"countryCode": "GB", "countryName": "United Kingdom", "currencyCode": "GBP"},
        {"countryCode": "AU", "countryName": "Australia", "currencyCode": "AUD"},
        {"countryCode": "NZ", "countryName": "New Zealand", "currencyCode": "NZD"},
        {"countryCode": "TR", "countryName": "Turkey", "currencyCode": "DEF"},
        {"countryCode": "ZM", "countryName": "Republic of Zambia", "currencyCode": "DEF"},
        {"countryCode": "ZW", "countryName": "Republic of Zimbabwe", "currencyCode": "DEF"},
        {"countryCode": "AZ", "countryName": "Azerbaijan", "currencyCode": "DEF"},
        {"countryCode": "MR", "countryName": "Mauritania", "currencyCode": "DEF"},
        {"countryCode": "ML", "countryName": "Mali", "currencyCode": "DEF"},
        {"countryCode": "NE", "countryName": "Niger", "currencyCode": "DEF"},
        {"countryCode": "TD", "countryName": "Chad", "currencyCode": "DEF"},
        {"countryCode": "SD", "countryName": "Sudan", "currencyCode": "DEF"},
        {"countryCode": "ER", "countryName": "Eritrea", "currencyCode": "DEF"},
        {"countryCode": "DJ", "countryName": "Djibouti", "currencyCode": "DEF"},
        {"countryCode": "SO", "countryName": "Somalia", "currencyCode": "DEF"},
        {"countryCode": "AD", "countryName": "Andorra", "currencyCode": "DEF"},
        {"countryCode": "GI", "countryName": "Gibraltar", "currencyCode": "DEF"},
        {"countryCode": "GG", "countryName": "Guernsey", "currencyCode": "DEF"},
        {"countryCode": "IM", "countryName": "Isle of Man", "currencyCode": "DEF"},
        {"countryCode": "JE", "countryName": "Jersey", "currencyCode": "DEF"},
        {"countryCode": "MC", "countryName": "Monaco", "currencyCode": "DEF"},
        {"countryCode": "IN", "countryName": "India", "currencyCode": "DEF"},
        {"countryCode": "SM", "countryName": "San Marino", "currencyCode": "DEF"},
        {"countryCode": "VA", "countryName": "Vatican City", "currencyCode": "DEF"},
        {"countryCode": "KR", "countryName": "Korea", "currencyCode": "KRW"},
        {"countryCode": "TW", "countryName": "Taiwan", "currencyCode": "TWD"},
        {"countryCode": "HK", "countryName": "Hong Kong", "currencyCode": "HKD"},
        {"countryCode": "CN", "countryName": "China", "currencyCode": "CNY"},
        {"countryCode": "IL", "countryName": "State of Israel", "currencyCode": "EUR"},
    ]
    # pylint: enable=line-too-long
    # fmt: on

    assert page.get_country_currency_mapping() == expected_mapping
