from unittest.mock import MagicMock, patch

import pytest
import requests

from app.nintendo.discounts.pages.discount_search_result_page import (
    DiscountSearchResultPage,
)


@pytest.fixture
def discount_info_response():
    response = MagicMock(spec=requests.Response)
    response.text = """
        <body>
            <input id="accessToken" value="12345">
            <input id="discountGroupIds" value="67890">
        </body>
    """
    response.status_code = 200
    return response


@pytest.fixture
def discount_info_response__multiple_discount_group_ids():
    response = MagicMock(spec=requests.Response)
    response.text = """
        <body>
            <input id="accessToken" value="12345">
            <input id="discountGroupIds" value="67890,67891">
        </body>
    """
    response.status_code = 200
    return response


@pytest.fixture
def discount_info__script_document_ready_section(files_based_mock_response):
    return files_based_mock_response([
        "discount-info--script-document-ready-section.html"
    ])


@pytest.fixture
def discount_info__sale_name_section(files_based_mock_response):
    return files_based_mock_response(["discount-info--sale-name-section.html"])


@pytest.fixture
def search_result_content_section(files_based_mock_response):
    return files_based_mock_response(["search-result-content-section.html"])


@pytest.fixture
def search_result_valid_page(files_based_mock_response):
    return files_based_mock_response([
        "discount-search-head-section.html",
        "discount-search-body-section--with-loginid.html",
        "discount-search-form-section.html",
    ])


@pytest.fixture
def search_result_content_no_span_tag_for_loginid(files_based_mock_response):
    return files_based_mock_response([
        "discount-search-head-section.html",
        "discount-search-body-section--no-span-tag-for-loginid.html",
    ])


@pytest.fixture
def search_result_content_no_content_for_loginid(files_based_mock_response):
    return files_based_mock_response([
        "discount-search-head-section.html",
        "discount-search-body-section--no-content-for-loginid.html",
    ])


@pytest.fixture
def discount_search__missing_permissions(files_based_mock_response):
    return files_based_mock_response([
        "discount-search-head-section.html",
        "discount-search-body-section--with-loginid.html",
        "discount-search-body-section--missing-permissions.html",
    ])


@pytest.fixture
def discount_search__login_form(files_based_mock_response):
    return files_based_mock_response([
        "discount-search-head-section.html",
        "discount-search-body-section--login-form.html",
    ])


@pytest.fixture
def get_mocked_result_page():
    def _get(page_number: int, is_last_page: bool):
        mock_page = MagicMock(spec=DiscountSearchResultPage)
        mock_page.get_page_number.return_value = page_number
        mock_page.is_last_page.return_value = is_last_page
        mock_page.get_discount_records.return_value = [
            MagicMock(submission_id=page_number * 3 - 2),
            MagicMock(submission_id=page_number * 3 - 1),
            MagicMock(submission_id=page_number * 3),
        ]
        return mock_page

    return _get


@pytest.fixture
def get_mocked_result_pages(get_mocked_result_page):
    def _get(number_of_pages: int):
        return {
            page_number: get_mocked_result_page(
                page_number, page_number == number_of_pages
            )
            for page_number in range(1, number_of_pages + 1)
        }

    return _get


@pytest.fixture
def api_response_discount_group(files_based_mock_response):
    return files_based_mock_response(["discountGroup-243107.json"])


@pytest.fixture
def api_response_prices(files_based_mock_response):
    return files_based_mock_response(["prices-70010000020724.json"])


@pytest.fixture
def mock_sleep():
    with patch("time.sleep") as _mock_sleep:
        yield _mock_sleep
