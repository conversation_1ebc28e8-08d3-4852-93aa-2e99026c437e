import pytest

from app.microsoft.microsoft_sales import (
    MSApplication,
    _fix_application_id_if_necessary,
)


@pytest.fixture
def ms_application():
    return MSApplication(
        product_id="app1",
        title_name="App1",
        parent_product_id="parent1",
        parent_product_name="ParentApp1",
        in_app=False,
    )


def test_should_not_modify_correct_ids(ms_application):
    input_rows = [
        {"applicationId": "app1", "acquisitionType": "Iap"},
        {"applicationId": "app1", "acquisitionType": "Purchase"},
    ]
    result = _fix_application_id_if_necessary(input_rows, ms_application)
    assert result == input_rows


def test_should_modify_incorrect_ids(ms_application):
    input_rows = [
        {"applicationId": "wrong_id", "acquisitionType": "Iap"},
        {"applicationId": "wrong_id", "acquisitionType": "Purchase"},
    ]
    expected_result = [
        {"applicationId": "parent1", "acquisitionType": "Iap"},
        {"applicationId": "app1", "acquisitionType": "Purchase"},
    ]
    result = _fix_application_id_if_necessary(input_rows, ms_application)
    assert result == expected_result


def test_should_correct_mixed_ids(ms_application):
    input_rows = [
        {"applicationId": "app1", "acquisitionType": "Iap"},
        {"applicationId": "wrong_id", "acquisitionType": "Purchase"},
    ]
    expected_result = [
        {"applicationId": "app1", "acquisitionType": "Iap"},
        {"applicationId": "app1", "acquisitionType": "Purchase"},
    ]
    result = _fix_application_id_if_necessary(input_rows, ms_application)
    assert result == expected_result


def test_should_handle_empty_list(ms_application):
    input_rows = []
    result = _fix_application_id_if_necessary(input_rows, ms_application)
    assert result == input_rows
