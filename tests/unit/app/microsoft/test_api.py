import pytest

from app.core.exceptions import InvalidCredentialsException
from app.microsoft.api import (
    ClientSecretExpiredException,
    InvalidClientIdException,
    InvalidClientSecretException,
    get_microsoft_auth_token_provider,
)
from app.microsoft.models import APISetupData

default_scope = "https://graph.microsoft.com/.default"


def test_should_return_proper_error_for_expired_client_secret(get_credentials):
    ms_credentials = get_credentials("microsoft_store_expired_client_secret")
    api_setup_data_with_expired_client_secret = APISetupData(
        client_id=ms_credentials["apiSetupData"][0]["clientId"],
        tenant_id=ms_credentials["apiSetupData"][0]["tenantId"],
        client_secret=ms_credentials["apiSetupData"][0]["clientSecret"],
        company_name="company_1",
    )

    provider = get_microsoft_auth_token_provider(
        api_setup_data_with_expired_client_secret, default_scope
    )
    with pytest.raises(ClientSecretExpiredException) as exc_info:
        provider()
    assert exc_info.value.error_type == "SECRET_EXPIRED"


def test_should_return_proper_error_for_invalid_client_id(get_credentials):
    ms_credentials = get_credentials("microsoft_store")
    api_setup_data_with_expired_client_secret = APISetupData(
        client_id="INVALID_CLIENT_ID",
        tenant_id=ms_credentials["apiSetupData"][0]["tenantId"],
        client_secret=ms_credentials["apiSetupData"][0]["clientSecret"],
        company_name="company_1",
    )

    provider = get_microsoft_auth_token_provider(
        api_setup_data_with_expired_client_secret, default_scope
    )
    with pytest.raises(InvalidClientIdException) as exc_info:
        provider()
    value = exc_info.value
    assert value.error_type == "INCORRECT_CREDENTIALS"


def test_should_return_proper_error_for_invalid_tenant_id(get_credentials):
    ms_credentials = get_credentials("microsoft_store")
    api_setup_data_with_expired_client_secret = APISetupData(
        client_id=ms_credentials["apiSetupData"][0]["clientId"],
        tenant_id="invalid_tenant_id",
        client_secret=ms_credentials["apiSetupData"][0]["clientSecret"],
        company_name="company_1",
    )

    with pytest.raises(InvalidCredentialsException) as exc_info:
        get_microsoft_auth_token_provider(
            api_setup_data_with_expired_client_secret, default_scope
        )

    value = exc_info.value
    assert value.error_type == "INCORRECT_CREDENTIALS"


def test_should_return_proper_error_for_invalid_client_secret(get_credentials):
    ms_credentials = get_credentials("microsoft_store")
    api_setup_data_with_expired_client_secret = APISetupData(
        client_id=ms_credentials["apiSetupData"][0]["clientId"],
        tenant_id=ms_credentials["apiSetupData"][0]["tenantId"],
        client_secret="invalid",  # noqa: S106
        company_name="company_1",
    )

    provider = get_microsoft_auth_token_provider(
        api_setup_data_with_expired_client_secret, default_scope
    )
    with pytest.raises(InvalidClientSecretException) as exc_info:
        provider()

    value = exc_info.value
    assert value.error_type == "INCORRECT_CREDENTIALS"
