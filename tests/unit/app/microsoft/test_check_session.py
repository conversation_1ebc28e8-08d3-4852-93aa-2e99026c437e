from app.core.types import FeatureFlags
from app.microsoft.microsoft_sales import MicrosoftSalesScraper
from app.microsoft.models import APISetupData, MicrosoftSession


def test_session_identifier_is_sorted(mock_get_microsoft_auth_token_provider):
    session = MicrosoftSession(
        api_setup_data=[
            APISetupData(
                client_id="ZZZ",
                client_secret="secret_1",  # noqa: S106
                company_name="company_1",
                tenant_id="tenant_1",
            ),
            APISetupData(
                client_id="AAA",
                client_secret="secret_1",  # noqa: S106
                company_name="company_1",
                tenant_id="tenant_1",
            ),
        ]
    )
    result = MicrosoftSalesScraper(
        credentials=None, session=session, feature_flags=FeatureFlags(root=[])
    ).check_session()

    assert result.id == "AAA-ZZZ"
