import json
from collections.abc import Iterator
from datetime import date
from http import HTTPStatus
from pathlib import Path
from typing import Any
from unittest.mock import MagicMock, patch
from zipfile import ZipFile

import httpx
import pytest
import respx
import time_machine

from app.core.exceptions import MissingPermissionsException, TemporaryApiIssueException
from app.core.scraper import ReportZip
from app.core.source import Source
from app.core.types import FeatureFlags
from app.microsoft.api import MicrosoftAPIClient
from app.microsoft.microsoft_sales import (
    DEFAULT_QUERY,
    MicrosoftSalesScraper,
    SKUsFetcher,
    scrape_acquisitions,
)
from app.microsoft.models import (
    APISetupData,
    MicrosoftSession,
    MSApplication,
)


@pytest.fixture(autouse=True)
def freeze_time():
    with time_machine.travel("2025-03-04 11:00 +0000"):
        yield


@pytest.fixture(autouse=True)
def mock_sleep_on_microsoft_sales():
    with (
        patch("app.microsoft.microsoft_sales.sleep") as m,
        patch("app.microsoft.api.sleep"),
    ):
        yield m


@pytest.fixture
def respx_mock():
    with respx.mock() as respx_mock:
        yield respx_mock


@pytest.fixture
def fetch_existing_report(respx_mock: respx.MockRouter):
    query_response = {
        "value": [
            {
                "ProductInfo": {
                    "productGroupId": "",
                    "productId": "all",
                    "productIdDbColumnName": "ProductId",
                },
                "queryId": "6e06edcb-d5f6-487f-884a-b41f7212b896",
                "name": "IndieBIAcquisitionV2",
                "description": "Acquisition query creation.",
                "query": "SELECT DateStamp, ProductId, TitleName, ParentProductId, ParentProductName FROM Acquisitions WHERE ProductId IN ('all') ORDER BY DateStamp TIMESPAN LAST_4_YEARS AGGREGATED Monthly",
                "type": "userDefined",
                "user": "117516010",
                "createdTime": "2024-09-18T11:44:34Z",
            }
        ],
        "totalCount": 1,
        "message": "Queries fetched successfully",
        "statusCode": 200,
    }
    respx_mock.get(
        "https://manage.devcenter.microsoft.com/v1.0/my/ScheduledQueries",
        headers={"authorization": "Bearer fake_token"},
    ).respond(json=query_response)


@pytest.fixture
def fetch_no_reports_and_create_new(respx_mock: respx.MockRouter):
    respx_mock.get(
        "https://manage.devcenter.microsoft.com/v1.0/my/ScheduledQueries",
        headers={"authorization": "Bearer fake_token"},
    ).respond(
        json={
            "value": [],
            "totalCount": 0,
            "message": "Queries fetched successfully",
            "statusCode": 200,
        }
    )

    respx_mock.post(
        "https://manage.devcenter.microsoft.com/v1.0/my/ScheduledQueries",
        json={
            "Query": "SELECT DateStamp, ProductId, TitleName, ParentProductId, ParentProductName FROM Acquisitions WHERE ProductId IN ('all') ORDER BY DateStamp TIMESPAN LAST_4_YEARS AGGREGATED Monthly",
            "Name": "IndieBIAcquisitionV2",
            "Description": "Acquisition query creation.",
        },
    ).respond(
        json={
            "value": [
                {
                    "ProductInfo": {
                        "productGroupId": "",
                        "productId": "all",
                        "productIdDbColumnName": "ProductId",
                    },
                    "queryId": "6e06edcb-d5f6-487f-884a-b41f7212b896",
                    "name": "IndieBIAcquisitionV2",
                    "description": "Acquisition query creation.",
                    "query": "SELECT DateStamp, ProductId, TitleName, ParentProductId, ParentProductName FROM Acquisitions WHERE ProductId IN ('all') ORDER BY DateStamp TIMESPAN LAST_4_YEARS AGGREGATED Monthly",
                    "type": "userDefined",
                    "user": "117516010",
                    "createdTime": "2024-09-18T11:44:34Z",
                }
            ],
            "totalCount": 1,
            "message": "Query created successfully",
            "statusCode": 200,
        }
    )


@pytest.fixture
def no_scheduled_reports(respx_mock):
    empty_response = {
        "value": [],
        "totalCount": 0,
        "message": None,
        "statusCode": 200,
    }
    respx_mock.get(
        "https://manage.devcenter.microsoft.com/v1.0/my/ScheduledReport",
        params={"queryId": "6e06edcb-d5f6-487f-884a-b41f7212b896"},
    ).respond(json=empty_response)


@pytest.fixture
def scheduled_reports_that_is_running(respx_mock):
    single_report_response = {
        "value": [
            {
                "productInfo": {
                    "productGroupId": "",
                    "productId": "all",
                    "productIdDbColumnName": "ProductId",
                },
                "reportId": "34f824c9-065f-4272-aed3-87ac3d1f339f",
                "reportName": "IndieBIAcquisitionV2Execution",
                "description": None,
                "queryId": "cf2536ed-279d-4320-a5c3-c159557e7be7",
                "query": "SELECT DateStamp, ProductId, TitleName, ParentProductId, ParentProductName FROM Acquisitions ORDER BY DateStamp DESC TIMESPAN LAST_4_YEARS AGGREGATED Monthly",
                "user": "117516010",
                "createdTime": "2025-03-03T11:09:01Z",
                "modifiedTime": None,
                "executeNow": True,
                "queryStartTime": None,
                "queryEndTime": None,
                "startTime": "2025-03-03T11:09:01Z",
                "reportStatus": "Active",
                "recurrenceInterval": -1,
                "recurrenceCount": 1,
                "callbackUrl": None,
                "callbackMethod": None,
                "format": "csv",
                "endTime": "2025-03-03T11:09:01Z",
                "totalRecurrenceCount": 1,
                "nextExecutionStartTime": None,
            }
        ],
        "totalCount": 1,
        "message": None,
        "statusCode": 200,
    }
    respx_mock.get(
        "https://manage.devcenter.microsoft.com/v1.0/my/ScheduledReport",
        params={"queryId": "6e06edcb-d5f6-487f-884a-b41f7212b896"},
    ).respond(json=single_report_response)

    empy_status = {
        "value": [],
        "totalCount": 0,
        "message": "There are no executions that have occurred for the given filter conditions. Please recheck the reportId or executionId and retry the API after the report''s scheduled execution time",
        "statusCode": 200,
    }

    running_execution_response = {
        "value": [
            {
                "executionId": "038383c0-d779-4ac3-95ad-4ca2a8acd67f",
                "reportId": "34f824c9-065f-4272-aed3-87ac3d1f339f",
                "recurrenceInterval": -1,
                "recurrenceCount": 1,
                "callbackUrl": None,
                "callbackMethod": None,
                "format": "csv",
                "executionStatus": "Running",
                "reportLocation": "NOT THIS URL",
                "reportAccessSecureLink": "https://manage.devcenter.microsoft.com/download_csv?skoid=946869db",
                "reportExpiryTime": None,
                "reportGeneratedTime": "2024-09-19T13:49:38Z",
                "endTime": "2024-09-19T13:30:54Z",
                "totalRecurrenceCount": 1,
                "nextExecutionStartTime": None,
            }
        ],
        "totalCount": 1,
        "message": None,
        "statusCode": 200,
    }

    respx_mock.get(
        "https://manage.devcenter.microsoft.com/v1.0/my/ScheduledReport/execution/34f824c9-065f-4272-aed3-87ac3d1f339f?executionStatus=Completed",
        headers={"authorization": "Bearer fake_token"},
    ).respond(json=empy_status)
    respx_mock.get(
        "https://manage.devcenter.microsoft.com/v1.0/my/ScheduledReport/execution/34f824c9-065f-4272-aed3-87ac3d1f339f?executionStatus=Failed",
        headers={"authorization": "Bearer fake_token"},
    ).respond(json=empy_status)

    respx_mock.get(
        "https://manage.devcenter.microsoft.com/v1.0/my/ScheduledReport/execution/34f824c9-065f-4272-aed3-87ac3d1f339f?executionStatus=Running",
        headers={"authorization": "Bearer fake_token"},
    ).respond(json=running_execution_response)


@pytest.fixture
def expired_scheduled_report(respx_mock):
    single_report_response = {
        "value": [
            {
                "productInfo": {
                    "productGroupId": "",
                    "productId": "all",
                    "productIdDbColumnName": "ProductId",
                },
                "reportId": "34f824c9-065f-4272-aed3-87ac3d1f339f",
                "reportName": "IndieBIAcquisitionV2Execution",
                "description": None,
                "queryId": "cf2536ed-279d-4320-a5c3-c159557e7be7",
                "query": "SELECT DateStamp, ProductId, TitleName, ParentProductId, ParentProductName FROM Acquisitions ORDER BY DateStamp DESC TIMESPAN LAST_4_YEARS AGGREGATED Monthly",
                "user": "117516010",
                "createdTime": "2025-02-21T11:09:01Z",
                "modifiedTime": None,
                "executeNow": True,
                "queryStartTime": None,
                "queryEndTime": None,
                "startTime": "2025-02-21T11:09:01Z",
                "reportStatus": "Active",
                "recurrenceInterval": -1,
                "recurrenceCount": 1,
                "callbackUrl": None,
                "callbackMethod": None,
                "format": "csv",
                "endTime": "2025-02-21T11:09:01Z",
                "totalRecurrenceCount": 1,
                "nextExecutionStartTime": None,
            }
        ],
        "totalCount": 1,
        "message": None,
        "statusCode": 200,
    }
    respx_mock.get(
        "https://manage.devcenter.microsoft.com/v1.0/my/ScheduledReport",
        params={"queryId": "6e06edcb-d5f6-487f-884a-b41f7212b896"},
    ).respond(json=single_report_response)


@pytest.fixture
def schedule_instant_report_execution(respx_mock):
    report_creation_response = {
        "value": [
            {
                "productInfo": {
                    "productGroupId": "",
                    "productId": "all",
                    "productIdDbColumnName": "ProductId",
                },
                "reportId": "34f824c9-065f-4272-aed3-87ac3d1f339f",
                "reportName": "IndieBIAcquisitionV2Execution",
                "description": None,
                "queryId": "6e06edcb-d5f6-487f-884a-b41f7212b896",
                "query": "SELECT DateStamp, ProductId, TitleName, ParentProductId, ParentProductName FROM Acquisitions WHERE ProductId IN ('all') ORDER BY DateStamp TIMESPAN LAST_4_YEARS AGGREGATED Monthly",
                "user": "117516010",
                "createdTime": "2024-09-19T13:30:54Z",
                "modifiedTime": None,
                "executeNow": True,
                "queryStartTime": None,
                "queryEndTime": None,
                "startTime": "2024-09-19T13:30:54Z",
                "reportStatus": "Active",
                "recurrenceInterval": -1,
                "recurrenceCount": 1,
                "callbackUrl": None,
                "callbackMethod": None,
                "format": "csv",
                "endTime": "2024-09-19T13:30:54Z",
                "totalRecurrenceCount": 1,
                "nextExecutionStartTime": None,
            }
        ],
        "totalCount": 1,
        "message": "Report created successfully",
        "statusCode": 200,
    }

    return respx_mock.post(
        "https://manage.devcenter.microsoft.com/v1.0/my/ScheduledReport",
        json={
            "QueryId": "6e06edcb-d5f6-487f-884a-b41f7212b896",
            "ReportName": "IndieBIAcquisitionV2Execution",
            "executeNow": True,
        },
    ).respond(json=report_creation_response)


@pytest.fixture
def wait_for_report_to_finish(respx_mock):
    not_finished_execution_response = {
        "value": [],
        "totalCount": 0,
        "message": "There are no executions that have occurred for the given filter conditions. Please recheck the reportId or executionId and retry the API after the report''s scheduled execution time",
        "statusCode": 200,
    }
    failed_execution_response = {
        "value": [],
        "totalCount": 0,
        "message": "There are no executions that have occurred for the given filter conditions. Please recheck the reportId or executionId and retry the API after the report''s scheduled execution time",
        "statusCode": 200,
    }
    finished_execution_response = {
        "value": [
            {
                "executionId": "038383c0-d779-4ac3-95ad-4ca2a8acd67f",
                "reportId": "34f824c9-065f-4272-aed3-87ac3d1f339f",
                "recurrenceInterval": -1,
                "recurrenceCount": 1,
                "callbackUrl": None,
                "callbackMethod": None,
                "format": "csv",
                "executionStatus": "Completed",
                "reportLocation": "NOT THIS URL",
                "reportAccessSecureLink": "https://manage.devcenter.microsoft.com/download_csv?skoid=946869db",
                "reportExpiryTime": None,
                "reportGeneratedTime": "2024-09-19T13:49:38Z",
                "endTime": "2024-09-19T13:30:54Z",
                "totalRecurrenceCount": 1,
                "nextExecutionStartTime": None,
            }
        ],
        "totalCount": 1,
        "message": None,
        "statusCode": 200,
    }

    # we need to set-up more detailed request firs, because respx is searching for
    # the first matching request and one without query params would take all responses
    respx_mock.get(
        "https://manage.devcenter.microsoft.com/v1.0/my/ScheduledReport/execution/34f824c9-065f-4272-aed3-87ac3d1f339f?executionStatus=Failed",
        headers={"authorization": "Bearer fake_token"},
    ).respond(json=failed_execution_response)

    respx_mock.get(
        "https://manage.devcenter.microsoft.com/v1.0/my/ScheduledReport/execution/34f824c9-065f-4272-aed3-87ac3d1f339f",
        headers={"authorization": "Bearer fake_token"},
    ).mock(
        side_effect=[
            httpx.Response(200, json=not_finished_execution_response),
            httpx.Response(200, json=finished_execution_response),
        ]
    )


@pytest.fixture
def wait_for_report_but_it_failed(respx_mock):
    not_finished_execution_response = {
        "value": [],
        "totalCount": 0,
        "message": "There are no executions that have occurred for the given filter conditions. Please recheck the reportId or executionId and retry the API after the report''s scheduled execution time",
        "statusCode": 200,
    }
    failed_execution_response = {
        "value": [
            {
                "executionId": "038383c0-d779-4ac3-95ad-4ca2a8acd67f",
                "reportId": "34f824c9-065f-4272-aed3-87ac3d1f339f",
                "recurrenceInterval": -1,
                "recurrenceCount": 1,
                "callbackUrl": None,
                "callbackMethod": None,
                "format": "csv",
                "executionStatus": "Failed",
                "reportLocation": "NOT THIS URL",
                "reportAccessSecureLink": "https://manage.devcenter.microsoft.com/download_csv?skoid=946869db",
                "reportExpiryTime": None,
                "reportGeneratedTime": "2024-09-19T13:49:38Z",
                "endTime": "2024-09-19T13:30:54Z",
                "totalRecurrenceCount": 1,
                "nextExecutionStartTime": None,
            }
        ],
        "totalCount": 1,
        "message": None,
        "statusCode": 200,
    }

    # we need to set-up more detailed request firs, because respx is searching for
    # the first matching request and one without query params would take all responses
    respx_mock.get(
        "https://manage.devcenter.microsoft.com/v1.0/my/ScheduledReport/execution/34f824c9-065f-4272-aed3-87ac3d1f339f?executionStatus=Failed",
        headers={"authorization": "Bearer fake_token"},
    ).respond(json=failed_execution_response)

    respx_mock.get(
        "https://manage.devcenter.microsoft.com/v1.0/my/ScheduledReport/execution/34f824c9-065f-4272-aed3-87ac3d1f339f",
        headers={"authorization": "Bearer fake_token"},
    ).respond(json=not_finished_execution_response)


@pytest.fixture
def download_report(respx_mock):
    csv_report = """DateStamp,ProductId,TitleName,ParentProductId,ParentProductName
2024-10-01T00:00:00.000Z,9NV17MJB26PG,SUPERHOT WINDOWS 10,9NV17MJB26PG,SUPERHOT WINDOWS 10
2024-10-01T00:00:00.000Z,BVZ0D05W8MP2,SUPERHOT,BVZ0D05W8MP2,SUPERHOT
2024-10-01T00:00:00.000Z,BVZ0D05W8MP2,SUPERHOT,BVZ0D05W8MP2,SUPERHOT
2024-10-01T00:00:00.000Z,9NRH78B682L8,SUPERHOT: MIND CONTROL DELETE,9NRH78B682L8,SUPERHOT: MIND CONTROL DELETE
2024-10-01T00:00:00.000Z,9NRH78B682L8,SUPERHOT: MIND CONTROL DELETE,9NRH78B682L8,SUPERHOT: MIND CONTROL DELETE
2024-10-01T00:00:00.000Z,XXXXXXXXXXXX,SUPER COINS,BVZ0D05W8MP2,SUPERHOT
2024-10-01T00:00:00.000Z,XXXXXXXXXXXX,SUPER COINS,BVZ0D05W8MP2,SUPERHOT
2020-11-01T00:00:00.000Z,9NV17MJB26PG,SUPERHOT - Windows 10,9NV17MJB26PG,SUPERHOT - Windows 10"""

    respx_mock.get(
        "https://manage.devcenter.microsoft.com/download_csv?skoid=946869db"
    ).respond(text=csv_report)


@pytest.fixture
def fake_auth_microsoft_api_client() -> Iterator[MicrosoftAPIClient]:
    with MicrosoftAPIClient(
        auth_token_provider=lambda: "Bearer fake_token",
        base_url="https://manage.devcenter.microsoft.com/v1.0/my/",
    ) as client:
        yield client


def test_get_application_from_existing_report(
    fake_auth_microsoft_api_client,
    fetch_existing_report,
    no_scheduled_reports,
    schedule_instant_report_execution,
    wait_for_report_to_finish,
    download_report,
    mock_sleep_on_microsoft_sales,
):
    result = SKUsFetcher(
        client=fake_auth_microsoft_api_client, query=DEFAULT_QUERY
    ).get_skus()
    assert result == [
        MSApplication(
            product_id="XXXXXXXXXXXX",
            title_name="SUPER COINS",
            parent_product_id="BVZ0D05W8MP2",
            parent_product_name="SUPERHOT",
            in_app=True,
        ),
        MSApplication(
            product_id="BVZ0D05W8MP2",
            title_name="SUPERHOT",
            parent_product_id="BVZ0D05W8MP2",
            parent_product_name="SUPERHOT",
            in_app=False,
        ),
        MSApplication(
            product_id="9NV17MJB26PG",
            title_name="SUPERHOT WINDOWS 10",
            parent_product_id="9NV17MJB26PG",
            parent_product_name="SUPERHOT WINDOWS 10",
            in_app=False,
        ),
        MSApplication(
            product_id="9NRH78B682L8",
            title_name="SUPERHOT: MIND CONTROL DELETE",
            parent_product_id="9NRH78B682L8",
            parent_product_name="SUPERHOT: MIND CONTROL DELETE",
            in_app=False,
        ),
    ]
    mock_sleep_on_microsoft_sales.assert_called_once_with(30)
    assert schedule_instant_report_execution.call_count == 2, (
        "We need to run schedule report at the beginning and end of fetching SKUs"
    )


def test_get_application_when_there_was_already_running_report(
    fake_auth_microsoft_api_client,
    fetch_existing_report,
    scheduled_reports_that_is_running,
    schedule_instant_report_execution,
    wait_for_report_to_finish,
    download_report,
    mock_sleep_on_microsoft_sales,
):
    result = SKUsFetcher(
        client=fake_auth_microsoft_api_client, query=DEFAULT_QUERY
    ).get_skus()
    assert len(result) == 4

    assert schedule_instant_report_execution.call_count == 1, (
        "We should only run schedule report when there is already running report"
    )


def test_get_application_schedule_new_report_when_all_are_expired(
    fake_auth_microsoft_api_client,
    fetch_existing_report,
    expired_scheduled_report,
    schedule_instant_report_execution,
    wait_for_report_to_finish,
    download_report,
    mock_sleep_on_microsoft_sales,
):
    result = SKUsFetcher(
        client=fake_auth_microsoft_api_client, query=DEFAULT_QUERY
    ).get_skus()
    assert len(result) == 4

    assert schedule_instant_report_execution.call_count == 2


def test_get_application_failed_report_raises_missing_permission(
    fake_auth_microsoft_api_client,
    fetch_existing_report,
    no_scheduled_reports,
    schedule_instant_report_execution,
    wait_for_report_but_it_failed,
    mock_sleep_on_microsoft_sales,
):
    with pytest.raises(MissingPermissionsException):
        SKUsFetcher(
            client=fake_auth_microsoft_api_client, query=DEFAULT_QUERY
        ).get_skus()


def test_get_application_from_new_report(
    fake_auth_microsoft_api_client,
    fetch_no_reports_and_create_new,
    no_scheduled_reports,
    schedule_instant_report_execution,
    wait_for_report_to_finish,
    download_report,
    mock_sleep_on_microsoft_sales,
):
    result = SKUsFetcher(
        client=fake_auth_microsoft_api_client, query=DEFAULT_QUERY
    ).get_skus()
    assert result == [
        MSApplication(
            product_id="XXXXXXXXXXXX",
            title_name="SUPER COINS",
            parent_product_id="BVZ0D05W8MP2",
            parent_product_name="SUPERHOT",
            in_app=True,
        ),
        MSApplication(
            product_id="BVZ0D05W8MP2",
            title_name="SUPERHOT",
            parent_product_id="BVZ0D05W8MP2",
            parent_product_name="SUPERHOT",
            in_app=False,
        ),
        MSApplication(
            product_id="9NV17MJB26PG",
            title_name="SUPERHOT WINDOWS 10",
            parent_product_id="9NV17MJB26PG",
            parent_product_name="SUPERHOT WINDOWS 10",
            in_app=False,
        ),
        MSApplication(
            product_id="9NRH78B682L8",
            title_name="SUPERHOT: MIND CONTROL DELETE",
            parent_product_id="9NRH78B682L8",
            parent_product_name="SUPERHOT: MIND CONTROL DELETE",
            in_app=False,
        ),
    ]


@pytest.fixture
def fetch_app_sales_with_retried_api_limit_exceeded(respx_mock: respx.MockRouter):
    query_response = {
        "statusCode": 429,
        "message": "Rate limit is exceeded. Try again in 1 seconds.",
    }
    respx_mock.get(
        "https://manage.devcenter.microsoft.com/v1.0/my/analytics/acquisitions?applicationId=BVZ0D05W8MP2&startDate=2024-09-29&endDate=2024-10-05&groupby=market%2CapplicationName%2CacquisitionType%2CosVersion%2Cage%2CdeviceType%2Cgender%2CpaymentInstrumentType%2CsandboxId%2CstoreClient%2CxboxTitleId%2ClocalCurrencyCode%2CxboxProductId%2CavailabilityId%2CskuId%2CskuDisplayName%2CxboxParentProductId%2CparentProductName&orderby=date",
        headers={"authorization": "Bearer fake_token"},
    ).respond(json=query_response)

    query_response = {
        "Value": [
            {
                "applicationId": "BVZ0D05W8MP2",
                "applicationName": "SUPERHOT",
                "date": "2024-09-29",
                "acquisitionType": "Paid",
                "age": "25-34",
                "deviceType": "Console-Xbox Series X|S",
                "gender": "Unknown",
                "market": "AU",
                "osVersion": "Windows 11",
                "paymentInstrumentType": "Credit Card",
                "sandboxId": "RETAIL",
                "storeClient": "Microsoft Store (client)",
                "xboxTitleId": "0E4F819A",
                "localCurrencyCode": "AUD",
                "xboxProductId": "0856a106-226c-48cc-a062-fe9fcec37ddb",
                "availabilityId": "B4DRK1FXQK95",
                "skuId": "0001",
                "skuDisplayName": "SUPERHOT",
                "xboxParentProductId": "0DB3E878-1821-4279-91C3-A3C7AFB7D3D8",
                "parentProductName": "SUPERHOT",
                "acquisitionQuantity": 1,
                "purchasePriceUSDAmount": 22.715895000000003,
                "purchasePriceLocalAmount": 33.45,
                "purchaseTaxUSDAmount": 2.064464,
                "purchaseTaxLocalAmount": 3.04,
            }
        ],
        "TotalCount": 1,
        "DataFreshnessTimestamp": "0001-01-01T00:00:00",
    }
    respx_mock.get(
        "https://manage.devcenter.microsoft.com/v1.0/my/analytics/acquisitions?applicationId=BVZ0D05W8MP2&startDate=2024-09-29&endDate=2024-10-05&groupby=market%2CapplicationName%2CacquisitionType%2CosVersion%2Cage%2CdeviceType%2Cgender%2CpaymentInstrumentType%2CsandboxId%2CstoreClient%2CxboxTitleId%2ClocalCurrencyCode%2CxboxProductId%2CavailabilityId%2CskuId%2CskuDisplayName%2CxboxParentProductId%2CparentProductName&orderby=date",
        headers={"authorization": "Bearer fake_token"},
    ).respond(json=query_response)


def test_scrape_acquisitions(
    tmp_path: Path,
    fake_auth_microsoft_api_client: MicrosoftAPIClient,
    fetch_app_sales_with_retried_api_limit_exceeded,
):
    date_from = date(2024, 9, 29)
    date_to = date(2024, 10, 5)
    excluded_skus: list[Any] = []
    applications = [
        MSApplication(
            product_id="BVZ0D05W8MP2",
            title_name="SUPERHOT",
            parent_product_id="BVZ0D05W8MP2",
            parent_product_name="SUPERHOT",
            in_app=False,
        )
    ]

    report_zip = ReportZip[Any](
        report_path=tmp_path,
        source=Source.MICROSOFT_SALES,
        date_from=date_from,
        date_to=date_to,
        manifest_version=3,
    )
    with report_zip:
        scrape_acquisitions(
            client=fake_auth_microsoft_api_client,
            report_zip=report_zip,
            company_name="Client Organization",
            applications=applications,
            date_from=date_from,
            date_to=date_to,
            excluded_skus=excluded_skus,
        )
    expected_zip_path = tmp_path / "microsoft_sales-2024-09-29_2024-10-05.zip"
    assert expected_zip_path.exists()

    created_zip = ZipFile(expected_zip_path)
    report_file_name = (
        "microsoft_sales-2024-09-29-2024-10-05-BVZ0D05W8MP2-CLIENT-ORGANIZATION.json"
    )
    assert created_zip.namelist() == [
        report_file_name,
        "manifest.json",
    ]

    assert json.loads(created_zip.read("manifest.json").decode()) == {
        "dateFrom": "2024-09-29",
        "dateTo": "2024-10-05",
        "fileMetaData": {
            report_file_name: {
                "humanName": "SUPERHOT",
                "skuId": "BVZ0D05W8MP2",
                "parentSkuId": "BVZ0D05W8MP2",
            },
        },
        "manifestVersion": 3,
        "scraperVersion": "0.0.0",
    }
    actual_report = json.loads(created_zip.read(report_file_name).decode())

    assert actual_report == [
        {
            "market": "AU",
            "applicationName": "SUPERHOT",
            "acquisitionType": "Paid",
            "osVersion": "Windows 11",
            "age": "25-34",
            "deviceType": "Console-Xbox Series X|S",
            "gender": "Unknown",
            "paymentInstrumentType": "Credit Card",
            "sandboxId": "RETAIL",
            "storeClient": "Microsoft Store (client)",
            "xboxTitleId": "0E4F819A",
            "localCurrencyCode": "AUD",
            "xboxProductId": "0856a106-226c-48cc-a062-fe9fcec37ddb",
            "availabilityId": "B4DRK1FXQK95",
            "skuId": "0001",
            "skuDisplayName": "SUPERHOT",
            "xboxParentProductId": "0DB3E878-1821-4279-91C3-A3C7AFB7D3D8",
            "parentProductName": "SUPERHOT",
            "applicationId": "BVZ0D05W8MP2",
            "date": "2024-09-29",
            "acquisitionQuantity": 1,
            "purchasePriceUSDAmount": 22.715895000000003,
            "purchasePriceLocalAmount": 33.45,
            "purchaseTaxUSDAmount": 2.064464,
            "purchaseTaxLocalAmount": 3.04,
            "inAppProductName": "",
        }
    ]


@pytest.fixture
def fetch_app_sales_with_retried_api_timeout_exceeded(respx_mock: respx.MockRouter):
    query_response = {
        "statusCode": 504,
        "text": "<html><head><title>504 Gateway Time-out</title></head><body><center><h1>504 Gateway Time-out</h1></center><hr><center>Microsoft-Azure-Application-Gateway/v2</center></body></html>",
    }
    respx_mock.get(
        "https://manage.devcenter.microsoft.com/v1.0/my/analytics/acquisitions?applicationId=BVZ0D05W8MP2&startDate=2024-09-29&endDate=2024-10-05&groupby=market%2CapplicationName%2CacquisitionType%2CosVersion%2Cage%2CdeviceType%2Cgender%2CpaymentInstrumentType%2CsandboxId%2CstoreClient%2CxboxTitleId%2ClocalCurrencyCode%2CxboxProductId%2CavailabilityId%2CskuId%2CskuDisplayName%2CxboxParentProductId%2CparentProductName&orderby=date",
        headers={"authorization": "Bearer fake_token"},
    ).respond(json=query_response)

    query_response = {
        "Value": [
            {
                "applicationId": "BVZ0D05W8MP2",
                "applicationName": "SUPERHOT",
                "date": "2024-09-29",
                "acquisitionType": "Paid",
                "age": "25-34",
                "deviceType": "Console-Xbox Series X|S",
                "gender": "Unknown",
                "market": "AU",
                "osVersion": "Windows 11",
                "paymentInstrumentType": "Credit Card",
                "sandboxId": "RETAIL",
                "storeClient": "Microsoft Store (client)",
                "xboxTitleId": "0E4F819A",
                "localCurrencyCode": "AUD",
                "xboxProductId": "0856a106-226c-48cc-a062-fe9fcec37ddb",
                "availabilityId": "B4DRK1FXQK95",
                "skuId": "0001",
                "skuDisplayName": "SUPERHOT",
                "xboxParentProductId": "0DB3E878-1821-4279-91C3-A3C7AFB7D3D8",
                "parentProductName": "SUPERHOT",
                "acquisitionQuantity": 1,
                "purchasePriceUSDAmount": 22.715895000000003,
                "purchasePriceLocalAmount": 33.45,
                "purchaseTaxUSDAmount": 2.064464,
                "purchaseTaxLocalAmount": 3.04,
            }
        ],
        "TotalCount": 1,
        "DataFreshnessTimestamp": "0001-01-01T00:00:00",
    }
    respx_mock.get(
        "https://manage.devcenter.microsoft.com/v1.0/my/analytics/acquisitions?applicationId=BVZ0D05W8MP2&startDate=2024-09-29&endDate=2024-10-05&groupby=market%2CapplicationName%2CacquisitionType%2CosVersion%2Cage%2CdeviceType%2Cgender%2CpaymentInstrumentType%2CsandboxId%2CstoreClient%2CxboxTitleId%2ClocalCurrencyCode%2CxboxProductId%2CavailabilityId%2CskuId%2CskuDisplayName%2CxboxParentProductId%2CparentProductName&orderby=date",
        headers={"authorization": "Bearer fake_token"},
    ).respond(json=query_response)


def test_scrape_acquisitions_with_timeout_retry(
    tmp_path: Path,
    fake_auth_microsoft_api_client: MicrosoftAPIClient,
    fetch_app_sales_with_retried_api_timeout_exceeded,
):
    date_from = date(2024, 9, 29)
    date_to = date(2024, 10, 5)
    excluded_skus: list[Any] = []
    applications = [
        MSApplication(
            product_id="BVZ0D05W8MP2",
            title_name="SUPERHOT",
            parent_product_id="BVZ0D05W8MP2",
            parent_product_name="SUPERHOT",
            in_app=False,
        )
    ]

    report_zip = ReportZip[Any](
        report_path=tmp_path,
        source=Source.MICROSOFT_SALES,
        date_from=date_from,
        date_to=date_to,
    )
    with report_zip:
        scrape_acquisitions(
            client=fake_auth_microsoft_api_client,
            report_zip=report_zip,
            company_name="Client Organization",
            applications=applications,
            date_from=date_from,
            date_to=date_to,
            excluded_skus=excluded_skus,
        )
    expected_zip_path = tmp_path / "microsoft_sales-2024-09-29_2024-10-05.zip"

    created_zip = ZipFile(expected_zip_path)
    report_file_name = (
        "microsoft_sales-2024-09-29-2024-10-05-BVZ0D05W8MP2-CLIENT-ORGANIZATION.json"
    )
    actual_report = json.loads(created_zip.read(report_file_name).decode())

    assert actual_report == [
        {
            "market": "AU",
            "applicationName": "SUPERHOT",
            "acquisitionType": "Paid",
            "osVersion": "Windows 11",
            "age": "25-34",
            "deviceType": "Console-Xbox Series X|S",
            "gender": "Unknown",
            "paymentInstrumentType": "Credit Card",
            "sandboxId": "RETAIL",
            "storeClient": "Microsoft Store (client)",
            "xboxTitleId": "0E4F819A",
            "localCurrencyCode": "AUD",
            "xboxProductId": "0856a106-226c-48cc-a062-fe9fcec37ddb",
            "availabilityId": "B4DRK1FXQK95",
            "skuId": "0001",
            "skuDisplayName": "SUPERHOT",
            "xboxParentProductId": "0DB3E878-1821-4279-91C3-A3C7AFB7D3D8",
            "parentProductName": "SUPERHOT",
            "applicationId": "BVZ0D05W8MP2",
            "date": "2024-09-29",
            "acquisitionQuantity": 1,
            "purchasePriceUSDAmount": 22.715895000000003,
            "purchasePriceLocalAmount": 33.45,
            "purchaseTaxUSDAmount": 2.064464,
            "purchaseTaxLocalAmount": 3.04,
            "inAppProductName": "",
        }
    ]


@pytest.fixture
def fetch_app_sales_with_unsuccessful_retried_api_timeout_exceeded(
    respx_mock: respx.MockRouter,
):
    query_response = {
        "statusCode": 504,
        "text": "<html><head><title>504 Gateway Time-out</title></head><body><center><h1>504 Gateway Time-out</h1></center><hr><center>Microsoft-Azure-Application-Gateway/v2</center></body></html>",
    }
    respx_mock.get(
        "https://manage.devcenter.microsoft.com/v1.0/my/analytics/acquisitions?applicationId=BVZ0D05W8MP2&startDate=2024-09-29&endDate=2024-10-05&groupby=market%2CapplicationName%2CacquisitionType%2CosVersion%2Cage%2CdeviceType%2Cgender%2CpaymentInstrumentType%2CsandboxId%2CstoreClient%2CxboxTitleId%2ClocalCurrencyCode%2CxboxProductId%2CavailabilityId%2CskuId%2CskuDisplayName%2CxboxParentProductId%2CparentProductName&orderby=date",
        headers={"authorization": "Bearer fake_token"},
    ).respond(json=query_response)

    respx_mock.get(
        "https://manage.devcenter.microsoft.com/v1.0/my/analytics/acquisitions?applicationId=BVZ0D05W8MP2&startDate=2024-09-29&endDate=2024-10-05&groupby=market%2CapplicationName%2CacquisitionType%2CosVersion%2Cage%2CdeviceType%2Cgender%2CpaymentInstrumentType%2CsandboxId%2CstoreClient%2CxboxTitleId%2ClocalCurrencyCode%2CxboxProductId%2CavailabilityId%2CskuId%2CskuDisplayName%2CxboxParentProductId%2CparentProductName&orderby=date",
        headers={"authorization": "Bearer fake_token"},
    ).respond(json=query_response)


def test_scrape_acquisitions_with_unsuccessful_retried_api_timeout_exceeded(
    tmp_path: Path,
    fake_auth_microsoft_api_client: MicrosoftAPIClient,
    fetch_app_sales_with_unsuccessful_retried_api_timeout_exceeded,
    caplog,
):
    date_from = date(2024, 9, 29)
    date_to = date(2024, 10, 5)
    excluded_skus: list[Any] = []
    applications = [
        MSApplication(
            product_id="BVZ0D05W8MP2",
            title_name="SUPERHOT",
            parent_product_id="BVZ0D05W8MP2",
            parent_product_name="SUPERHOT",
            in_app=False,
        )
    ]

    report_zip = ReportZip[Any](
        report_path=tmp_path,
        source=Source.MICROSOFT_SALES,
        date_from=date_from,
        date_to=date_to,
    )
    with pytest.raises(TemporaryApiIssueException), report_zip:
        scrape_acquisitions(
            client=fake_auth_microsoft_api_client,
            report_zip=report_zip,
            company_name="Client Organization",
            applications=applications,
            date_from=date_from,
            date_to=date_to,
            excluded_skus=excluded_skus,
        )
    assert (
        "Unknown response from API human_name='SUPERHOT' sku_id='BVZ0D05W8MP2' parent_sku_id='BVZ0D05W8MP2'; Full response: {'statusCode': 504, 'text': '<html><head><title>504 Gateway Time-out</title></head><body><center><h1>504 Gateway Time-out</h1></center><hr><center>Microsoft-Azure-Application-Gateway/v2</center></body></html>'"
        in caplog.text
    )


@pytest.fixture
def fetch_app_sales_with_unsupported_response(respx_mock: respx.MockRouter):
    query_response = {
        "something": "not supported",
    }
    respx_mock.get(
        "https://manage.devcenter.microsoft.com/v1.0/my/analytics/acquisitions?applicationId=BVZ0D05W8MP2&startDate=2024-09-29&endDate=2024-10-05&groupby=market%2CapplicationName%2CacquisitionType%2CosVersion%2Cage%2CdeviceType%2Cgender%2CpaymentInstrumentType%2CsandboxId%2CstoreClient%2CxboxTitleId%2ClocalCurrencyCode%2CxboxProductId%2CavailabilityId%2CskuId%2CskuDisplayName%2CxboxParentProductId%2CparentProductName&orderby=date",
        headers={"authorization": "Bearer fake_token"},
    ).respond(json=query_response)


def test_scrape_acquisitions_with_not_supported_json_content(
    tmp_path: Path,
    fake_auth_microsoft_api_client: MicrosoftAPIClient,
    fetch_app_sales_with_unsupported_response,
    caplog,
):
    date_from = date(2024, 9, 29)
    date_to = date(2024, 10, 5)
    excluded_skus: list[Any] = []
    applications = [
        MSApplication(
            product_id="BVZ0D05W8MP2",
            title_name="SUPERHOT",
            parent_product_id="BVZ0D05W8MP2",
            parent_product_name="SUPERHOT",
            in_app=False,
        )
    ]

    report_zip = ReportZip[Any](
        report_path=tmp_path,
        source=Source.MICROSOFT_SALES,
        date_from=date_from,
        date_to=date_to,
    )
    with pytest.raises(TemporaryApiIssueException), report_zip:
        scrape_acquisitions(
            client=fake_auth_microsoft_api_client,
            report_zip=report_zip,
            company_name="Client Organization",
            applications=applications,
            date_from=date_from,
            date_to=date_to,
            excluded_skus=excluded_skus,
        )
    assert (
        "Unknown response from API human_name='SUPERHOT' sku_id='BVZ0D05W8MP2' parent_sku_id='BVZ0D05W8MP2'; Full response: {'something': 'not supported'}"
        in caplog.text
    )


@pytest.fixture
def fetch_app_sales_with_non_jsonable_response(respx_mock: respx.MockRouter):
    status = HTTPStatus.OK
    resp_text = "This is not a JSON"
    respx_mock.get(
        "https://manage.devcenter.microsoft.com/v1.0/my/analytics/acquisitions?applicationId=BVZ0D05W8MP2&startDate=2024-09-29&endDate=2024-10-05&groupby=market%2CapplicationName%2CacquisitionType%2CosVersion%2Cage%2CdeviceType%2Cgender%2CpaymentInstrumentType%2CsandboxId%2CstoreClient%2CxboxTitleId%2ClocalCurrencyCode%2CxboxProductId%2CavailabilityId%2CskuId%2CskuDisplayName%2CxboxParentProductId%2CparentProductName&orderby=date",
        headers={"authorization": "Bearer fake_token"},
    ).respond(
        status_code=status,
        text=resp_text,
    )


def test_scrape_acquisitions_with_non_jsonable_content(
    tmp_path: Path,
    fake_auth_microsoft_api_client: MicrosoftAPIClient,
    fetch_app_sales_with_non_jsonable_response,
    caplog,
):
    date_from = date(2024, 9, 29)
    date_to = date(2024, 10, 5)
    excluded_skus: list[Any] = []
    applications = [
        MSApplication(
            product_id="BVZ0D05W8MP2",
            title_name="SUPERHOT",
            parent_product_id="BVZ0D05W8MP2",
            parent_product_name="SUPERHOT",
            in_app=False,
        )
    ]

    report_zip = ReportZip[Any](
        report_path=tmp_path,
        source=Source.MICROSOFT_SALES,
        date_from=date_from,
        date_to=date_to,
    )
    with pytest.raises(TemporaryApiIssueException), report_zip:
        scrape_acquisitions(
            client=fake_auth_microsoft_api_client,
            report_zip=report_zip,
            company_name="Client Organization",
            applications=applications,
            date_from=date_from,
            date_to=date_to,
            excluded_skus=excluded_skus,
        )
    assert (
        "Failed to decode JSON response for SKU human_name='SUPERHOT' sku_id='BVZ0D05W8MP2' parent_sku_id='BVZ0D05W8MP2'; Status code: 200; Response text: This is not a JSON"
        in caplog.text
    )


@patch("app.microsoft.microsoft_sales.SKUsFetcher.get_skus")
def test_scraper_reports_has_data_when_ony_on_organization_has_data(
    get_skus_mock: MagicMock,
    tmp_path: Path,
    mock_get_microsoft_auth_token_provider,
    fetch_app_sales_with_retried_api_limit_exceeded,
):
    session = MicrosoftSession(
        api_setup_data=[
            APISetupData(
                client_id="ZZZ",
                client_secret="secret_1",  # noqa: S106
                company_name="company_1",
                tenant_id="tenant_1",
            ),
            APISetupData(
                client_id="AAA",
                client_secret="secret_1",  # noqa: S106
                company_name="company_1",
                tenant_id="tenant_1",
            ),
        ],
    )

    aaa_aps: list[MSApplication] = [
        MSApplication(
            product_id="BVZ0D05W8MP2",
            title_name="SUPERHOT",
            parent_product_id="BVZ0D05W8MP2",
            parent_product_name="SUPERHOT",
            in_app=False,
        )
    ]
    zzz_aps: list[MSApplication] = []

    get_skus_mock.side_effect = [aaa_aps, zzz_aps]

    date_from = date(2024, 9, 29)
    date_to = date(2024, 10, 5)
    report_zip = ReportZip[Any](
        report_path=tmp_path,
        source=Source.MICROSOFT_SALES,
        date_from=date_from,
        date_to=date_to,
    )
    with report_zip:
        result = MicrosoftSalesScraper(
            credentials=None, session=session, feature_flags=FeatureFlags(root=[])
        ).scrape(from_=date_from, to=date_to, report_zip=report_zip, excluded_skus=[])
    assert result.no_data == False


@pytest.fixture
def fetch_in_app_sales_with_no_application_id(respx_mock: respx.MockRouter):
    query_response = {
        "Value": [
            {
                "applicationName": "SUPER COINS",
                "date": "2024-09-29",
                "acquisitionType": "Iap",
                "age": "25-34",
                "deviceType": "Console-Xbox Series X|S",
                "gender": "Unknown",
                "market": "AU",
                "osVersion": "Windows 11",
                "paymentInstrumentType": "Credit Card",
                "sandboxId": "RETAIL",
                "storeClient": "Microsoft Store (client)",
                "xboxTitleId": "0E4F819A",
                "localCurrencyCode": "AUD",
                "xboxProductId": "0856a106-226c-48cc-a062-fe9fcec37ddb",
                "availabilityId": "B4DRK1FXQK95",
                "skuId": "0001",
                "skuDisplayName": "SUPER COINS",
                "xboxParentProductId": "0DB3E878-1821-4279-91C3-A3C7AFB7D3D8",
                "parentProductName": "SUPERHOT",
                "acquisitionQuantity": 1,
                "purchasePriceUSDAmount": 22.715895000000003,
                "purchasePriceLocalAmount": 33.45,
                "purchaseTaxUSDAmount": 2.064464,
                "purchaseTaxLocalAmount": 3.04,
            }
        ],
        "TotalCount": 1,
        "DataFreshnessTimestamp": "0001-01-01T00:00:00",
    }
    respx_mock.get(
        "https://manage.devcenter.microsoft.com/v1.0/my/analytics/acquisitions?applicationId=XXXXXXXXXXXX&startDate=2024-09-29&endDate=2024-10-05&groupby=market%2CapplicationName%2CacquisitionType%2CosVersion%2Cage%2CdeviceType%2Cgender%2CpaymentInstrumentType%2CsandboxId%2CstoreClient%2CxboxTitleId%2ClocalCurrencyCode%2CxboxProductId%2CavailabilityId%2CskuId%2CskuDisplayName%2CxboxParentProductId%2CparentProductName%2CinAppProductName&orderby=date",
        headers={"authorization": "Bearer fake_token"},
    ).respond(json=query_response)


def test_scrape_iap_acquisitions_without_application_id(
    tmp_path: Path,
    fake_auth_microsoft_api_client: MicrosoftAPIClient,
    fetch_in_app_sales_with_no_application_id,
):
    date_from = date(2024, 9, 29)
    date_to = date(2024, 10, 5)
    excluded_skus: list[Any] = []
    applications = [
        MSApplication(
            product_id="XXXXXXXXXXXX",
            title_name="SUPER COINS",
            parent_product_id="BVZ0D05W8MP2",
            parent_product_name="SUPERHOT",
            in_app=True,
        ),
    ]

    report_zip = ReportZip[Any](
        report_path=tmp_path,
        source=Source.MICROSOFT_SALES,
        date_from=date_from,
        date_to=date_to,
    )
    with report_zip:
        scrape_acquisitions(
            client=fake_auth_microsoft_api_client,
            report_zip=report_zip,
            company_name="Client Organization",
            applications=applications,
            date_from=date_from,
            date_to=date_to,
            excluded_skus=excluded_skus,
        )
    expected_zip_path = tmp_path / "microsoft_sales-2024-09-29_2024-10-05.zip"

    created_zip = ZipFile(expected_zip_path)
    report_file_name = (
        "microsoft_sales-2024-09-29-2024-10-05-XXXXXXXXXXXX-CLIENT-ORGANIZATION.json"
    )
    actual_report = json.loads(created_zip.read(report_file_name).decode())

    assert actual_report == [
        {
            "market": "AU",
            "applicationName": "SUPER COINS",
            "acquisitionType": "Iap",
            "osVersion": "Windows 11",
            "age": "25-34",
            "deviceType": "Console-Xbox Series X|S",
            "gender": "Unknown",
            "paymentInstrumentType": "Credit Card",
            "sandboxId": "RETAIL",
            "storeClient": "Microsoft Store (client)",
            "xboxTitleId": "0E4F819A",
            "localCurrencyCode": "AUD",
            "xboxProductId": "0856a106-226c-48cc-a062-fe9fcec37ddb",
            "availabilityId": "B4DRK1FXQK95",
            "skuId": "0001",
            "skuDisplayName": "SUPER COINS",
            "xboxParentProductId": "0DB3E878-1821-4279-91C3-A3C7AFB7D3D8",
            "parentProductName": "SUPERHOT",
            "applicationId": "BVZ0D05W8MP2",
            "date": "2024-09-29",
            "acquisitionQuantity": 1,
            "purchasePriceUSDAmount": 22.715895000000003,
            "purchasePriceLocalAmount": 33.45,
            "purchaseTaxUSDAmount": 2.064464,
            "purchaseTaxLocalAmount": 3.04,
            "inAppProductName": "",
        }
    ]


def test_setting_no_feature_flag_will_use_default_query():
    scraper = MicrosoftSalesScraper(
        credentials=None,
        session=None,
        feature_flags=FeatureFlags(root=[]),
    )

    assert scraper._query.name == "IndieBIAcquisitionV2"


def test_setting_feature_flag_will_change_query():
    feature_flag = "microsoft-sales-sku-report-range-2y"
    scraper = MicrosoftSalesScraper(
        credentials=None,
        session=None,
        feature_flags=FeatureFlags(root=[feature_flag]),
    )

    assert scraper._query.name == "IndieBIAcquisitionV22Y"
