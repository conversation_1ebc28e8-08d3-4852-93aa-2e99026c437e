# Standard libraries
from datetime import datetime

# Third party libraries
import pytest

# Current project files
from app.util.daterange import (
    first_day_of_date,
    google_date_range,
    last_day_of_date,
)


@pytest.mark.parametrize(
    ("input_date_from", "input_date_to", "expected_result"),
    [
        (
            datetime.strptime("2021-04-05", "%Y-%m-%d").date(),
            datetime.strptime("2021-6-17", "%Y-%m-%d").date(),
            ["202104", "202105", "202106"],
        ),
        (
            datetime.strptime("2020-01-01", "%Y-%m-%d").date(),
            datetime.strptime("2020-1-1", "%Y-%m-%d").date(),
            ["202001"],
        ),
        (
            datetime.strptime("2023-02-27", "%Y-%m-%d").date(),
            datetime.strptime("2023-12-08", "%Y-%m-%d").date(),
            [
                "202302",
                "202303",
                "202304",
                "202305",
                "202306",
                "202307",
                "202308",
                "202309",
                "202310",
                "202311",
                "202312",
            ],
        ),
        (
            datetime.strptime("2023-12-27", "%Y-%m-%d").date(),
            datetime.strptime("2024-3-18", "%Y-%m-%d").date(),
            ["202312", "202401", "202402", "202403"],
        ),
        (
            datetime.strptime("2023-12-27", "%Y-%m-%d").date(),
            datetime.strptime("2024-2-29", "%Y-%m-%d").date(),
            ["202312", "202401", "202402"],
        ),
    ],
)
def test_google_date_range_returns_correct_list(
    input_date_from, input_date_to, expected_result
):
    result = google_date_range(date_from=input_date_from, date_to=input_date_to)
    assert result == expected_result


@pytest.mark.parametrize(
    ("input_date", "expected_result"),
    [
        (datetime(2021, 4, 15).date(), datetime(2021, 4, 1).date()),
        (datetime(2023, 12, 31).date(), datetime(2023, 12, 1).date()),
        (datetime(2017, 1, 1).date(), datetime(2017, 1, 1).date()),
        (datetime(2019, 2, 28).date(), datetime(2019, 2, 1).date()),
        (datetime(2020, 2, 29).date(), datetime(2020, 2, 1).date()),
    ],
)
def test_first_day_of_date_returns_correct_date(input_date, expected_result):
    result = first_day_of_date(date_=input_date)
    assert result == expected_result


@pytest.mark.parametrize(
    ("input_date", "expected_result"),
    [
        (datetime(2021, 4, 15).date(), datetime(2021, 4, 30).date()),
        (datetime(2023, 12, 31).date(), datetime(2023, 12, 31).date()),
        (datetime(2017, 1, 1).date(), datetime(2017, 1, 31).date()),
        (datetime(2019, 2, 28).date(), datetime(2019, 2, 28).date()),
        (datetime(2020, 2, 29).date(), datetime(2020, 2, 29).date()),
    ],
)
def test_last_day_of_date_returns_correct_date(input_date, expected_result):
    result = last_day_of_date(date_=input_date)
    assert result == expected_result
