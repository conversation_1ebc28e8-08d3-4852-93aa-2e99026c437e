from app.core.types import CSV
from app.util.csv import to_raw_csv


def test_to_raw_csv_basic():
    csv_data: CSV = {
        "headers": ["Name", "Age"],
        "rows": [{"Name": "<PERSON>", "Age": 30}, {"Name": "<PERSON>", "Age": 25}],
    }
    assert to_raw_csv(csv_data) == "Name,Age\r\nAlice,30\r\nBob,25\r\n"


def test_to_raw_csv_with_varied_data_types():
    csv_data: CSV = {
        "headers": ["Name", "Age", "Salary", "Active"],
        "rows": [
            {"Name": "Alice", "Age": 30, "Salary": 55000.5, "Active": True},
            {"Name": "Bob", "Age": 25, "Salary": 48000.0, "Active": False},
        ],
    }
    expected_output = (
        "Name,Age,Salary,Active\r\nAlice,30,55000.5,True\r\nBob,25,48000.0,False\r\n"
    )
    assert to_raw_csv(csv_data) == expected_output


def test_to_raw_csv_empty_data():
    assert to_raw_csv({"headers": [], "rows": []}) == "\r\n"
