import pytest
import time_machine

from app.core.exceptions import TemporaryApiIssueException
from app.core.scraper import SessionIdentifier
from app.util.messaging import send_exception, send_result


@pytest.fixture(autouse=True)
def _set_fixed_time():
    with time_machine.travel("2023-01-01T00:00:00.000Z", tick=False):
        yield


def test_send_result_full(get_all_logs, any_int):
    login_result = SessionIdentifier(
        id="example", has_scrape_blocking_issues=False
    ).model_dump(by_alias=True)

    send_result(login_result)

    expected_result_log = [
        {
            "timestamp": "2023-01-01T00:00:00.000Z",
            "metadata": {
                "function_name": "send_result",
                "line_no": any_int,
                "logger_name": "app.util.messaging",
                "level_name": "INFO",
            },
            "message": "",
            "version": 2,
            "type": "result",
            "originId": "Scraper-py-0.0.0",
            "logLevel": 1,
            "data": {
                "id": "example",
                "hasScrapeBlockingIssues": False,
            },
        }
    ]

    assert get_all_logs() == expected_result_log


def test_send_exception_full(any_int, get_all_logs):
    try:
        raise TemporaryApiIssueException("Example exception")
    except Exception as e:
        send_exception(e)

    expected_result = {
        "timestamp": "2023-01-01T00:00:00.000Z",
        "metadata": {
            "function_name": "send_exception",
            "line_no": any_int,
            "logger_name": "app.util.messaging",
            "level_name": "ERROR",
        },
        "message": "app.core.exceptions.TemporaryApiIssueException: Example exception",
        "version": 2,
        "type": "error",
        "originId": "Scraper-py-0.0.0",
        "logLevel": 3,
        "errorType": "TEMPORARY_PORTAL_ISSUE",
        "data": {
            "message": "Example exception",  # we are testing stack in a separate assertion
        },
    }

    all_logs = get_all_logs()
    stack = all_logs[1]["data"].pop("stack")
    assert all_logs[1] == expected_result

    expected_stack = ' in test_send_exception_full\n    raise TemporaryApiIssueException("Example exception")\n'

    assert stack.endswith(expected_stack)

    assert all_logs[0] == {
        "timestamp": "2023-01-01T00:00:00.000Z",
        "metadata": {
            "function_name": "send_exception",
            "line_no": any_int,
            "logger_name": "app.util.messaging",
            "level_name": "ERROR",
        },
        "message": "Example exception",
        "errorType": "TEMPORARY_PORTAL_ISSUE",
        "type": "output",
        "logLevel": 3,
        "originId": "Scraper-py-0.0.0",
        "version": 2,
    }
