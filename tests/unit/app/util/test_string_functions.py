# Standard libraries
from datetime import date

# Third party libraries
import pytest

# Current project files
from app.core.source import Source
from app.util.string_functions import (
    crypt_text,
    format_date,
    generate_report_file_name,
    remove_quotation,
)


@pytest.mark.parametrize(
    ("input_date", "expected_string"),
    [
        (date(2001, 12, 16), "2001-12-16"),
        (date(2012, 7, 6), "2012-07-06"),
        (date(2023, 4, 25), "2023-04-25"),
    ],
)
def test_format_date_returns_expected_string_date(input_date, expected_string):
    assert format_date(date_=input_date) == expected_string


def test_generate_report_file_name_returns_correct_string_without_suffix():
    new_name = generate_report_file_name(
        source=Source("epic_sales"),
        start_date=date(2023, 4, 23),
        end_date=date(2023, 4, 25),
        extension="bmp",
    )
    assert new_name == "epic_sales-2023-04-23-2023-04-25.bmp"


def test_generate_report_file_name_returns_correct_string_with_suffix():
    new_name = generate_report_file_name(
        source=Source("epic_sales"),
        start_date=date(2023, 4, 23),
        end_date=date(2023, 4, 25),
        extension="bmp",
        suffix="PRO",
    )
    assert new_name == "epic_sales-2023-04-23-2023-04-25-PRO.bmp"


@pytest.mark.parametrize(
    ("text_with_quotes", "clear_text"),
    [
        (
            '"I am sure it "should be" removed from §this `text`.',
            'I am sure it "should be" removed from §this `text`.',
        ),
        (
            '"I am sure it "should be" removed from §this `text`',
            'I am sure it "should be" removed from §this `text',
        ),
        (
            'I am sure it "should be" removed from §this `text`.\'',
            'I am sure it "should be" removed from §this `text`.',
        ),
    ],
)
def test_remove_quotation_returns_string_without_quotes_characters_on_edges(
    text_with_quotes, clear_text
):
    assert clear_text == remove_quotation(text=text_with_quotes)


def test_crypt_text_is_able_to_crypt_and_decrypt_string(plaintext):
    encrypted_text = crypt_text(text=plaintext)
    assert crypt_text(text=encrypted_text, encrypt=False) == plaintext


def test_crypt_text_is_able_to_crypt_and_decrypt_bytes(plaintext):
    encrypted_text_bytes = crypt_text(text=plaintext.encode()).encode()
    assert crypt_text(text=encrypted_text_bytes, encrypt=False) == plaintext
