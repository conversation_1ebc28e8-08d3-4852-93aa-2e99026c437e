# Standard libraries
import os
from unittest import mock

# Third party libraries
import pytest


@pytest.fixture
def plaintext():
    return "This is very important text to check how crypt function works."


@pytest.fixture(autouse=True)
def mock_settings_env_vars():
    fake_key = "8pmZ0aZwhi5Kr3vutCmjpE9EPx-11aFo4_KnVH_D90k="
    with mock.patch.dict(os.environ, {"SCP_CREDENTIALS_ENCRYPTION_KEY": fake_key}):
        yield
