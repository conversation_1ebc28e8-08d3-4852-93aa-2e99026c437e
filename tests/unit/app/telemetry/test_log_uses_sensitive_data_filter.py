import logging
from json import dumps

from app.logs import add_filter_to_all_loggers
from app.util.logging.telemetry_type_filter import FilterSensitiveInfo

log = logging.getLogger(__name__)

redacted_message = "[REDACTED]"
unredacted_message = "safe"
password = "password"


def test_log_info_uses_sensitive_data_filter(caplog):
    add_filter_to_all_loggers(FilterSensitiveInfo())

    log.info('Houston, we have a %s", password')
    log.info(unredacted_message)

    log_dump = dumps(caplog.messages)

    assert password not in log_dump
    assert [redacted_message, unredacted_message] == caplog.messages


def test_log_warn_uses_sensitive_data_filter(caplog):
    add_filter_to_all_loggers(FilterSensitiveInfo())

    log.warning('Houston, we have a %s", password')
    log.warning(unredacted_message)

    log_dump = dumps(caplog.messages)

    assert password not in log_dump
    assert [redacted_message, unredacted_message] == caplog.messages


def test_log_error_uses_sensitive_data_filter(caplog):
    add_filter_to_all_loggers(FilterSensitiveInfo())

    log.error('Houston, we have a %s", password')
    log.error(unredacted_message)

    log_dump = dumps(caplog.messages)

    assert password not in log_dump
    assert [redacted_message, unredacted_message] == caplog.messages


def test_log_exception_uses_sensitive_data_filter(caplog):
    add_filter_to_all_loggers(FilterSensitiveInfo())

    log.exception('Houston, we have a %s", password')
    log.exception(unredacted_message)

    log_dump = dumps(caplog.messages)

    assert password not in log_dump
    assert [redacted_message, unredacted_message] == caplog.messages
