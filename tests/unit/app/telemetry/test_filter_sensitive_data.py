from app.util.logging.sensitive_data_filter import (
    filter_sensitive_data,
    filter_sensitive_string,
    sensitive_parameters,
)


def test_should_filter_out_sensitive_fields():
    test_input = {
        "name": "name",
        "password": "password",
        "apiSetupData": "apiSetupData",
        "clientId": "clientId",
        "tenantId": "tenantId",
        "tenant_id": "tenantId",
        "totpSecret": "totpSecret",
        "cloudStorageBucket": "cloudStorageBucket",
        "cloud_storage_bucket": "cloudStorageBucket",
        "configs": [
            {
                "source": "steam_sales",
                "credentials": {
                    "login": "login",
                    "password": "password",
                },
            },
            {
                "source": "playstation_sales",
                "credentials": {"clientId": "clientId", "clientSecret": "clientSecret"},
            },
        ],
        "safe": {
            "butNotReally": {"password": "password", "thisIsFine": True, "safe": 0}
        },
    }

    output = filter_sensitive_data(test_input)
    assert output == {
        "name": "name",
        "password": "[REDACTED]",
        "apiSetupData": "[REDACTED]",
        "clientId": "[REDACTED]",
        "tenantId": "[REDACTED]",
        "tenant_id": "[REDACTED]",
        "totpSecret": "[REDACTED]",
        "cloudStorageBucket": "[REDACTED]",
        "cloud_storage_bucket": "[REDACTED]",
        "configs": [
            {"source": "steam_sales", "credentials": "[REDACTED]"},
            {"source": "playstation_sales", "credentials": "[REDACTED]"},
        ],
        "safe": {
            "butNotReally": {
                "password": "[REDACTED]",
                "thisIsFine": True,
                "safe": 0,
            }
        },
    }


def test_should_filter_out_sensitive_fields_in_tuple_of_dicts():
    test_input = (
        {
            "name": "name",
            "password": "password",
        },
    )

    output = filter_sensitive_data(test_input)
    assert output == [
        {
            "name": "name",
            "password": "[REDACTED]",
        }
    ]


def test_should_handle_empty_input():
    output = filter_sensitive_data(None)
    assert output is None


def test_should_handle_dict_with_empty_list_input():
    output = filter_sensitive_data({"asd": []})
    assert output == {"asd": []}


def test_should_handle_empty_list_input():
    output = filter_sensitive_data([])
    assert output == []


def test_should_handle_mixed_input():
    test_input = {
        "foo": [
            "asd",
            "zxc",
            {"dfg": 1},
            {"password": "password", "safe": {"password": "password"}},
        ]
    }
    output = filter_sensitive_data(test_input)
    assert output == {
        "foo": [
            "asd",
            "zxc",
            {"dfg": 1},
            {"password": "[REDACTED]", "safe": {"password": "[REDACTED]"}},
        ]
    }


def test_should_filter_unhandled_type():
    output = filter_sensitive_data("asd")
    assert output == {"message": "Redacted because of unhandled type"}


def test_should_redact_touple():
    output = filter_sensitive_data(("a", "b"))
    assert output == ["a", "b"]


def test_should_redact_object_inside_of_list_within_list():
    output = filter_sensitive_data({"a": ["b", ["c", {"password": "password"}]]})
    assert output == {
        "a": [
            "b",
            [
                "c",
                {
                    "password": "[REDACTED]",
                },
            ],
        ]
    }


def test_filter_sensitive_string():
    for sensitive_param in sensitive_parameters:
        result = filter_sensitive_string(sensitive_param)
        assert sensitive_param not in result


def test_filter_sensitive_string_and_handle_whitelist():
    message = '{"yes":123, "output":True, "errorType": "INCORRECT_CREDENTIALS"}'
    result = filter_sensitive_string(message)
    assert message == result
