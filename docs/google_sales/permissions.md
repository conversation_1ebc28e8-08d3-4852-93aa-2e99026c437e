## Why do we require admin level privileges for the Google Sales scraper?

Initial tests proved that this was the most sure way to do it (the research was conducted before we found the 48h propagation cycle).
We have a confirmed example that at least one client set the scraper up with non admin permissions and scraped successfully (the contents of the reports still need to be verified).

## What's with the 48 hours wait period for obtaining proper credentials?

Through testing we found that Google takes a while to update the permissions for the service account.
It can take up to 48hours for the new permissions to propagate through the system.
