OnlinePrices(nsUid).prices(DataSource, priceType): {
  shape: sql_table

  priceId: str {constraint: primary_key}
  amount: str
  countryCode: str
  startDatetime: datestr
  endDatetime: datestr
  "...": ""
}

DiscountGroup.discounts(DiscountGroupId, DataSource, ContentsType): {
  shape: sql_table

  discountId: str {constraint: primary_key}
  discountValueId: str
  priceId: str {constraint: foreign_key}
  countryCode: str
  startDatetime: datestr
  endDatetime: datestr
  "...": ""
}

TargetTitles.contents.prices: {
  shape: sql_table

  priceId: str {constraint: primary_key}
  amount: str
  preOrderFlag: str
  countryCode: str
  startDatetime: datestr
  endDatetime: datestr
}
