# Nintendo Discounts - research

## Vocabulary

As Socrates stated almost 2,500 years ago,

> The beginning of wisdom is the definition of terms.

As the quote suggests, we first must agree about what things are called. To be precies, how _Nintendo Developers_ called certain things on their website and in their API.

### Data Source

When requesting data from the API, we need to specify a `dataSource`. It accepts values: `LIVE`, `QA`, and `both` (which results in a `LIVE` and `QA` data added to the response).

Our current understanding is, that in QA they are discounts with status `DiscountStatus.DRAFT`. Because currently we are interested only with discounts with status `DiscountStatus.PROCESS_COMPLETED`, in final CSV file we include only data from `LIVE` data source. In raw json files we include both.

### Discount

The row marked <span style="color:red;">**in red**</span> is a representation of a `discount`, therphore by this definition a row marked <span style="color:blue;">**in blue**</span> is a different `discount`.

![image](./images/discount.png)

### Discount Group

_Located on Discount Info page_

The group of _discounts_, is called a **_Discount Group_** (marked <span style="color:red;">**in red**</span>). One _Discount Group_ can only contains _discounts_ for one _Product_ (?).

![Discount Group](./images/discount_group.png)

There could be multiple _Discount Groups_ on a single _Discount Info_ page:

<center><span style="color:red;font-size:8pt">THIS SCREENSHOT IS FAKE/FABRICATED. REPLACE WITH A REAL ONE</span></center>

![Screenshot from 2023-11-22 00-35-16](./images/multiple_discount_groups.png)

#### discountGroupId

It looks like `discountGroupId` is an id of a group marked here by a blue rectangle:

![Screenshot from 2023-11-21 23-31-00](./images/discount_group_id.png)

To get value of `discountGroupId`, we need to scrape the page

```html
<input
  type="hidden"
  id="discountGroupIds"
  name="discountGroupIds"
  value="243107"
/>
```

What is really interesting, we need to check `discountGroupId**s**` (plural), what suggests that in certain circumstances, this could be for example _a comma separated list_(?).

### Discount Info Page / Submission

Discount Info page is a page with URL:

```url
https://ncms3.mng.nintendo.net/ncms3/temporaryDiscount/discountInfo?id=<submissionId>
```

From this page, we need to extract:

_DiscountGroupIds_, so we could:

make a GET request to:

API_URL=<https://smt.mng.nintendo.net/smt/rs/>

```python
{API_URL}/discount/term/get?
    token={access_token}&
    discountGroupId={group_id}&
    changeSetId=&
    locale=en&
    dataSource={DataSource.BOTH}
```

```python
{API_URL}/title/onlineprice/get?
    token={access_token}&
    nsUid={ns_uid}&
    locale=en&
    dataSource={DataSource.BOTH}
```

### nsUid (store sku)

## Relations

```mermaid
flowchart TD
    %% Style definitions
    classDef searchStyle fill:#e1f5fe,stroke:#01579b,color:#000
    classDef infoStyle fill:#e8f5e9,stroke:#2e7d32,color:#000
    classDef groupStyle fill:#fff3e0,stroke:#ef6c00,color:#000
    classDef discountStyle fill:#fce4ec,stroke:#c2185b,color:#000

    SearchPage[Discount Search Page]:::searchStyle -->|1 : 0..*| InfoPage

    subgraph InfoPage[Discount Info Page]
        direction TB
        SubmissionId
        Status
        ReleaseRegion[Release Region]
        subgraph Period[Discount Period]
            direction LR
            StartDate1[Start Date]
            EndDate1[End Date]
        end
        SaleName[Sale Name]
    end
    class InfoPage infoStyle

    InfoPage -->|1 : 1..*| DiscGroup

    subgraph DiscGroup[Discount Group]
        direction TB
        NsUid[nsUid]
        ProductCode[Product Code]
        Region[Region?]
    end
    class DiscGroup groupStyle

    DiscGroup -->|1 : 1..*| Disc

    subgraph Disc[Discount]
        direction TB
        Country[Country]
        Currency[Currency]
        subgraph Price[Price]
            direction TB
            StartDate2[Start Date]
            EndDate2[End Date]
            RegularPrice[Regular Price]
            DiscountedPrice[Discounted Price]
            DiscountedAmount[Discounted Amount]
            DiscountRate[Discount Rate]
            DiscountRateUser[Discount Rate for User]
        end
    end
    class Disc discountStyle
```

## Endpoints and their content

![Screenshot from 2023-11-22 03-04-21](./images/diagram_of_endpoints.d2.png)

## Questions

Is there a way to get all nsUid from API?

There is an endpoint, which after a POST request returns potentially useful data:

<https://ncms3.mng.nintendo.net/ncms3/temporaryDiscount/setup/ajaxContentSearchResult>

![Screenshot from 2023-11-22 02-47-24](./images/question_1.png)

Note: if usefull, we would need to make still at least 4 requests. One per each content type.

![Screenshot from 2023-11-22 02-49-35](./images/question_2.png)
