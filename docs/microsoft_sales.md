# Microsoft Sales Data Scraper

## Introduction

This scraper is designed to fetch Microsoft sales data using only API calls. The previous version operated in a hybrid mode, where a browser was used to fetch SKU lists, and the API was used to collect sales data. In the current version, we exclusively use the API. However, fetching the list of SKUs is still a bit tricky.

## Workflow Steps

![](./images/ms_revers_flow.png)

#### 1. Preparing Query (If Needed)

- If necessary, we prepare a query to run on the Microsoft site to generate a report.
- This report is used to obtain SKUs for a given customer.

#### 2. Checking Execution History

- We check the execution history for the last 7 days.
- The goal is to find the first **pending** or **completed** report within this period.

#### 3. Handling Missing Reports

- If no valid SKU report is available, we trigger a new one.

#### 4. Checking Report State

- Whether using a new or existing report, we verify its state.
- There is a possibility that an existing report is still **pending**, so we ensure it is **completed** before proceeding.

#### 5. Fetching SKUs

- Once a valid report is identified and completed, we fetch the SKUs from the report generated on the Microsoft site.

#### 6. Triggering a New Report (If Needed)

- If the last **scheduled** report is not from today, we trigger a new one **without waiting**.
- The result of this new report will be used in the next execution cycle.

### Goal

In the best case, the scraper never starts by generating a report, only scheduling one at the end for future use.

## Fetching SKUs

To obtain the list of SKUs, we rely on downloadable reports. Specifically, we generate sales data for all games for the past four years. The data is aggregated on a monthly basis. While this is limited in detail, it is sufficient for extracting the necessary SKUs.

### External documentation

To learn how we generate the SKU list, check those links:

- [Generating SKUs list](https://learn.microsoft.com/en-us/partner-center/insights/apps-games-make-your-first-api-call)
- [OpenAPI Specification](https://learn.microsoft.com/en-us/partner-center/insights/apps-games-make-your-first-api-call#api-documentation)
- [Detailed Fields Specification](https://learn.microsoft.com/en-us/partner-center/insights/downloads-hub-datasets)

## Fetching Sales Data

Once we have the list of SKUs, fetching the sales data is straightforward. We use the following API endpoint to retrieve sales data for a single SKU:

- [Sales Data API](https://learn.microsoft.com/en-us/windows/uwp/monetize/acquisitions-data)

The API allows us to fetch the complete sales history with no time limit. **Important note:** Although the documentation mentions pagination, it doesn't work correctly for this endpoint. When using pagination, only the first page is returned, and no `nextLink` field is included in the response.

## Alternative way to fetch SKU

There is another API for fetching SKUs, but it returns inconsistent results. For example, it provided a different SKU for Superhot, which prevented us from fetching sales data for it. Additionally, one of the SKUs was missing from the response. The API for fetching add-ons did not work correctly for InnerSloth. (I don't remember where this API was)

- [Get all apps](https://learn.microsoft.com/en-us/windows/uwp/monetize/get-all-apps)
- [Get all add-ons](https://learn.microsoft.com/en-us/windows/uwp/monetize/get-all-add-ons)

It would be wort checking if we will be able to solve problems with this API as it is much much much faster.

### PowerBI option

We also considered using PowerBI to extract the SKU list. But we didn't test that approach yet.

- [Documentation](https://learn.microsoft.com/en-us/partner-center/insights/apps-and-games-overview#windows-dev-center-content-pack-for-power-bi)

## Rate limiting

The API enforces rate limits when the limit is exceeded. It’s possible to pre-check this limit via the `X-RateLimit` header, but this header is not always returned. You can search for this header in VCR recordings.

## Troubleshooting: `MISSING_PERMISSIONS` Error

When the scraper fails with a `MISSING_PERMISSIONS` error, follow these steps:

### 1. Verify single SKU acquisition works

- Go to the **Microsoft Partner Center**.
- Find the SKU in the game's URL.
- Use the `analytics/acquisitions` endpoint to fetch sales data **for that specific SKU**.
  - If this request **fails**, it likely means that **API credentials are invalid**.
  - This typically happens when the customer didn’t provide a **globally unique name** when creating the API credentials.

#### ✅ Solution:

Ask the customer to generate new API credentials with a globally unique name.

Example naming format: `IndieBI - CUSTOMER_NAME - DATE` (or something random)

### 2. If single SKU acquisition works but full report still fails

- Enable the feature flag in the backoffice:

  ```
  microsoft-sales-sku-report-range-2y
  ```

- This flag changes the default date range for generating SKU reports from 4 years to **2 years**.

#### 💡 Context:

For some customers, reports for the last 4 years fail to generate, but 2-year reports work. This fix has been confirmed to work when single SKU sales data can be fetched, but the aggregated report fails.

#### Test alternative ranges manually

It might happen that 2 years will not work either. Try lowest range report manually modifying code or adding another feature flag.

- Try generating a report with a **shorter date range**, e.g. `LAST_72_HOURS`.
- Make sure to use a **matching aggregation level**, e.g. `Hourly` for short ranges.

Check [ranges documentation](https://learn.microsoft.com/en-us/partner-center/insights/apps-games-make-your-first-api-call#response-example) to find all available ranges and their corresponding aggregation levels.
