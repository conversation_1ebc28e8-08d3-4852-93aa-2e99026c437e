# pipeline types:
#
# MR - ran on a merge request event. Check the incoming MR before merging.
# Release - on a release branch, prepares a release candidate package.
# Nightly - scheduled run of integration tests (integration tests can fail without code changes because the portal APIs/sites have changed)
#         - longer static analysis, including security audits

workflow:
    rules:
        - if: '$CI_PIPELINE_SOURCE == "schedule"'
          variables:
              PIPELINE: Nightly
        - if: '$CI_COMMIT_BRANCH =~ /^\d+\.\d+\.\d+(-.*)?$/i'
          variables:
              PIPELINE: Release
        - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
          variables:
              PIPELINE: MR
        - if: '$CI_COMMIT_BRANCH && $CI_OPEN_MERGE_REQUESTS'
          when: never
        - if: '$CI_COMMIT_BRANCH'
          # for pushes to branches that are not covered by a MR, run an MR pipeline anyway
          variables:
              PIPELINE: MR

variables:
    NOTIFICATIONS_WEBHOOK_URL: "https://indiebisa.webhook.office.com/webhookb2/b6947d99-b6cd-42d1-a855-30c89fad141a@bb7887c7-c8cd-4f3d-b602-2e270710fdb9/IncomingWebhook/ed5d9e23304e47439fd8a5f522733673/94401c41-bbc4-4257-87a0-393108a2471a"
    POETRY_CACHE_DIR: "$CI_PROJECT_DIR/.poetry-cache"
    POETRY_VIRTUALENVS_IN_PROJECT: "true"
    POETRY_HTTP_BASIC_VCR_USERNAME: "gitlab-ci-token"
    POETRY_HTTP_BASIC_VCR_PASSWORD: "$CI_JOB_TOKEN"

include:
    - local: '/ci-cd/gitlab/install.yml'
    - local: '/ci-cd/gitlab/lint.yml'
    - local: '/ci-cd/gitlab/test.yml'
    - local: '/ci-cd/gitlab/package.yml'
    - local: '/ci-cd/gitlab/deploy.yml'
    - local: '/ci-cd/gitlab/report_if_failure.yml'

image: crindiebimain.azurecr.io/dpt/ci-cd/scrapers-py-python-3.11-poetry-chromium:prod

stages:
    - install
    - lint
    - test
    - package
    - deploy

default:
    tags:
        - dpt-azure
