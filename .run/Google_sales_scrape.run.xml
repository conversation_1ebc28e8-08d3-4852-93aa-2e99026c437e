<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Google_sales_scrape" type="PythonConfigurationType" factoryName="Python">
    <module name="scrapers-py" />
    <option name="INTERPRETER_OPTIONS" value="" />
    <option name="PARENT_ENVS" value="true" />
    <envs>
      <env name="DATA_DIR" value=".private" />
      <env name="GOOGLE_SALES_CREDENTIALS" value="{&quot;user&quot;:&quot;FAKE&quot;,&quot;password&quot;:&quot;N/A&quot;}" />
      <env name="PYTHONUNBUFFERED" value="1" />
      <env name="SCRAPE_FROM_DATE" value="2024-02-02" />
      <env name="SCRAPE_TO_DATE" value="2024-02-03" />
      <env name="TELEMETRY_PARAMS" value="{&quot;connectionString&quot;: &quot;InstrumentationKey=814196bf-6202-489b-a228-ada0a87b6b6f;IngestionEndpoint=https://westeurope-5.in.applicationinsights.azure.com/;LiveEndpoint=https://westeurope.livediagnostics.monitor.azure.com/&quot;,&quot;userEmail&quot;: &quot;<EMAIL>&quot;,&quot;developerMode&quot;: &quot;true&quot;,&quot;authenticatedUserId&quot;: &quot;-2&quot;}" />
    </envs>
    <option name="SDK_HOME" value="" />
    <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/scripts" />
    <option name="IS_MODULE_SDK" value="true" />
    <option name="ADD_CONTENT_ROOTS" value="true" />
    <option name="ADD_SOURCE_ROOTS" value="true" />
    <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
    <option name="SCRIPT_NAME" value="$PROJECT_DIR$/scripts/run.py" />
    <option name="PARAMETERS" value="scrape google_sales" />
    <option name="SHOW_COMMAND_LINE" value="false" />
    <option name="EMULATE_TERMINAL" value="false" />
    <option name="MODULE_MODE" value="false" />
    <option name="REDIRECT_INPUT" value="false" />
    <option name="INPUT_FILE" value="" />
    <method v="2" />
  </configuration>
</component>
