<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="Run VCR tests" type="tests" factoryName="Autodetect">
    <module name="scrapers-py" />
    <option name="INTERPRETER_OPTIONS" value="" />
    <option name="PARENT_ENVS" value="true" />
    <option name="SDK_HOME" value="$USER_HOME$/.cache/pypoetry/virtualenvs/scp-3529-wOtmeuL0-py3.11/bin/python" />
    <option name="SDK_NAME" value="Python 3.11 (scp-3529-wOtmeuL0-py3.11)" />
    <option name="WORKING_DIRECTORY" value="" />
    <option name="IS_MODULE_SDK" value="false" />
    <option name="ADD_CONTENT_ROOTS" value="true" />
    <option name="ADD_SOURCE_ROOTS" value="true" />
    <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
    <option name="_new_additionalArguments" value="&quot;&quot;" />
    <option name="_new_target" value="&quot;$PROJECT_DIR$/test/vcr&quot;" />
    <option name="_new_targetType" value="&quot;PATH&quot;" />
    <method v="2" />
  </configuration>
</component>
