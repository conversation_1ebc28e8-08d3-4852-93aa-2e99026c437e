{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Login (params from env)",
            "type": "debugpy",
            "request": "launch",
            "module": "app.main",
            "justMyCode": false,
            "args": [
                "login"
            ]
        },
        {
            "name": "Check-session (params from env)",
            "type": "debugpy",
            "request": "launch",
            "module": "app.main",
            "justMyCode": false,
            "args": [
                "check-session"
            ]
        },
        {
            "name": "check-session --source=<pick>",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/scripts/run.py",
            "args": [
                "check-session",
                "${input:source}"
            ],
            "envFile": "${workspaceFolder}/.env",
            "console": "integratedTerminal"
        },
        {
            "name": "login --source=<pick>",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/scripts/run.py",
            "args": [
                "login",
                "${input:source}"
            ],
            "envFile": "${workspaceFolder}/.env",
            "console": "integratedTerminal"
        },
        {
            "name": "scrape --source=<pick>",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/scripts/run.py",
            "args": [
                "scrape",
                "${input:source}"
            ],
            "envFile": "${workspaceFolder}/.env",
            "console": "integratedTerminal"
        },
        {
            "name": "Python: Current File",
            "type": "debugpy",
            "request": "launch",
            "program": "${file}",
            "console": "integratedTerminal",
            "justMyCode": true
        },
        {
            "name": "Nintendo Discounts: to_csv(<jsons>)",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/scripts/debug_csv_generation_with_jsons_files.py",
            "envFile": "${workspaceFolder}/.env",
            "console": "integratedTerminal"
        },
    ],
    "inputs": [
        {
            "id": "source",
            "description": "Pick a source",
            "type": "pickString",
            "options": [
                "app_store_sales",
                "epic_sales",
                "google_sales",
                "microsoft_sales",
                "nintendo_sales",
                "nintendo_discounts",
                "nintendo_wishlists",
            ],
        }
    ]
}
