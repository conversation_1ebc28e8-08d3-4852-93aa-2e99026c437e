{"$schema": "https://docs.renovatebot.com/renovate-schema.json", "extends": ["config:recommended", "docker:disable"], "packageRules": [{"groupName": "dev-dependencies", "matchDepTypes": ["dev"]}, {"groupName": "test-dependencies", "matchDepTypes": ["test"]}, {"matchPackageNames": ["pyinstaller"], "allowedVersions": "6.6.0"}], "prCreation": "immediate", "rebaseWhen": "behind-base-branch", "separateMinorPatch": true, "minimumReleaseAge": "7 days"}