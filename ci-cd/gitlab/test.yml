.test: &test
    stage: test
    interruptible: true
    coverage: '/(?i)total.*? (100(?:\.0+)?\%|[1-9]?\d(?:\.\d+)?\%)$/'
    artifacts:
        reports:
            coverage_report:
                coverage_format: cobertura
                path: coverage.xml
            junit: junit-coverage.xml


unit test:
    <<: *test
    script:
        - poetry run pytest -s tests/unit --cov --cov-report term --cov-report xml:coverage.xml --junitxml junit-coverage.xml


integration test:
    <<: *test
    before_script:
        # Create directories for the failure report
        - mkdir -p failures
        - mkdir -p .private/reports/$SOURCE
        - mkdir -p .private/dumps
        - touch failures/${SOURCE////_} # replace / with _ to create a file instead of a dir
    script:
        - poetry run pytest -s tests/integration/$SOURCE --cov --cov-report term --cov-report xml:coverage.xml --junitxml junit-coverage.xml
    after_script:
        # To avoid reporting successful runs
        - if [ "$CI_JOB_STATUS" == "success" ]; then rm failures/${SOURCE////_}; fi
    parallel:
        matrix:
            - SOURCE:
                  - app_store_sales
                  - google_sales
                  - microsoft_sales
                  - nintendo
    artifacts:
        when: always
        paths:
            - failures/
            - .private/dumps/
        reports:
            coverage_report:
                coverage_format: cobertura
                path: coverage.xml
            junit: junit-coverage.xml

vcr playback test:
    <<: *test
    script:
        - poetry run pytest -s tests/vcr --cov --cov-report term --cov-report xml:coverage.xml --junitxml junit-coverage.xml
