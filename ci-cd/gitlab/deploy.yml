.update-lse-file: &update-lse-file
    stage: deploy
    needs: ['upload package linux to azure', 'upload package windows to azure', 'upload package macos to azure']
    rules:
      - if: '$PIPELINE == "Release"'
        when: manual
    dependencies: []
    before_script:
        - SUPPORTED_SOURCES='["app_store_sales", "google_sales", "nintendo_sales","nintendo_wishlists", "nintendo_discounts", "microsoft_sales"]'
        - RELEASE_FULL_VERSION=$(./scripts/get-version.sh)
        - RELEASE_SHORT_VERSION=$(echo $RELEASE_FULL_VERSION | sed "s/-.*//g")
        - OVERRIDES=`echo $SUPPORTED_SOURCES | jq "map({\"scrapers\":\"$RELEASE_FULL_VERSION\",\"source\":.})"`
        - JQ_CMD=".cli.overrides = $OVERRIDES"

.update-dev-lse-file: &update-dev-lse-file
    <<: *update-lse-file
    after_script:
        - 'curl -f -X PUT --data-binary ''@dev_lse_version.json'' -H ''x-ms-blob-type'' --header ''x-ms-blob-type: BlockBlob'' --header ''Content-Type: application/json'' "$DEV_SCP_CONTAINER_URL/lse_version.json?$SCP_CONTAINER_SAS_TOKEN"'

.update-prod-lse-file: &update-prod-lse-file
    <<: *update-lse-file
    after_script:
        - 'curl -f -X PUT --data-binary ''@lse_version.json'' -H ''x-ms-blob-type'' --header ''x-ms-blob-type: BlockBlob'' --header ''Content-Type: application/json'' "$PRODUCTION_SCP_CONTAINER_URL/lse_version.json?$SCP_CONTAINER_SAS_TOKEN"'

update dev cli.overrides version:
    <<: *update-dev-lse-file
    script:
        - (curl -fsL "$DEV_SCP_CONTAINER_URL/lse_version.json" || echo '{}') | jq "$JQ_CMD" > dev_lse_version.json

update prod cli.overrides version:
    <<: *update-prod-lse-file
    script:
        - (curl -fsL "$PRODUCTION_SCP_CONTAINER_URL/lse_version.json" || echo '{}') | jq "$JQ_CMD" > lse_version.json

update all:
    <<: *update-prod-lse-file
    script:
        # CLI Dev
        - (curl -fsL "$DEV_SCP_CONTAINER_URL/lse_version.json" || echo '{}' ) > dev_lse_version.json
        - echo $(jq "$JQ_CMD" dev_lse_version.json) > dev_lse_version.json
        - 'curl -f -X PUT --data-binary ''@dev_lse_version.json'' -H ''x-ms-blob-type'' --header ''x-ms-blob-type: BlockBlob'' --header ''Content-Type: application/json'' "$DEV_SCP_CONTAINER_URL/lse_version.json?$SCP_CONTAINER_SAS_TOKEN"'
        # CLI Prod
        - (curl -fsL "$PRODUCTION_SCP_CONTAINER_URL/lse_version.json" || echo '{}' ) > lse_version.json
        - echo $(jq "$JQ_CMD" lse_version.json) > lse_version.json
