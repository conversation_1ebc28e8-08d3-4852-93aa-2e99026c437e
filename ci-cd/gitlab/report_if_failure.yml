report to teams if failure:
    needs: ['integration test']
    dependencies: ['integration test']
    script: |
        # Check if nonempty "failures" directory exists (created by integration tests on failure)
        if [ -d failures ] && [ "$(ls -A failures)" ]; then
          failedScrapers=`ls failures/`
          source ./ci-cd/scripts/integration-tests-notifications.sh "$NOTIFICATIONS_WEBHOOK_URL" "$failedScrapers"
        fi
    rules:
        - if: '$PIPELINE == "Nightly"'
          when: on_failure
        - when: never
