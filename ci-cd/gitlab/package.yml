package linux:
    stage: package
    needs: []
    script:
        - ./package/build.sh linux
    artifacts:
        paths:
            - dist/

package windows:
    stage: package
    needs: []
    tags:
        # see docs/gitlab-runners.md
        - indiebi
        - windows
    before_script:
        - $env:PATH += ";c:/python311;c:/python311/scripts"
        - pip install --force-reinstall -v "poetry==1.8.1"
    script:
        - bash package\build.sh win
    artifacts:
        paths:
            - dist/

package macos:
    stage: package
    needs: []
    tags:
        # see docs/gitlab-runners.md
        - macos-highsierra
    script:
        - ./package/build.sh macos
    artifacts:
        paths:
            - dist/

sign windows:
    stage: package
    needs: ['package windows']
    tags:
        - indiebi
        - linux
        - ev-cert
    dependencies: ['package windows']
    script:
        - ./package/sign/sign.sh 'dist/scrape.exe'
    artifacts:
        paths:
            - dist/
    rules:
        - if: '$PIPELINE == "Release"'

upload package linux to azure:
    stage: package
    needs: ['lints', 'unit test', 'integration test', 'package linux']
    script:
        - curl -f -X PUT --data-binary "@dist/scrape" -H "x-ms-blob-type" --header "x-ms-blob-type:BlockBlob" --header "Content-Type:application/octet-stream" "${PRODUCTION_SCP_CONTAINER_URL}/bin/scrapers-cli-linux-$(scripts/get-version.sh)?${SCP_CONTAINER_SAS_TOKEN}"
    rules:
        - if: '$PIPELINE == "Release"'

upload package windows to azure:
    stage: package
    needs: ['lints', 'unit test', 'integration test', 'package windows', 'sign windows']
    script:
        - curl -f -X PUT --data-binary "@dist/scrape.exe" -H "x-ms-blob-type" --header "x-ms-blob-type:BlockBlob" --header "Content-Type:application/octet-stream" "${PRODUCTION_SCP_CONTAINER_URL}/bin/scrapers-cli-win-$(scripts/get-version.sh).exe?${SCP_CONTAINER_SAS_TOKEN}"
    rules:
        - if: '$PIPELINE == "Release"'

upload package macos to azure:
    stage: package
    needs: ['lints', 'unit test', 'integration test', 'package macos']
    script:
        - curl -f -X PUT --data-binary "@dist/scrape" -H "x-ms-blob-type" --header "x-ms-blob-type:BlockBlob" --header "Content-Type:application/octet-stream" "${PRODUCTION_SCP_CONTAINER_URL}/bin/scrapers-cli-mac-$(scripts/get-version.sh)?${SCP_CONTAINER_SAS_TOKEN}"
    rules:
        - if: '$PIPELINE == "Release"'
